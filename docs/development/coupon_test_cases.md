# 优惠券 isReceived 字段测试用例

## 测试环境准备

### 测试数据
```sql
-- 测试优惠券数据
INSERT INTO inz_coupon (id, name, type, amount, min_amount, store_id, total_quantity, remaining_quantity, main_category, sub_category, start_time, end_time, description, status, create_time) VALUES
('test_coupon_001', '测试优惠券1', 1, 10.00, 50.00, 'store_001', 100, 95, '2', '1', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '测试用优惠券', 1, NOW()),
('test_coupon_002', '测试优惠券2', 2, 0.90, 100.00, 'store_002', 50, 50, '3', '2', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '测试用折扣券', 1, NOW()),
('test_coupon_003', '已领取优惠券', 1, 20.00, 80.00, 'store_001', 200, 199, '2', '1', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '用户已领取的优惠券', 1, NOW());

-- 测试用户优惠券数据（用户已领取test_coupon_003）
INSERT INTO inz_user_coupon (id, user_id, coupon_id, status, is_received, claim_time, create_time) VALUES
('test_user_coupon_001', '26cd17218464e1484b6d3a35a0d48fce', 'test_coupon_003', 0, 1, NOW(), NOW());
```

## 测试用例

### 1. 获取可用优惠券列表测试

#### 测试接口
```
GET /api/user/coupon/available?pageNo=1&pageSize=10
```

#### 预期结果
```json
{
  "success": true,
  "result": {
    "records": [
      {
        "id": "test_coupon_001",
        "name": "测试优惠券1",
        "isReceived": 0,  // 未领取
        "amount": 10.00,
        "storeName": "测试店铺1"
      },
      {
        "id": "test_coupon_002", 
        "name": "测试优惠券2",
        "isReceived": 0,  // 未领取
        "amount": 0.90,
        "storeName": "测试店铺2"
      },
      {
        "id": "test_coupon_003",
        "name": "已领取优惠券", 
        "isReceived": 1,  // 已领取
        "amount": 20.00,
        "storeName": "测试店铺1"
      }
    ]
  }
}
```

### 2. 获取我的优惠券列表测试

#### 测试接口
```
GET /api/user/coupon/my?pageNo=1&pageSize=10
```

#### 预期结果
```json
{
  "success": true,
  "result": {
    "records": [
      {
        "id": "test_coupon_003",
        "name": "已领取优惠券",
        "isReceived": 1,  // 我的优惠券都是已领取
        "status": 0,      // 未使用
        "amount": 20.00
      }
    ]
  }
}
```

### 3. 优惠券领取测试

#### 测试接口
```
POST /api/user/coupon/receive
Content-Type: application/json

{
  "couponIds": ["test_coupon_001"]
}
```

#### 预期结果
```json
{
  "success": true,
  "result": {
    "successCount": 1,
    "details": [
      {
        "success": true,
        "message": "领取优惠券成功"
      }
    ]
  }
}
```

#### 后续验证
再次调用可用优惠券列表，test_coupon_001 的 isReceived 应变为 1。

### 4. 重复领取测试

#### 测试接口
```
POST /api/user/coupon/receive
Content-Type: application/json

{
  "couponIds": ["test_coupon_003"]  // 已领取的优惠券
}
```

#### 预期结果
```json
{
  "success": true,
  "result": {
    "successCount": 0,
    "details": [
      {
        "success": false,
        "message": "您已领取过该优惠券"
      }
    ]
  }
}
```

### 5. 未登录用户测试

#### 测试步骤
1. 清除登录token
2. 调用可用优惠券列表接口

#### 预期结果
所有优惠券的 `isReceived` 字段都应为 `0`。

### 6. 店铺优惠券测试

#### 测试接口
```
GET /api/user/coupon/store/store_001/withStatus
```

#### 预期结果
```json
{
  "success": true,
  "result": [
    {
      "id": "test_coupon_001",
      "name": "测试优惠券1",
      "isReceived": 1,  // 如果已领取
      "storeId": "store_001",
      "storeName": "测试店铺1"
    },
    {
      "id": "test_coupon_003",
      "name": "已领取优惠券",
      "isReceived": 1,  // 已领取
      "storeId": "store_001", 
      "storeName": "测试店铺1"
    }
  ]
}
```

### 7. 兑换码测试

#### 测试接口
```
POST /api/user/coupon/redeem
Content-Type: application/json

{
  "codes": ["TEST_CODE_001"]
}
```

#### 预期结果
```json
{
  "success": true,
  "result": {
    "successCount": 1,
    "successList": [
      {
        "id": "redeemed_coupon_001",
        "name": "兑换券",
        "isReceived": 1,  // 兑换成功后应为已领取
        "amount": 15.00
      }
    ]
  }
}
```

## 自动化测试脚本

### Postman 测试集合

```javascript
// 测试前置脚本
pm.test("设置测试用户token", function () {
    pm.globals.set("user_token", "your_test_token_here");
});

// 测试1: 获取可用优惠券列表
pm.test("可用优惠券列表包含isReceived字段", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData.success).to.be.true;
    
    if (jsonData.result.records.length > 0) {
        jsonData.result.records.forEach(function(coupon) {
            pm.expect(coupon).to.have.property('isReceived');
            pm.expect(coupon.isReceived).to.be.oneOf([0, 1]);
        });
    }
});

// 测试2: 我的优惠券列表
pm.test("我的优惠券isReceived都为1", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData.success).to.be.true;
    
    if (jsonData.result.records.length > 0) {
        jsonData.result.records.forEach(function(coupon) {
            pm.expect(coupon.isReceived).to.equal(1);
        });
    }
});

// 测试3: 优惠券领取
pm.test("优惠券领取成功", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData.success).to.be.true;
    pm.expect(jsonData.result.successCount).to.be.greaterThan(0);
});
```

## 性能测试

### 并发领取测试
```bash
# 使用Apache Bench进行并发测试
ab -n 100 -c 10 -H "Authorization: Bearer your_token" \
   -p coupon_receive.json -T application/json \
   http://localhost:8080/api/user/coupon/receive
```

### 数据库查询性能
```sql
-- 检查查询执行计划
EXPLAIN SELECT 
    c.*,
    s.name as store_name,
    CASE WHEN uc.id IS NOT NULL THEN 1 ELSE 0 END as is_received
FROM inz_coupon c
LEFT JOIN inz_store s ON c.store_id = s.id
LEFT JOIN inz_user_coupon uc ON c.id = uc.coupon_id AND uc.user_id = 'test_user_id'
WHERE c.remaining_quantity > 0 AND NOW() BETWEEN c.start_time AND c.end_time;
```

## 测试检查清单

- [ ] 所有优惠券接口都返回 `isReceived` 字段
- [ ] `isReceived` 字段类型为 Integer (0或1)
- [ ] 未登录用户所有优惠券 `isReceived` 为 0
- [ ] 已登录用户根据实际情况显示 0 或 1
- [ ] 重复领取返回正确错误信息
- [ ] 兑换码成功后 `isReceived` 为 1
- [ ] 我的优惠券列表所有 `isReceived` 为 1
- [ ] 店铺优惠券正确显示领取状态
- [ ] 数据库查询性能正常
- [ ] 并发领取不会出现数据不一致

## 问题排查

### 常见问题
1. **isReceived 字段为 null**: 检查SQL查询是否正确添加了字段映射
2. **重复领取成功**: 检查数据库唯一约束是否生效
3. **性能问题**: 检查是否存在N+1查询问题
4. **数据不一致**: 检查事务处理是否正确

### 调试方法
1. 开启SQL日志查看实际执行的查询
2. 使用数据库监控工具检查查询性能
3. 添加详细的业务日志记录关键操作
4. 使用单元测试验证核心逻辑
