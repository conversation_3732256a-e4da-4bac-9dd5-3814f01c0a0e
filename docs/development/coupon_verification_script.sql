-- 优惠券 isReceived 字段验证脚本

-- 1. 检查优惠券表结构
DESCRIBE inz_coupon;

-- 2. 检查用户优惠券表结构
DESCRIBE inz_user_coupon;

-- 3. 验证测试数据
SELECT 
    c.id,
    c.name,
    c.remaining_quantity,
    c.status,
    c.start_time,
    c.end_time
FROM inz_coupon c 
WHERE c.status = 1 
  AND c.remaining_quantity > 0 
  AND NOW() BETWEEN c.start_time AND c.end_time
LIMIT 5;

-- 4. 验证用户优惠券关联数据
SELECT 
    uc.user_id,
    uc.coupon_id,
    uc.status,
    uc.is_received,
    c.name as coupon_name
FROM inz_user_coupon uc
JOIN inz_coupon c ON uc.coupon_id = c.id
WHERE uc.user_id = '26cd17218464e1484b6d3a35a0d48fce'
LIMIT 5;

-- 5. 模拟可用优惠券查询（包含 isReceived 字段）
SELECT
    c.*,
    s.name as store_name,
    CASE WHEN uc.id IS NOT NULL THEN 1 ELSE 0 END as is_received,
    CASE WHEN uc.id IS NOT NULL THEN true ELSE false END as claimed,
    CASE
        WHEN NOW() > c.end_time THEN 2
        WHEN uc.status IS NOT NULL THEN uc.status
        ELSE 0
    END as status,
    DATEDIFF(c.end_time, NOW()) as days_remaining
FROM inz_coupon c
LEFT JOIN inz_store s ON c.store_id = s.id
LEFT JOIN inz_user_coupon uc ON c.id = uc.coupon_id AND uc.user_id = '26cd17218464e1484b6d3a35a0d48fce'
WHERE
    c.remaining_quantity > 0
    AND NOW() BETWEEN c.start_time AND c.end_time
    AND c.status = 1
ORDER BY c.create_time DESC
LIMIT 10;

-- 6. 模拟我的优惠券查询
SELECT
    c.*,
    s.name as store_name,
    1 as is_received,
    true as claimed,
    CASE
        WHEN NOW() > c.end_time THEN 2
        WHEN uc.status IS NOT NULL THEN uc.status
        ELSE 0
    END as status,
    DATEDIFF(c.end_time, NOW()) as days_remaining
FROM inz_user_coupon uc
JOIN inz_coupon c ON uc.coupon_id = c.id
LEFT JOIN inz_store s ON c.store_id = s.id
WHERE
    uc.user_id = '26cd17218464e1484b6d3a35a0d48fce'
ORDER BY uc.create_time DESC
LIMIT 10;

-- 7. 检查唯一约束（防止重复领取）
SHOW INDEX FROM inz_user_coupon WHERE Key_name = 'uk_user_coupon';

-- 8. 验证数据类型
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'inz_user_coupon' 
  AND COLUMN_NAME IN ('is_received', 'status', 'user_id', 'coupon_id');

-- 9. 性能测试查询
EXPLAIN SELECT
    c.*,
    CASE WHEN uc.id IS NOT NULL THEN 1 ELSE 0 END as is_received
FROM inz_coupon c
LEFT JOIN inz_user_coupon uc ON c.id = uc.coupon_id AND uc.user_id = '26cd17218464e1484b6d3a35a0d48fce'
WHERE c.remaining_quantity > 0 AND c.status = 1;

-- 10. 清理测试数据（可选）
-- DELETE FROM inz_user_coupon WHERE user_id = 'test_user_id';
-- DELETE FROM inz_coupon WHERE id LIKE 'test_coupon_%';
