const server = Bun.serve({
  port: 3000,
  fetch(req, server) {
    // upgrade the request to a WebSocket
    if (server.upgrade(req)) {
      return; // do not return a Response
    }
    return new Response("Upgrade failed :(", { status: 500 });
  },
  websocket: {
    open(ws) {
      console.log("Client connected!");
    },
    message(ws, message) {
      console.log("Client sent:", message);
      // echo back the message
      ws.send(`Server received: ${message}`);
    },
    close(ws) {
      console.log("Client disconnected!");
    },
  },
});

console.log(`WebSocket server listening on port ${server.port}`); 