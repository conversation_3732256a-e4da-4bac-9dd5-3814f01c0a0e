-- inz_points_category 多条数据
INSERT INTO `inz_points_category` (`id`, `name`, `icon`, `sort`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
('cat01', '数码电子', '/icons/digital.png', 1, 1, 'admin', NOW(), NULL, NOW()),
('cat02', '生活用品', '/icons/daily.png', 2, 1, 'admin', NOW(), NULL, NOW()),
('cat03', '礼品卡券', '/icons/voucher.png', 3, 1, 'admin', NOW(), NULL, NOW()),
('cat04', '虚拟商品', '/icons/virtual.png', 4, 1, 'admin', NOW(), NULL, NOW());

-- inz_points_product 多条数据
INSERT INTO `inz_points_product` (
  `id`, `name`, `image`, `description`, `points`, `stock`, `exchange_count`, `limit_count`, `type`, `status`, `shelf_time`, `off_shelf_time`, `sort`, `is_limited`, `limited_number`, `product_id`, `tags`, `create_by`, `create_time`, `update_by`, `update_time`
) VALUES
('prod01', '蓝牙耳机', '/products/headphone.jpg', '高品质无线蓝牙耳机', 2000, 100, 10, 0, 1, 1, NOW(), NULL, 1, 0, NULL, NULL, '数码,蓝牙', 'admin', NOW(), NULL, NOW()),
('prod02', '保温杯', '/products/thermos.jpg', '304不锈钢保温杯', 1000, 200, 20, 0, 1, 1, NOW(), NULL, 2, 0, NULL, NULL, '生活,保温', 'admin', NOW(), NULL, NOW()),
('prod03', '50元京东卡', '/products/jd-card.jpg', '京东电子购物卡', 500, 1000, 100, 0, 3, 1, NOW(), NULL, 3, 0, NULL, NULL, '卡券,京东', 'admin', NOW(), NULL, NOW()),
('prod04', '腾讯视频月卡', '/products/video-card.jpg', '腾讯视频会员月卡', 800, 500, 50, 0, 2, 1, NOW(), NULL, 4, 0, NULL, NULL, '虚拟,视频', 'admin', NOW(), NULL, NOW());

-- inz_points_product_category 多条数据
INSERT INTO `inz_points_product_category` (`id`, `product_id`, `category_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
('ppc01', 'prod01', 'cat01', 'admin', NOW(), NULL, NOW()),
('ppc02', 'prod02', 'cat02', 'admin', NOW(), NULL, NOW()),
('ppc03', 'prod03', 'cat03', 'admin', NOW(), NULL, NOW()),
('ppc04', 'prod04', 'cat04', 'admin', NOW(), NULL, NOW());

-- inz_points_order 多条数据
INSERT INTO `inz_points_order` (
  `id`, `order_no`, `user_id`, `product_id`, `product_name`, `product_image`, `quantity`, `points`, `status`, `receiver_name`, `receiver_phone`, `receiver_address`, `tracking_no`, `express_company`, `ship_time`, `finish_time`, `remark`, `product_type`, `coupon_id`, `create_by`, `create_time`, `update_by`, `update_time`
) VALUES
('order01', 'PO202401010001', 'user001', 'prod01', '蓝牙耳机', '/products/headphone.jpg', 1, 2000, 0, '张三', '13800138001', '北京市朝阳区XX街道XX号', NULL, NULL, NULL, NULL, NULL, 1, NULL, 'admin', NOW(), NULL, NOW()),
('order02', 'PO202401010002', 'user002', 'prod02', '保温杯', '/products/thermos.jpg', 2, 2000, 1, '李四', '13800138002', '上海市浦东新区XX路XX号', 'YT1001002', '圆通快递', NOW(), NULL, NULL, 1, NULL, 'admin', NOW(), NULL, NOW()),
('order03', 'PO202401010003', 'user003', 'prod03', '50元京东卡', '/products/jd-card.jpg', 1, 500, 2, '王五', '13800138003', '广州市天河区XX路XX号', NULL, NULL, NULL, NOW(), NULL, 3, NULL, 'admin', NOW(), NULL, NOW()),
('order04', 'PO202401010004', 'user001', 'prod04', '腾讯视频月卡', '/products/video-card.jpg', 1, 800, 0, '赵六', '13800138004', '深圳市南山区XX路XX号', NULL, NULL, NULL, NULL, NULL, 2, NULL, 'admin', NOW(), NULL, NOW());

-- inz_points_record 多条数据
INSERT INTO `inz_points_record` (
  `id`, `user_id`, `points`, `type`, `source`, `order_id`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`
) VALUES
('rec01', 'user001', 2000, 2, 3, 'order01', '兑换蓝牙耳机消耗积分', 'admin', NOW(), NULL, NOW()),
('rec02', 'user002', 1000, 2, 3, 'order02', '兑换保温杯消耗积分', 'admin', NOW(), NULL, NOW()),
('rec03', 'user003', 500, 2, 3, 'order03', '兑换京东卡消耗积分', 'admin', NOW(), NULL, NOW()),
('rec04', 'user001', 800, 2, 3, 'order04', '兑换腾讯视频月卡消耗积分', 'admin', NOW(), NULL, NOW()),
('rec05', 'user001', 100, 1, 1, NULL, '每日签到获得积分', 'admin', NOW(), NULL, NOW()),
('rec06', 'user002', 200, 1, 4, NULL, '系统赠送积分', 'admin', NOW(), NULL, NOW()),
('rec07', 'user003', 300, 1, 5, NULL, '活动奖励积分', 'admin', NOW(), NULL, NOW());

-- inz_product 多条数据
INSERT INTO `inz_product` (
  `id`, `name`, `product_no`, `store_id`, `main_image`, `images`, `price`, `original_price`, `stock`, `sales`, `status`, `type`, `description`, `detail`, `specifications`, `category`, `tags`, `year`, `team`, `player`, `grade`, `rating`, `limited_number`, `has_signed`, `has_jersey_patch`, `card_type`, `freight_template_id`, `free_shipping`, `sort`, `create_by`, `create_time`, `update_by`, `update_time`
) VALUES
('p001', '蓝牙耳机', 'NO001', 'store01', '/products/headphone.jpg', '/products/headphone.jpg', 199.00, 299.00, 100, 10, 1, 1, '高品质无线蓝牙耳机', '详细介绍', NULL, '数码电子', '蓝牙,数码', '2024', '无', '无', NULL, 4.5, NULL, 0, 0, NULL, 1, 1, 'admin', NOW(), NULL, NOW()),
('p002', '保温杯', 'NO002', 'store02', '/products/thermos.jpg', '/products/thermos.jpg', 89.00, 129.00, 200, 20, 1, 1, '304不锈钢保温杯', '详细介绍', NULL, '生活用品', '保温,生活', '2024', '无', '无', NULL, 4.2, NULL, 0, 0, NULL, 2, 1, 'admin', NOW(), NULL, NOW()),
('p003', '50元京东卡', 'NO003', 'store03', '/products/jd-card.jpg', '/products/jd-card.jpg', 50.00, 50.00, 1000, 100, 1, 1, '京东电子购物卡', '详细介绍', NULL, '礼品卡券', '卡券,京东', '2024', '无', '无', NULL, 4.8, NULL, 0, 0, NULL, 3, 1, 'admin', NOW(), NULL, NOW()),
('p004', '腾讯视频月卡', 'NO004', 'store04', '/products/video-card.jpg', '/products/video-card.jpg', 80.00, 80.00, 500, 50, 1, 1, '腾讯视频会员月卡', '详细介绍', NULL, '虚拟商品', '虚拟,视频', '2024', '无', '无', NULL, 4.6, NULL, 0, 0, NULL, 4, 1, 'admin', NOW(), NULL, NOW());

-- inz_product_category 多条数据
INSERT INTO `inz_product_category` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `sys_org_code`, `name`, `parent_id`, `level`, `sort`, `status`) VALUES
('pc01', 'admin', NOW(), NULL, NOW(), NULL, '数码电子', NULL, 1, 1, 1),
('pc02', 'admin', NOW(), NULL, NOW(), NULL, '生活用品', NULL, 1, 2, 1),
('pc03', 'admin', NOW(), NULL, NOW(), NULL, '礼品卡券', NULL, 1, 3, 1),
('pc04', 'admin', NOW(), NULL, NOW(), NULL, '虚拟商品', NULL, 1, 4, 1);

-- inz_product_favorite 多条数据
INSERT INTO `inz_product_favorite` (`id`, `user_id`, `product_id`, `status`, `price`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
('fav01', 'user001', 'p001', 1, 199.00, 'admin', NOW(), NULL, NOW()),
('fav02', 'user002', 'p002', 1, 89.00, 'admin', NOW(), NULL, NOW()),
('fav03', 'user003', 'p003', 1, 50.00, 'admin', NOW(), NULL, NOW()),
('fav04', 'user001', 'p004', 1, 80.00, 'admin', NOW(), NULL, NOW()); 