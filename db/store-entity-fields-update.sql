-- ==========================================
-- Store实体类字段补充 SQL
-- 执行时间：2025-01-24
-- 目标：为前端Store表添加企业认证字段
-- ==========================================

-- 检查表是否存在
SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'inz_store';

-- 为inz_store表添加企业认证字段
ALTER TABLE `inz_store` ADD COLUMN `credit_code` varchar(50) COMMENT '统一社会信用代码' AFTER `business_license`;
ALTER TABLE `inz_store` ADD COLUMN `legal_person` varchar(100) COMMENT '法人代表' AFTER `credit_code`;
ALTER TABLE `inz_store` ADD COLUMN `business_license_img` varchar(500) COMMENT '营业执照图片' AFTER `legal_person`;
ALTER TABLE `inz_store` ADD COLUMN `id_card_front` varchar(500) COMMENT '身份证正面' AFTER `business_license_img`;
ALTER TABLE `inz_store` ADD COLUMN `id_card_back` varchar(500) COMMENT '身份证反面' AFTER `id_card_front`;

-- 创建索引优化查询
CREATE INDEX `idx_credit_code` ON `inz_store`(`credit_code`);

-- 验证字段添加结果
DESCRIBE `inz_store`;

-- ==========================================
-- 字段补充完成
-- 新增字段：5个企业认证字段
-- 新增索引：1个信用代码索引
-- ==========================================
