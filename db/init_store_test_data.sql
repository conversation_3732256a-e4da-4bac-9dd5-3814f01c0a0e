-- 清空表数据
DELETE FROM inz_store_feed;
DELETE FROM inz_store_follow;
DELETE FROM inz_store;

-- 插入店铺数据
INSERT INTO inz_store (id, create_time, user_id, name, logo, banner, description, announcement, business_license, contact_phone, province, city, district, address, status, followers_count, products_count, sales_count, rating) VALUES
('1', NOW(), 'admin', '球星卡世界', 'https://via.placeholder.com/150', 'https://via.placeholder.com/800x200', '专业销售各类球星卡，包括NBA、足球等体育明星卡片', '新年大促，全场8折！', 'license123', '***********', '广东省', '深圳市', '南山区', '科技园科兴科学园', 1, 0, 156, 1280, 4.8),
('2', NOW(), 'user1', '稀有卡片店', 'https://via.placeholder.com/150', 'https://via.placeholder.com/800x200', '专注于稀有和限量版卡片收藏与交易', '新到一批限量版球星签名卡', 'license456', '***********', '上海市', '上海市', '浦东新区', '陆家嘴金融贸易区', 1, 0, 89, 560, 4.9),
('3', NOW(), 'user2', '复古卡集市', 'https://via.placeholder.com/150', 'https://via.placeholder.com/800x200', '收集和销售各类复古运动卡片，主打80-90年代系列', '老卡新进，欢迎选购', 'license789', '13800138003', '北京市', '北京市', '朝阳区', '三里屯SOHO', 1, 0, 234, 890, 4.7);

-- 插入店铺动态数据
INSERT INTO inz_store_feed (id, create_time, store_id, type, title, content, images, related_id, status) VALUES
('1', NOW(), '1', 1, '新品上架：乔丹限量签名卡', '刚刚到货的乔丹亲笔签名限量版球星卡，全球限量100张，欢迎预订！', '["https://via.placeholder.com/300", "https://via.placeholder.com/300"]', NULL, 1),
('2', NOW(), '1', 2, '618大促活动开启', '618店铺大促，全场商品8折起，部分稀有卡片低至5折！', '["https://via.placeholder.com/300"]', NULL, 1),
('3', NOW(), '2', 1, '梅西世界杯金靴限定卡发售', '2022卡塔尔世界杯梅西金靴限定卡，现货发售中！', '["https://via.placeholder.com/300", "https://via.placeholder.com/300"]', NULL, 1),
('4', NOW(), '2', 3, '店铺升级公告', '本店即将升级为认证店铺，届时将提供更多优质服务！', NULL, NULL, 1),
('5', NOW(), '3', 1, '90年代NBA球星卡系列上新', '90年代NBA球星卡系列，包括乔丹、皮蓬、罗德曼等球星，限量发售！', '["https://via.placeholder.com/300"]', NULL, 1);

-- 插入一些关注数据（使用测试用户）
INSERT INTO inz_store_follow (id, create_time, user_id, store_id) VALUES
('1', NOW(), 'test_user1', '1'),
('2', NOW(), 'test_user2', '1'),
('3', NOW(), 'test_user3', '1'),
('4', NOW(), 'test_user1', '2'),
('5', NOW(), 'test_user2', '2'),
('6', NOW(), 'test_user1', '3');

-- 更新店铺的粉丝数
UPDATE inz_store SET followers_count = 3 WHERE id = '1';
UPDATE inz_store SET followers_count = 2 WHERE id = '2';
UPDATE inz_store SET followers_count = 1 WHERE id = '3'; 