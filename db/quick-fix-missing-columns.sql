-- ==========================================
-- 快速修复InzStore表缺失字段
-- 执行时间：2025-01-24
-- 目标：快速添加最关键的缺失字段，解决SQL错误
-- ==========================================

-- 快速添加所有缺失字段（一次性执行）
ALTER TABLE `inz_store` 
ADD COLUMN `shop_code` varchar(50) COMMENT '店铺编号',
ADD COLUMN `owner_name` varchar(100) COMMENT '店主姓名',
ADD COLUMN `owner_phone` varchar(20) COMMENT '店主手机号',
ADD COLUMN `owner_email` varchar(100) COMMENT '店主邮箱',
ADD COLUMN `legal_person` varchar(100) COMMENT '法人代表',
ADD COLUMN `credit_code` varchar(50) COMMENT '统一社会信用代码',
ADD COLUMN `business_license_img` varchar(500) COMMENT '营业执照图片',
ADD COLUMN `id_card_front` varchar(500) COMMENT '身份证正面',
ADD COLUMN `id_card_back` varchar(500) COMMENT '身份证反面',
ADD COLUMN `deposit` decimal(10,2) DEFAULT 0.00 COMMENT '保证金',
ADD COLUMN `balance` decimal(10,2) DEFAULT 0.00 COMMENT '账户余额',
ADD COLUMN `frozen_amount` decimal(10,2) DEFAULT 0.00 COMMENT '冻结金额',
ADD COLUMN `commission_rate` decimal(5,2) DEFAULT 5.00 COMMENT '佣金比例',
ADD COLUMN `month_sales` decimal(12,2) DEFAULT 0.00 COMMENT '月销售额',
ADD COLUMN `total_sales` decimal(15,2) DEFAULT 0.00 COMMENT '总销售额',
ADD COLUMN `audit_status` int(1) DEFAULT 0 COMMENT '审核状态',
ADD COLUMN `audit_remark` varchar(500) COMMENT '审核备注',
ADD COLUMN `audit_time` datetime COMMENT '审核时间',
ADD COLUMN `audit_by` varchar(50) COMMENT '审核人',
ADD COLUMN `open_time` datetime COMMENT '开店时间',
ADD COLUMN `close_time` datetime COMMENT '关店时间',
ADD COLUMN `close_reason` varchar(500) COMMENT '关店原因',
ADD COLUMN `business_start_time` varchar(10) DEFAULT '09:00' COMMENT '营业开始时间',
ADD COLUMN `business_end_time` varchar(10) DEFAULT '21:00' COMMENT '营业结束时间',
ADD COLUMN `is_recommend` int(1) DEFAULT 0 COMMENT '是否推荐',
ADD COLUMN `sort_order` int(10) DEFAULT 0 COMMENT '排序权重',
ADD COLUMN `remark` varchar(500) COMMENT '备注';

-- 为现有数据生成店铺编号
UPDATE `inz_store` SET `shop_code` = CONCAT('SHOP', LPAD(id, 8, '0')) WHERE `shop_code` IS NULL;

-- 验证修复结果
SELECT COUNT(*) as total_columns 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'inz_store';

-- ==========================================
-- 快速修复完成
-- 现在可以正常查询inz_store表了
-- ==========================================
