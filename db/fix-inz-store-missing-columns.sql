-- ==========================================
-- 修复InzStore表缺失字段 SQL
-- 执行时间：2025-01-24
-- 目标：为inz_store表添加实体类中定义的所有缺失字段
-- ==========================================

-- 1. 检查表结构
DESCRIBE `inz_store`;

-- 2. 添加第一阶段缺失的23个字段
-- 店铺编号和店主信息字段
ALTER TABLE `inz_store` ADD COLUMN `shop_code` varchar(50) COMMENT '店铺编号' AFTER `rating`;
ALTER TABLE `inz_store` ADD COLUMN `owner_name` varchar(100) COMMENT '店主姓名' AFTER `shop_code`;
ALTER TABLE `inz_store` ADD COLUMN `owner_phone` varchar(20) COMMENT '店主手机号' AFTER `owner_name`;
ALTER TABLE `inz_store` ADD COLUMN `owner_email` varchar(100) COMMENT '店主邮箱' AFTER `owner_phone`;

-- 企业认证信息字段
ALTER TABLE `inz_store` ADD COLUMN `legal_person` varchar(100) COMMENT '法人代表' AFTER `owner_email`;
ALTER TABLE `inz_store` ADD COLUMN `credit_code` varchar(50) COMMENT '统一社会信用代码' AFTER `legal_person`;
ALTER TABLE `inz_store` ADD COLUMN `business_license_img` varchar(500) COMMENT '营业执照图片' AFTER `credit_code`;
ALTER TABLE `inz_store` ADD COLUMN `id_card_front` varchar(500) COMMENT '身份证正面' AFTER `business_license_img`;
ALTER TABLE `inz_store` ADD COLUMN `id_card_back` varchar(500) COMMENT '身份证反面' AFTER `id_card_front`;

-- 财务管理字段
ALTER TABLE `inz_store` ADD COLUMN `deposit` decimal(10,2) DEFAULT 0.00 COMMENT '保证金' AFTER `id_card_back`;
ALTER TABLE `inz_store` ADD COLUMN `balance` decimal(10,2) DEFAULT 0.00 COMMENT '账户余额' AFTER `deposit`;
ALTER TABLE `inz_store` ADD COLUMN `frozen_amount` decimal(10,2) DEFAULT 0.00 COMMENT '冻结金额' AFTER `balance`;
ALTER TABLE `inz_store` ADD COLUMN `commission_rate` decimal(5,2) DEFAULT 0.00 COMMENT '佣金比例' AFTER `frozen_amount`;
ALTER TABLE `inz_store` ADD COLUMN `month_sales` decimal(12,2) DEFAULT 0.00 COMMENT '月销售额' AFTER `commission_rate`;
ALTER TABLE `inz_store` ADD COLUMN `total_sales` decimal(15,2) DEFAULT 0.00 COMMENT '总销售额' AFTER `month_sales`;

-- 审核流程字段
ALTER TABLE `inz_store` ADD COLUMN `audit_status` int(1) DEFAULT 0 COMMENT '审核状态(0-待审核,1-审核通过,2-审核拒绝)' AFTER `total_sales`;
ALTER TABLE `inz_store` ADD COLUMN `audit_remark` varchar(500) COMMENT '审核备注' AFTER `audit_status`;
ALTER TABLE `inz_store` ADD COLUMN `audit_time` datetime COMMENT '审核时间' AFTER `audit_remark`;
ALTER TABLE `inz_store` ADD COLUMN `audit_by` varchar(50) COMMENT '审核人' AFTER `audit_time`;

-- 营业时间管理字段
ALTER TABLE `inz_store` ADD COLUMN `open_time` datetime COMMENT '开店时间' AFTER `audit_by`;
ALTER TABLE `inz_store` ADD COLUMN `close_time` datetime COMMENT '关店时间' AFTER `open_time`;
ALTER TABLE `inz_store` ADD COLUMN `close_reason` varchar(500) COMMENT '关店原因' AFTER `close_time`;
ALTER TABLE `inz_store` ADD COLUMN `business_start_time` varchar(10) DEFAULT '09:00' COMMENT '营业开始时间' AFTER `close_reason`;
ALTER TABLE `inz_store` ADD COLUMN `business_end_time` varchar(10) DEFAULT '21:00' COMMENT '营业结束时间' AFTER `business_start_time`;

-- 推广管理字段
ALTER TABLE `inz_store` ADD COLUMN `is_recommend` int(1) DEFAULT 0 COMMENT '是否推荐(0-否,1-是)' AFTER `business_end_time`;
ALTER TABLE `inz_store` ADD COLUMN `sort_order` int(10) DEFAULT 0 COMMENT '排序权重' AFTER `is_recommend`;
ALTER TABLE `inz_store` ADD COLUMN `remark` varchar(500) COMMENT '备注' AFTER `sort_order`;

-- 3. 创建必要的索引
ALTER TABLE `inz_store` ADD UNIQUE INDEX `uk_shop_code` (`shop_code`);
ALTER TABLE `inz_store` ADD INDEX `idx_audit_status` (`audit_status`);
ALTER TABLE `inz_store` ADD INDEX `idx_owner_phone` (`owner_phone`);
ALTER TABLE `inz_store` ADD INDEX `idx_credit_code` (`credit_code`);
ALTER TABLE `inz_store` ADD INDEX `idx_is_recommend` (`is_recommend`);
ALTER TABLE `inz_store` ADD INDEX `idx_sort_order` (`sort_order`);

-- 4. 更新现有数据的默认值
UPDATE `inz_store` SET 
    `audit_status` = 1,
    `business_start_time` = '09:00',
    `business_end_time` = '21:00',
    `is_recommend` = 0,
    `sort_order` = 0,
    `deposit` = 0.00,
    `balance` = 0.00,
    `frozen_amount` = 0.00,
    `commission_rate` = 5.00,
    `month_sales` = 0.00,
    `total_sales` = 0.00
WHERE `audit_status` IS NULL;

-- 5. 生成店铺编号（为现有店铺生成编号）
UPDATE `inz_store` SET `shop_code` = CONCAT('SHOP', LPAD(id, 8, '0')) WHERE `shop_code` IS NULL OR `shop_code` = '';

-- 6. 同步店主信息（从用户表同步）
UPDATE `inz_store` s 
INNER JOIN `sys_user` u ON s.user_id = u.id 
SET 
    s.owner_name = u.realname,
    s.owner_phone = u.phone,
    s.owner_email = u.email
WHERE s.owner_name IS NULL OR s.owner_name = '';

-- 7. 验证字段添加结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'inz_store' 
AND COLUMN_NAME IN (
    'shop_code', 'owner_name', 'owner_phone', 'owner_email',
    'legal_person', 'credit_code', 'business_license_img', 'id_card_front', 'id_card_back',
    'deposit', 'balance', 'frozen_amount', 'commission_rate', 'month_sales', 'total_sales',
    'audit_status', 'audit_remark', 'audit_time', 'audit_by',
    'open_time', 'close_time', 'close_reason', 'business_start_time', 'business_end_time',
    'is_recommend', 'sort_order', 'remark'
)
ORDER BY ORDINAL_POSITION;

-- 8. 检查索引创建结果
SHOW INDEX FROM `inz_store` WHERE Key_name IN ('uk_shop_code', 'idx_audit_status', 'idx_owner_phone', 'idx_credit_code', 'idx_is_recommend', 'idx_sort_order');

-- 9. 统计现有数据
SELECT 
    COUNT(*) as total_stores,
    COUNT(shop_code) as stores_with_code,
    COUNT(owner_name) as stores_with_owner,
    COUNT(CASE WHEN audit_status = 0 THEN 1 END) as pending_audit,
    COUNT(CASE WHEN audit_status = 1 THEN 1 END) as approved,
    COUNT(CASE WHEN audit_status = 2 THEN 1 END) as rejected
FROM `inz_store`;

-- ==========================================
-- 字段同步完成验证
-- 预期结果：
-- 1. 新增23个字段全部添加成功
-- 2. 6个索引创建成功
-- 3. 现有数据默认值设置成功
-- 4. 店铺编号自动生成成功
-- ==========================================
