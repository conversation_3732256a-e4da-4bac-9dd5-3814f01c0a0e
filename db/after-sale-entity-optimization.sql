-- ==========================================
-- 售后实体类优化 SQL
-- 执行时间：2025-01-24
-- 目标：优化售后表字段，支持可配置原因和分类图片
-- ==========================================

-- 1. 为售后表添加新的原因字段
ALTER TABLE `inz_after_sale` ADD COLUMN `reason_code` int(2) COMMENT '售后原因编码' AFTER `type`;
ALTER TABLE `inz_after_sale` ADD COLUMN `reason_text` varchar(100) COMMENT '售后原因文本' AFTER `reason_code`;
ALTER TABLE `inz_after_sale` ADD COLUMN `custom_reason` varchar(200) COMMENT '自定义原因说明' AFTER `reason_text`;

-- 2. 重命名和新增说明字段
ALTER TABLE `inz_after_sale` CHANGE COLUMN `reason` `old_reason` varchar(255) COMMENT '旧售后原因(待删除)';
ALTER TABLE `inz_after_sale` CHANGE COLUMN `description` `additional_note` varchar(500) COMMENT '补充说明';
ALTER TABLE `inz_after_sale` ADD COLUMN `detail_description` text COMMENT '详细描述' AFTER `additional_note`;

-- 3. 重命名和新增图片字段
ALTER TABLE `inz_after_sale` CHANGE COLUMN `images` `user_images` text COMMENT '用户上传图片(问题凭证)';
ALTER TABLE `inz_after_sale` ADD COLUMN `product_images` text COMMENT '商品图片(商品状态)' AFTER `user_images`;
ALTER TABLE `inz_after_sale` ADD COLUMN `package_images` text COMMENT '包装图片(包装状态)' AFTER `product_images`;
ALTER TABLE `inz_after_sale` ADD COLUMN `other_images` text COMMENT '其他图片' AFTER `package_images`;

-- 4. 创建售后原因字典表
CREATE TABLE `inz_after_sale_reason` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `reason_code` int(2) NOT NULL COMMENT '原因编码',
  `reason_text` varchar(100) NOT NULL COMMENT '原因文本',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `sort_order` int(3) DEFAULT 0 COMMENT '排序权重',
  `is_active` int(1) DEFAULT 1 COMMENT '是否启用(0:禁用,1:启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_reason_code` (`reason_code`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='售后原因字典表';

-- 5. 插入默认售后原因数据
INSERT INTO `inz_after_sale_reason` (`id`, `reason_code`, `reason_text`, `icon`, `sort_order`, `is_active`) VALUES
('reason_001', 1, '不想要了', '😔', 1, 1),
('reason_002', 2, '商品有问题', '❌', 2, 1),
('reason_003', 3, '物流问题', '📦', 3, 1),
('reason_004', 4, '尺寸不合适', '📏', 4, 1),
('reason_005', 5, '颜色不符', '🎨', 5, 1),
('reason_006', 6, '质量问题', '⚠️', 6, 1),
('reason_007', 7, '发错商品', '🔄', 7, 1),
('reason_008', 8, '包装破损', '📦💥', 8, 1),
('reason_009', 9, '其他原因', '📝', 9, 1);

-- 6. 创建索引优化查询性能
CREATE INDEX `idx_after_sale_reason_code` ON `inz_after_sale`(`reason_code`);
CREATE INDEX `idx_after_sale_type_reason` ON `inz_after_sale`(`type`, `reason_code`);

-- 7. 数据迁移：将旧的reason字段数据迁移到新字段
UPDATE `inz_after_sale` SET 
    `reason_code` = CASE 
        WHEN `old_reason` LIKE '%不想要%' OR `old_reason` LIKE '%不需要%' THEN 1
        WHEN `old_reason` LIKE '%质量%' OR `old_reason` LIKE '%问题%' OR `old_reason` LIKE '%坏%' THEN 6
        WHEN `old_reason` LIKE '%物流%' OR `old_reason` LIKE '%快递%' OR `old_reason` LIKE '%配送%' THEN 3
        WHEN `old_reason` LIKE '%尺寸%' OR `old_reason` LIKE '%大小%' OR `old_reason` LIKE '%合适%' THEN 4
        WHEN `old_reason` LIKE '%颜色%' OR `old_reason` LIKE '%色差%' THEN 5
        WHEN `old_reason` LIKE '%发错%' OR `old_reason` LIKE '%错误%' THEN 7
        WHEN `old_reason` LIKE '%包装%' OR `old_reason` LIKE '%破损%' THEN 8
        ELSE 9
    END,
    `reason_text` = CASE 
        WHEN `old_reason` LIKE '%不想要%' OR `old_reason` LIKE '%不需要%' THEN '不想要了'
        WHEN `old_reason` LIKE '%质量%' OR `old_reason` LIKE '%问题%' OR `old_reason` LIKE '%坏%' THEN '质量问题'
        WHEN `old_reason` LIKE '%物流%' OR `old_reason` LIKE '%快递%' OR `old_reason` LIKE '%配送%' THEN '物流问题'
        WHEN `old_reason` LIKE '%尺寸%' OR `old_reason` LIKE '%大小%' OR `old_reason` LIKE '%合适%' THEN '尺寸不合适'
        WHEN `old_reason` LIKE '%颜色%' OR `old_reason` LIKE '%色差%' THEN '颜色不符'
        WHEN `old_reason` LIKE '%发错%' OR `old_reason` LIKE '%错误%' THEN '发错商品'
        WHEN `old_reason` LIKE '%包装%' OR `old_reason` LIKE '%破损%' THEN '包装破损'
        ELSE '其他原因'
    END,
    `custom_reason` = CASE 
        WHEN `reason_code` = 9 THEN `old_reason`
        ELSE NULL
    END
WHERE `old_reason` IS NOT NULL AND `old_reason` != '';

-- 8. 创建售后原因管理视图
CREATE OR REPLACE VIEW `v_after_sale_reason_stats` AS
SELECT 
    r.reason_code,
    r.reason_text,
    r.icon,
    COUNT(a.id) as usage_count,
    COUNT(CASE WHEN a.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as recent_usage,
    ROUND(COUNT(a.id) * 100.0 / (SELECT COUNT(*) FROM inz_after_sale), 2) as usage_percentage
FROM inz_after_sale_reason r
LEFT JOIN inz_after_sale a ON r.reason_code = a.reason_code
WHERE r.is_active = 1
GROUP BY r.reason_code, r.reason_text, r.icon
ORDER BY r.sort_order;

-- 9. 创建存储过程：获取热门售后原因
DELIMITER $$
CREATE PROCEDURE `sp_get_popular_after_sale_reasons`(
    IN p_limit INT DEFAULT 5
)
BEGIN
    SELECT 
        reason_code,
        reason_text,
        icon,
        usage_count,
        usage_percentage
    FROM v_after_sale_reason_stats
    WHERE usage_count > 0
    ORDER BY usage_count DESC
    LIMIT p_limit;
END$$
DELIMITER ;

-- 10. 创建触发器：自动填充原因文本
DELIMITER $$
CREATE TRIGGER `tr_after_sale_reason_text` 
BEFORE INSERT ON `inz_after_sale`
FOR EACH ROW
BEGIN
    DECLARE v_reason_text VARCHAR(100);
    
    -- 如果reason_code不为空但reason_text为空，自动填充
    IF NEW.reason_code IS NOT NULL AND (NEW.reason_text IS NULL OR NEW.reason_text = '') THEN
        SELECT reason_text INTO v_reason_text 
        FROM inz_after_sale_reason 
        WHERE reason_code = NEW.reason_code AND is_active = 1;
        
        IF v_reason_text IS NOT NULL THEN
            SET NEW.reason_text = v_reason_text;
        END IF;
    END IF;
END$$
DELIMITER ;

-- 11. 更新数据字典
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`) VALUES
('after_sale_reason_dict', '售后原因', 'after_sale_reason', '售后原因字典', 0, 'admin', NOW(), 'admin', NOW(), 0)
ON DUPLICATE KEY UPDATE `update_time` = NOW();

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
('after_sale_reason_1', 'after_sale_reason_dict', '不想要了', '1', '用户不想要商品', 1, 1, 'admin', NOW(), 'admin', NOW()),
('after_sale_reason_2', 'after_sale_reason_dict', '商品有问题', '2', '商品存在问题', 2, 1, 'admin', NOW(), 'admin', NOW()),
('after_sale_reason_3', 'after_sale_reason_dict', '物流问题', '3', '物流配送问题', 3, 1, 'admin', NOW(), 'admin', NOW()),
('after_sale_reason_4', 'after_sale_reason_dict', '尺寸不合适', '4', '商品尺寸不合适', 4, 1, 'admin', NOW(), 'admin', NOW()),
('after_sale_reason_5', 'after_sale_reason_dict', '颜色不符', '5', '商品颜色与描述不符', 5, 1, 'admin', NOW(), 'admin', NOW()),
('after_sale_reason_6', 'after_sale_reason_dict', '质量问题', '6', '商品质量问题', 6, 1, 'admin', NOW(), 'admin', NOW()),
('after_sale_reason_7', 'after_sale_reason_dict', '发错商品', '7', '发送了错误的商品', 7, 1, 'admin', NOW(), 'admin', NOW()),
('after_sale_reason_8', 'after_sale_reason_dict', '包装破损', '8', '商品包装破损', 8, 1, 'admin', NOW(), 'admin', NOW()),
('after_sale_reason_9', 'after_sale_reason_dict', '其他原因', '9', '其他原因', 9, 1, 'admin', NOW(), 'admin', NOW())
ON DUPLICATE KEY UPDATE `update_time` = NOW();

-- 12. 验证优化结果
SELECT 
    '字段优化检查' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'inz_after_sale' AND COLUMN_NAME = 'reason_code') 
        THEN '✅ 售后原因字段已优化'
        ELSE '❌ 售后原因字段未优化'
    END as status
UNION ALL
SELECT 
    '原因字典检查' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'inz_after_sale_reason') 
        THEN '✅ 售后原因字典表已创建'
        ELSE '❌ 售后原因字典表未创建'
    END as status
UNION ALL
SELECT 
    '图片字段检查' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'inz_after_sale' AND COLUMN_NAME = 'user_images') 
        THEN '✅ 图片字段已分类优化'
        ELSE '❌ 图片字段未分类优化'
    END as status
UNION ALL
SELECT 
    '数据迁移检查' as check_type,
    CONCAT('✅ 已迁移 ', COUNT(*), ' 条售后记录') as status
FROM inz_after_sale 
WHERE reason_code IS NOT NULL;

-- ==========================================
-- 售后实体类优化完成
-- 新增功能：
-- 1. 可配置的售后原因系统 (9个默认原因)
-- 2. 分类图片管理 (4种图片类型)
-- 3. 优化的字段命名 (更直观的用户体验)
-- 4. 数据迁移和兼容性处理
-- 5. 统计分析和管理功能
-- ==========================================
