-- ==========================================
-- 第二阶段：业务流程完善 SQL
-- 执行时间：2025-01-24
-- 目标：建立完整的店铺业务流程管理
-- ==========================================

-- 1. 创建审核记录表
CREATE TABLE `store_audit_record` (
    `id` varchar(32) NOT NULL COMMENT '主键',
    `store_id` varchar(32) NOT NULL COMMENT '店铺ID',
    `audit_status` int(1) NOT NULL COMMENT '审核状态(0-待审核,1-审核通过,2-审核拒绝)',
    `audit_remark` varchar(500) COMMENT '审核备注',
    `audit_by` varchar(50) COMMENT '审核人',
    `audit_time` datetime COMMENT '审核时间',
    `submit_time` datetime COMMENT '提交时间',
    `create_by` varchar(50) COMMENT '创建人',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(50) COMMENT '更新人',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_store_id` (`store_id`),
    KEY `idx_audit_status` (`audit_status`),
    KEY `idx_audit_time` (`audit_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺审核记录表';

-- 2. 创建财务流水表
CREATE TABLE `store_finance_record` (
    `id` varchar(32) NOT NULL COMMENT '主键',
    `store_id` varchar(32) NOT NULL COMMENT '店铺ID',
    `record_type` int(1) NOT NULL COMMENT '记录类型(1-收入,2-支出,3-冻结,4-解冻,5-保证金,6-佣金)',
    `amount` decimal(12,2) NOT NULL COMMENT '金额',
    `balance_before` decimal(12,2) COMMENT '变更前余额',
    `balance_after` decimal(12,2) COMMENT '变更后余额',
    `order_id` varchar(32) COMMENT '关联订单ID',
    `description` varchar(200) COMMENT '描述',
    `operator` varchar(50) COMMENT '操作人',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_store_id` (`store_id`),
    KEY `idx_record_type` (`record_type`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺财务流水表';

-- 3. 创建状态变更记录表
CREATE TABLE `store_status_record` (
    `id` varchar(32) NOT NULL COMMENT '主键',
    `store_id` varchar(32) NOT NULL COMMENT '店铺ID',
    `old_status` int(1) COMMENT '原状态',
    `new_status` int(1) NOT NULL COMMENT '新状态',
    `change_reason` varchar(200) COMMENT '变更原因',
    `operator` varchar(50) COMMENT '操作人',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_store_id` (`store_id`),
    KEY `idx_new_status` (`new_status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺状态变更记录表';

-- 4. 创建店铺统计表
CREATE TABLE `store_business_stats` (
    `id` varchar(32) NOT NULL COMMENT '主键',
    `store_id` varchar(32) NOT NULL COMMENT '店铺ID',
    `stat_date` date NOT NULL COMMENT '统计日期',
    `today_sales` decimal(12,2) DEFAULT 0.00 COMMENT '今日销售额',
    `month_sales` decimal(12,2) DEFAULT 0.00 COMMENT '月销售额',
    `total_sales` decimal(12,2) DEFAULT 0.00 COMMENT '总销售额',
    `today_orders` int(10) DEFAULT 0 COMMENT '今日订单数',
    `month_orders` int(10) DEFAULT 0 COMMENT '月订单数',
    `total_orders` int(10) DEFAULT 0 COMMENT '总订单数',
    `product_count` int(10) DEFAULT 0 COMMENT '商品总数',
    `on_sale_count` int(10) DEFAULT 0 COMMENT '在售商品数',
    `sold_out_count` int(10) DEFAULT 0 COMMENT '缺货商品数',
    `customer_count` int(10) DEFAULT 0 COMMENT '客户总数',
    `active_customers` int(10) DEFAULT 0 COMMENT '活跃客户数',
    `repurchase_rate` decimal(5,2) DEFAULT 0.00 COMMENT '复购率',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_store_date` (`store_id`, `stat_date`),
    KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺经营统计表';

-- 5. 添加第二阶段新字段到店铺表
ALTER TABLE `inz_store` ADD COLUMN `service_qq` varchar(20) COMMENT '客服QQ' AFTER `remark`;
ALTER TABLE `inz_store` ADD COLUMN `service_wechat` varchar(50) COMMENT '客服微信' AFTER `service_qq`;
ALTER TABLE `inz_store` ADD COLUMN `service_phone` varchar(20) COMMENT '客服电话' AFTER `service_wechat`;
ALTER TABLE `inz_store` ADD COLUMN `auto_reply_content` text COMMENT '自动回复内容' AFTER `service_phone`;
ALTER TABLE `inz_store` ADD COLUMN `auto_reply_enabled` int(1) DEFAULT 0 COMMENT '是否开启自动回复(0-否,1-是)' AFTER `auto_reply_content`;
ALTER TABLE `inz_store` ADD COLUMN `view_count` int(10) DEFAULT 0 COMMENT '浏览次数' AFTER `auto_reply_enabled`;
ALTER TABLE `inz_store` ADD COLUMN `follow_count` int(10) DEFAULT 0 COMMENT '关注数量' AFTER `view_count`;
ALTER TABLE `inz_store` ADD COLUMN `last_active_time` datetime COMMENT '最后活跃时间' AFTER `follow_count`;

-- 6. 创建索引优化查询性能
CREATE INDEX `idx_store_audit_status` ON `inz_store`(`audit_status`);
CREATE INDEX `idx_store_status` ON `inz_store`(`status`);
CREATE INDEX `idx_store_user_audit` ON `inz_store`(`user_id`, `audit_status`);
CREATE INDEX `idx_store_recommend_sort` ON `inz_store`(`is_recommend`, `sort_order`);

-- 7. 添加权限配置
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`) VALUES
('store_audit_manage', 'store_manage', '店铺审核管理', '/store/audit', 'store/StoreAuditList', 'StoreAuditList', NULL, 1, NULL, '1', 2, 0, 'audit', 1, 1, 0, 0, 0, '店铺审核管理', '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0);

INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`) VALUES
('store_audit_submit', 'store_audit_manage', '提交审核', NULL, NULL, NULL, NULL, 2, 'inz_store:audit:submit', '1', 1, 0, NULL, 1, 1, 0, 0, 0, '店铺提交审核', '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0),
('store_audit_approve', 'store_audit_manage', '审核通过', NULL, NULL, NULL, NULL, 2, 'inz_store:audit:approve', '1', 2, 0, NULL, 1, 1, 0, 0, 0, '店铺审核通过', '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0),
('store_audit_reject', 'store_audit_manage', '审核拒绝', NULL, NULL, NULL, NULL, 2, 'inz_store:audit:reject', '1', 3, 0, NULL, 1, 1, 0, 0, 0, '店铺审核拒绝', '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0),
('store_audit_batch', 'store_audit_manage', '批量审核', NULL, NULL, NULL, NULL, 2, 'inz_store:audit:batch', '1', 4, 0, NULL, 1, 1, 0, 0, 0, '店铺批量审核', '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0),
('store_audit_list', 'store_audit_manage', '审核列表', NULL, NULL, NULL, NULL, 2, 'inz_store:audit:list', '1', 5, 0, NULL, 1, 1, 0, 0, 0, '店铺审核列表', '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0);

-- 8. 更新数据字典
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
('finance_record_type_1', 'finance_record_type', '收入', '1', '财务收入记录', 1, 1, 'admin', NOW(), 'admin', NOW()),
('finance_record_type_2', 'finance_record_type', '支出', '2', '财务支出记录', 2, 1, 'admin', NOW(), 'admin', NOW()),
('finance_record_type_3', 'finance_record_type', '冻结', '3', '资金冻结记录', 3, 1, 'admin', NOW(), 'admin', NOW()),
('finance_record_type_4', 'finance_record_type', '解冻', '4', '资金解冻记录', 4, 1, 'admin', NOW(), 'admin', NOW()),
('finance_record_type_5', 'finance_record_type', '保证金', '5', '保证金记录', 5, 1, 'admin', NOW(), 'admin', NOW()),
('finance_record_type_6', 'finance_record_type', '佣金', '6', '佣金记录', 6, 1, 'admin', NOW(), 'admin', NOW());

-- 9. 初始化现有店铺的统计数据
INSERT INTO `store_business_stats` (`id`, `store_id`, `stat_date`, `today_sales`, `month_sales`, `total_sales`)
SELECT 
    CONCAT('stats_', s.id, '_', DATE_FORMAT(NOW(), '%Y%m%d')) as id,
    s.id as store_id,
    CURDATE() as stat_date,
    COALESCE(s.month_sales, 0.00) as today_sales,
    COALESCE(s.month_sales, 0.00) as month_sales,
    COALESCE(s.total_sales, 0.00) as total_sales
FROM `inz_store` s
WHERE s.id IS NOT NULL;

-- 10. 创建定时任务配置
INSERT INTO `sys_quartz_job` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `job_class_name`, `cron_expression`, `parameter`, `description`, `status`) VALUES
('store_stats_daily_job', 'admin', NOW(), 'admin', NOW(), 'org.jeecg.modules.inz_store.job.StoreStatsDailyJob', '0 0 1 * * ?', NULL, '店铺每日统计任务', 1);

-- 11. 更新现有店铺的默认值
UPDATE `inz_store` SET 
    `auto_reply_enabled` = 0,
    `view_count` = 0,
    `follow_count` = 0,
    `last_active_time` = NOW()
WHERE `auto_reply_enabled` IS NULL;

-- 12. 创建触发器，自动记录状态变更
DELIMITER $$
CREATE TRIGGER `tr_store_status_change` 
AFTER UPDATE ON `inz_store`
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status OR OLD.audit_status != NEW.audit_status THEN
        INSERT INTO `store_status_record` (
            `id`, `store_id`, `old_status`, `new_status`, 
            `change_reason`, `operator`, `create_time`
        ) VALUES (
            CONCAT('status_', NEW.id, '_', UNIX_TIMESTAMP(NOW())),
            NEW.id,
            OLD.status,
            NEW.status,
            CASE 
                WHEN OLD.audit_status != NEW.audit_status THEN 
                    CONCAT('审核状态变更: ', OLD.audit_status, ' -> ', NEW.audit_status)
                ELSE 
                    CONCAT('营业状态变更: ', OLD.status, ' -> ', NEW.status)
            END,
            NEW.update_by,
            NOW()
        );
    END IF;
END$$
DELIMITER ;

-- ==========================================
-- 第二阶段业务流程完善完成
-- 新增表：4个
-- 新增字段：8个
-- 新增权限：5个
-- 核心功能：审核管理、财务管理、状态管理、统计分析
-- ==========================================
