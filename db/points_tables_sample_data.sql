-- 插入积分商品分类数据
INSERT INTO `inz_points_category` (`id`, `name`, `description`, `icon`, `sort_order`, `status`, `create_time`, `update_time`) VALUES
('1', '数码电子', '各类数码电子产品', '/icons/digital.png', 1, 1, NOW(), NOW()),
('2', '生活用品', '日常生活用品', '/icons/daily.png', 2, 1, NOW(), NOW()),
('3', '礼品卡券', '各类礼品卡和优惠券', '/icons/voucher.png', 3, 1, NOW(), NOW()),
('4', '虚拟商品', '游戏点卡等虚拟商品', '/icons/virtual.png', 4, 1, NOW(), NOW());

-- 插入积分商品数据
INSERT INTO `inz_points_product` (`id`, `category_id`, `name`, `description`, `image`, `points_price`, `original_price`, `stock`, `sales`, `status`, `start_time`, `end_time`, `create_time`, `update_time`) VALUES
('1', '1', '蓝牙耳机', '高品质无线蓝牙耳机', '/products/headphone.jpg', 2000, 199.00, 100, 50, 1, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), NOW(), NOW()),
('2', '2', '保温杯', '304不锈钢保温杯', '/products/thermos.jpg', 1000, 89.00, 200, 30, 1, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), NOW(), NOW()),
('3', '3', '50元京东卡', '京东电子购物卡', '/products/jd-card.jpg', 500, 50.00, 1000, 200, 1, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), NOW(), NOW()),
('4', '4', '腾讯视频月卡', '腾讯视频会员月卡', '/products/video-card.jpg', 800, 80.00, 500, 100, 1, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), NOW(), NOW());

-- 插入用户积分账户数据
INSERT INTO `inz_user_points` (`id`, `user_id`, `points_balance`, `total_points`, `used_points`, `frozen_points`, `last_update_time`, `create_time`, `update_time`) VALUES
('1', 'user001', 5000, 8000, 3000, 0, NOW(), NOW(), NOW()),
('2', 'user002', 3000, 4000, 1000, 0, NOW(), NOW(), NOW()),
('3', 'user003', 10000, 12000, 2000, 500, NOW(), NOW(), NOW());

-- 插入积分订单数据
INSERT INTO `inz_points_order` (`id`, `order_no`, `user_id`, `product_id`, `product_name`, `points_price`, `quantity`, `total_points`, `status`, `receiver_name`, `receiver_phone`, `receiver_address`, `tracking_no`, `tracking_company`, `create_time`, `update_time`) VALUES
('1', 'PO202401010001', 'user001', '1', '蓝牙耳机', 2000, 1, 2000, 2, '张三', '13800138001', '北京市朝阳区XX街道XX号', 'SF1001001', '顺丰快递', NOW(), NOW()),
('2', 'PO202401010002', 'user002', '2', '保温杯', 1000, 1, 1000, 1, '李四', '13800138002', '上海市浦东新区XX路XX号', 'YT1001002', '圆通快递', NOW(), NOW()),
('3', 'PO202401010003', 'user003', '3', '50元京东卡', 500, 2, 1000, 0, '王五', '13800138003', '广州市天河区XX路XX号', NULL, NULL, NOW(), NOW());

-- 插入积分记录数据
INSERT INTO `inz_points_record` (`id`, `user_id`, `points`, `type`, `source`, `source_id`, `description`, `before_points`, `after_points`, `expire_time`, `create_time`, `update_time`) VALUES
-- 用户1的积分记录
('1', 'user001', 5000, 1, 'sign', NULL, '每日签到奖励', 0, 5000, DATE_ADD(NOW(), INTERVAL 1 YEAR), NOW(), NOW()),
('2', 'user001', -2000, 2, 'order', 'PO202401010001', '购买蓝牙耳机', 5000, 3000, NULL, NOW(), NOW()),

-- 用户2的积分记录
('3', 'user002', 3000, 1, 'order', 'ORDER001', '购物获得积分', 0, 3000, DATE_ADD(NOW(), INTERVAL 1 YEAR), NOW(), NOW()),
('4', 'user002', -1000, 2, 'order', 'PO202401010002', '购买保温杯', 3000, 2000, NULL, NOW(), NOW()),

-- 用户3的积分记录
('5', 'user003', 10000, 1, 'admin', NULL, '管理员奖励', 0, 10000, DATE_ADD(NOW(), INTERVAL 1 YEAR), NOW(), NOW()),
('6', 'user003', -1000, 2, 'order', 'PO202401010003', '购买京东卡', 10000, 9000, NULL, NOW(), NOW()),
('7', 'user003', 500, 4, 'system', NULL, '积分冻结', 9000, 8500, NULL, NOW(), NOW());

-- 添加一些额外的积分获取记录
INSERT INTO `inz_points_record` (`id`, `user_id`, `points`, `type`, `source`, `source_id`, `description`, `before_points`, `after_points`, `expire_time`, `create_time`, `update_time`) VALUES
('8', 'user001', 100, 1, 'sign', NULL, '连续签到奖励', 3000, 3100, DATE_ADD(NOW(), INTERVAL 1 YEAR), NOW(), NOW()),
('9', 'user002', 200, 1, 'sign', NULL, '连续签到奖励', 2000, 2200, DATE_ADD(NOW(), INTERVAL 1 YEAR), NOW(), NOW()),
('10', 'user003', 300, 1, 'sign', NULL, '连续签到奖励', 8500, 8800, DATE_ADD(NOW(), INTERVAL 1 YEAR), NOW(), NOW()); 