-- Sample data for user with ID: 26cd17218464e1484b6d3a35a0d48fce

-- 1. Regular Orders and Order Items
INSERT INTO `inz_order` (`id`, `order_no`, `user_id`, `store_id`, `total_amount`, `pay_amount`, `discount_amount`, 
    `freight_amount`, `status`, `pay_time`, `pay_type`, `trade_no`, `receiver_name`, `receiver_phone`, 
    `receiver_address`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
-- 已完成的订单
('ord001', 'KL2024061301', '26cd17218464e1484b6d3a35a0d48fce', 'store001', 299.00, 289.00, 20.00, 10.00, 3, 
    '2024-06-13 10:30:00', 1, 'ALI123456789', '张三', '13800138001', '北京市朝阳区某某街道1号', '无', 
    '2024-06-13 10:25:00', 'system', '2024-06-13 15:00:00', 'system'),
-- 待收货的订单
('ord002', 'KL2024061302', '26cd17218464e1484b6d3a35a0d48fce', 'store002', 199.00, 199.00, 0.00, 0.00, 2, 
    '2024-06-13 14:20:00', 2, 'WX987654321', '张三', '13800138001', '北京市朝阳区某某街道1号', '请尽快发货', 
    '2024-06-13 14:15:00', 'system', '2024-06-13 14:20:00', 'system'),
-- 待付款的订单
('ord003', 'KL2024061303', '26cd17218464e1484b6d3a35a0d48fce', 'store001', 399.00, 389.00, 20.00, 10.00, 0, 
    NULL, NULL, NULL, '张三', '13800138001', '北京市朝阳区某某街道1号', NULL, 
    '2024-06-13 16:00:00', 'system', '2024-06-13 16:00:00', 'system');

INSERT INTO `inz_order_item` (`id`, `order_id`, `product_id`, `product_name`, `product_image`, 
    `price`, `quantity`, `total_amount`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
-- 订单1的商品
('item001', 'ord001', 'prod001', '高级休闲鞋', 'shoes001.jpg', 299.00, 1, 299.00, 
    '2024-06-13 10:25:00', 'system', '2024-06-13 10:25:00', 'system'),
-- 订单2的商品
('item002', 'ord002', 'prod002', '运动T恤', 'tshirt001.jpg', 99.50, 2, 199.00,
    '2024-06-13 14:15:00', 'system', '2024-06-13 14:15:00', 'system'),
-- 订单3的商品
('item003', 'ord003', 'prod003', '智能手表', 'watch001.jpg', 399.00, 1, 399.00,
    '2024-06-13 16:00:00', 'system', '2024-06-13 16:00:00', 'system');

-- 2. User Points
INSERT INTO `inz_user_points` (`id`, `user_id`, `total_points`, `available_points`, `used_points`, 
    `frozen_points`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
('up001', '26cd17218464e1484b6d3a35a0d48fce', 1500, 1200, 200, 100,
    '2024-06-13 10:00:00', 'system', '2024-06-13 16:00:00', 'system');

-- 3. Points Records
INSERT INTO `inz_points_record` (`id`, `user_id`, `points`, `type`, `source`, `order_id`, 
    `remark`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
-- 签到获得积分
('pr001', '26cd17218464e1484b6d3a35a0d48fce', 10, 1, 1, NULL,
    '每日签到奖励', '2024-06-13 09:00:00', 'system', '2024-06-13 09:00:00', 'system'),
-- 购物获得积分
('pr002', '26cd17218464e1484b6d3a35a0d48fce', 290, 1, 2, 'ord001',
    '购物订单KL2024061301获得积分', '2024-06-13 15:00:00', 'system', '2024-06-13 15:00:00', 'system'),
-- 兑换消耗积分
('pr003', '26cd17218464e1484b6d3a35a0d48fce', 200, 2, 3, NULL,
    '兑换优惠券', '2024-06-13 11:00:00', 'system', '2024-06-13 11:00:00', 'system'),
-- 系统赠送积分
('pr004', '26cd17218464e1484b6d3a35a0d48fce', 1000, 1, 4, NULL,
    '新用户注册奖励', '2024-06-13 10:00:00', 'system', '2024-06-13 10:00:00', 'system'),
-- 活动奖励积分
('pr005', '26cd17218464e1484b6d3a35a0d48fce', 400, 1, 5, NULL,
    '618购物节活动奖励', '2024-06-13 12:00:00', 'system', '2024-06-13 12:00:00', 'system');

-- 4. Points Orders
INSERT INTO `inz_points_order` (`id`, `order_no`, `user_id`, `product_id`, `product_name`, 
    `product_image`, `quantity`, `points`, `status`, `receiver_name`, `receiver_phone`, 
    `receiver_address`, `tracking_no`, `express_company`, `ship_time`, `finish_time`, 
    `remark`, `product_type`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
-- 已完成的积分订单（实物商品）
('po001', 'PO2024061301', '26cd17218464e1484b6d3a35a0d48fce', 'pp001', '便携式蓝牙音箱', 
    'speaker001.jpg', 1, 100, 2, '张三', '13800138001', '北京市朝阳区某某街道1号',
    'SF123456789', '顺丰快递', '2024-06-13 14:00:00', '2024-06-13 16:00:00',
    '无', 1, '2024-06-13 13:00:00', 'system', '2024-06-13 16:00:00', 'system'),
-- 待发货的积分订单（虚拟商品）
('po002', 'PO2024061302', '26cd17218464e1484b6d3a35a0d48fce', 'pp002', '视频会员月卡',
    'vip001.jpg', 1, 100, 0, '张三', '13800138001', '北京市朝阳区某某街道1号',
    NULL, NULL, NULL, NULL, '虚拟商品', 2, '2024-06-13 15:30:00', 'system', 
    '2024-06-13 15:30:00', 'system'); 