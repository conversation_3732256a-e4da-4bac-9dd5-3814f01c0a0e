-- 备份当前表结构
CREATE TABLE IF NOT EXISTS `inz_order_backup` LIKE `inz_order`;
INSERT INTO `inz_order_backup` SELECT * FROM `inz_order`;

-- 添加新字段到订单表
ALTER TABLE `inz_order`
ADD COLUMN `order_type` tinyint(1) DEFAULT 1 COMMENT '订单类型(1:金钱订单,2:积分订单,3:混合支付)' AFTER `store_id`,
ADD COLUMN `point_amount` int(11) DEFAULT 0 COMMENT '使用的积分数量' AFTER `pay_price`,
ADD COLUMN `express_company` varchar(50) DEFAULT NULL COMMENT '物流公司' AFTER `tracking_no`,
ADD COLUMN `express_code` varchar(50) DEFAULT NULL COMMENT '物流公司编码' AFTER `express_company`,
ADD COLUMN `ship_time` datetime DEFAULT NULL COMMENT '发货时间' AFTER `express_code`,
ADD COLUMN `finish_time` datetime DEFAULT NULL COMMENT '完成时间' AFTER `ship_time`;

-- 修正字段名称
ALTER TABLE `inz_order` 
CHANGE COLUMN IF EXISTS `refound_status` `refund_status` int(10) DEFAULT 1 COMMENT '退款状态(1-未操作退款,2-用户已提交，未退款,3-退款中,4-退款成功,5-退款失败,6-退款驳回)';

-- 添加索引
ALTER TABLE `inz_order`
ADD INDEX `idx_order_type` (`order_type`),
ADD INDEX `idx_tracking_no` (`tracking_no`),
ADD INDEX `idx_ship_time` (`ship_time`),
ADD INDEX `idx_finish_time` (`finish_time`);

-- 将积分订单数据迁移到统一订单表
-- 注意：执行此部分前请确保已经完成了积分订单表的备份
INSERT INTO `inz_order` (
    `id`, `order_no`, `user_id`, `store_id`, `order_type`, 
    `total_price`, `pay_price`, `point_amount`, `discount_amount`, `freight_amount`,
    `order_status`, `pay_time`, `receiver_name`, `receiver_phone`, `receiver_address`,
    `tracking_no`, `express_company`, `ship_time`, `finish_time`,
    `remark`, `refund_status`, `refund_time`, `create_by`, `create_time`, `update_by`, `update_time`
)
SELECT 
    `id`, `order_no`, `user_id`, NULL AS `store_id`, 2 AS `order_type`, 
    0 AS `total_price`, 0 AS `pay_price`, `points` AS `point_amount`, 0 AS `discount_amount`, 0 AS `freight_amount`,
    -- 状态映射: 0:待发货->2, 1:待收货->3, 2:已完成->4, 3:已取消->5
    CASE 
        WHEN `status` = 0 THEN 2 -- 待发货 -> 已支付待发货
        WHEN `status` = 1 THEN 3 -- 待收货 -> 已发货待收货
        WHEN `status` = 2 THEN 4 -- 已完成 -> 已完成
        WHEN `status` = 3 THEN 5 -- 已取消 -> 已取消
        ELSE 1
    END AS `order_status`,
    `create_time` AS `pay_time`, -- 积分订单创建即支付
    `receiver_name`, `receiver_phone`, `receiver_address`,
    `tracking_no`, `express_company`, NULL AS `ship_time`, `finish_time`,
    `remark`, `refund_status`, `refund_time`, `create_by`, `create_time`, `update_by`, `update_time`
FROM `inz_points_order`;

-- 迁移积分订单商品数据到订单商品表
-- 注意：需要根据实际情况调整字段
INSERT INTO `inz_order_item` (
    `id`, `order_id`, `product_id`, `product_name`, `product_image`, 
    `price`, `quantity`, `total_amount`, `create_by`, `create_time`
)
SELECT 
    UUID() AS `id`, `id` AS `order_id`, `product_id`, `product_name`, `product_image`,
    0 AS `price`, `quantity`, 0 AS `total_amount`, `create_by`, `create_time`
FROM `inz_points_order`;

-- 注释：
-- 1. 执行此脚本前请确保已备份数据库
-- 2. 建议分步执行，每步都验证结果
-- 3. 订单状态和类型变更可能影响现有业务逻辑，请相应更新代码
-- 4. 如果有大量数据，建议在系统低峰期执行

-- Add new columns to inz_order table for unified order management
ALTER TABLE inz_order 
ADD COLUMN order_type TINYINT(1) COMMENT '订单类型(1:普通订单,2:积分订单,3:混合支付)' AFTER store_id,
ADD COLUMN pay_price DECIMAL(10,2) DEFAULT 0.00 COMMENT '实际支付金额' AFTER total_price,
ADD COLUMN point_amount INT DEFAULT 0 COMMENT '积分支付数量' AFTER pay_price,
ADD COLUMN tracking_no VARCHAR(64) COMMENT '物流单号' AFTER refund_status,
ADD COLUMN express_company VARCHAR(100) COMMENT '物流公司名称' AFTER tracking_no,
ADD COLUMN express_code VARCHAR(50) COMMENT '物流公司编码' AFTER express_company,
ADD COLUMN ship_time DATETIME COMMENT '发货时间' AFTER express_code,
ADD COLUMN finish_time DATETIME COMMENT '完成时间' AFTER ship_time;

-- Add indexes for better query performance
CREATE INDEX idx_inz_order_order_type ON inz_order(order_type);
CREATE INDEX idx_inz_order_tracking_no ON inz_order(tracking_no);

-- Update existing orders to be type 1 (money payment)
UPDATE inz_order SET order_type = 1, pay_price = total_price WHERE order_type IS NULL;

-- Add comment to table
ALTER TABLE inz_order COMMENT '统一订单表(包含普通订单和积分订单)'; 