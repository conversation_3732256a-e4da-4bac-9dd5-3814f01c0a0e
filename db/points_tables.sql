-- Points Category Table
CREATE TABLE `inz_points_category` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(0-禁用,1-启用)',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='积分商品分类表';

-- Points Product Table
CREATE TABLE `inz_points_product` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `category_id` varchar(32) NOT NULL COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `description` text COMMENT '商品描述',
  `image` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `points_price` int NOT NULL COMMENT '积分价格',
  `original_price` decimal(10,2) DEFAULT '0.00' COMMENT '原价',
  `stock` int NOT NULL DEFAULT '0' COMMENT '库存数量',
  `sales` int DEFAULT '0' COMMENT '销量',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(0-下架,1-上架)',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_points_price` (`points_price`),
  KEY `idx_sales` (`sales`),
  CONSTRAINT `fk_points_product_category` FOREIGN KEY (`category_id`) REFERENCES `inz_points_category` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='积分商品表';

-- Points Order Table
CREATE TABLE `inz_points_order` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `order_no` varchar(32) NOT NULL COMMENT '订单编号',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `product_id` varchar(32) NOT NULL COMMENT '商品ID',
  `product_name` varchar(100) NOT NULL COMMENT '商品名称',
  `points_price` int NOT NULL COMMENT '积分价格',
  `quantity` int NOT NULL DEFAULT '1' COMMENT '数量',
  `total_points` int NOT NULL COMMENT '总积分',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '订单状态(0-待发货,1-已发货,2-已完成,3-已取消)',
  `receiver_name` varchar(50) NOT NULL COMMENT '收货人姓名',
  `receiver_phone` varchar(20) NOT NULL COMMENT '收货人电话',
  `receiver_address` varchar(255) NOT NULL COMMENT '收货地址',
  `tracking_no` varchar(50) DEFAULT NULL COMMENT '物流单号',
  `tracking_company` varchar(50) DEFAULT NULL COMMENT '物流公司',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user` (`user_id`),
  KEY `idx_product` (`product_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_points_order_product` FOREIGN KEY (`product_id`) REFERENCES `inz_points_product` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='积分订单表';

-- User Points Table
CREATE TABLE `inz_user_points` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `points_balance` int NOT NULL DEFAULT '0' COMMENT '积分余额',
  `total_points` int NOT NULL DEFAULT '0' COMMENT '累计获得积分',
  `used_points` int NOT NULL DEFAULT '0' COMMENT '已使用积分',
  `frozen_points` int NOT NULL DEFAULT '0' COMMENT '冻结积分',
  `last_update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user` (`user_id`),
  KEY `idx_points_balance` (`points_balance`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户积分账户表';

-- Points Record Table
CREATE TABLE `inz_points_record` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `points` int NOT NULL COMMENT '积分变动数量',
  `type` tinyint NOT NULL COMMENT '类型(1-获得,2-使用,3-过期,4-冻结,5-解冻)',
  `source` varchar(50) NOT NULL COMMENT '来源(sign-签到,order-订单,admin-管理员,system-系统)',
  `source_id` varchar(32) DEFAULT NULL COMMENT '来源ID',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `before_points` int NOT NULL COMMENT '变动前积分',
  `after_points` int NOT NULL COMMENT '变动后积分',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_source` (`source`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_expire_time` (`expire_time`),
  CONSTRAINT `fk_points_record_user` FOREIGN KEY (`user_id`) REFERENCES `inz_user_points` (`user_id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='积分记录表';

-- 创建积分规则表
CREATE TABLE IF NOT EXISTS `inz_points_rule` (
  `id` varchar(36) NOT NULL COMMENT '主键',
  `rule_code` varchar(50) DEFAULT NULL COMMENT '规则编码',
  `rule_name` varchar(100) DEFAULT NULL COMMENT '规则名称',
  `rule_desc` varchar(255) DEFAULT NULL COMMENT '规则描述',
  `points` int(11) DEFAULT NULL COMMENT '积分值',
  `rule_type` tinyint(1) DEFAULT NULL COMMENT '规则类型(1:获取规则,2:使用规则)',
  `task_type` tinyint(1) DEFAULT NULL COMMENT '任务类型(1:签到,2:分享,3:评价,4:关注,5:购物)',
  `daily_limit` int(11) DEFAULT '0' COMMENT '每日限制次数(0表示不限制)',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:禁用,1:启用)',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `sys_org_code` varchar(64) DEFAULT NULL COMMENT '所属部门编码',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rule_code` (`rule_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分规则表';

-- 插入一些初始数据
INSERT INTO `inz_points_rule` (`id`, `rule_code`, `rule_name`, `rule_desc`, `points`, `rule_type`, `task_type`, `daily_limit`, `status`, `sort`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
('1', 'SIGN_IN', '每日签到', '每日签到获取积分', 10, 1, 1, 1, 1, 1, 'admin', NOW(), 'admin', NOW()),
('2', 'SHARE', '分享内容', '分享内容到社交媒体', 5, 1, 2, 5, 1, 2, 'admin', NOW(), 'admin', NOW()),
('3', 'REVIEW', '评价商品', '评价已购买的商品', 15, 1, 3, 10, 1, 3, 'admin', NOW(), 'admin', NOW()),
('4', 'FOLLOW', '关注店铺', '关注店铺获取积分', 20, 1, 4, 1, 1, 4, 'admin', NOW(), 'admin', NOW()),
('5', 'PURCHASE', '购物消费', '每消费1元获取1积分', 1, 1, 5, 0, 1, 5, 'admin', NOW(), 'admin', NOW()),
('6', 'REDEEM_PRODUCT', '兑换商品', '使用积分兑换商品', 0, 2, NULL, 0, 1, 6, 'admin', NOW(), 'admin', NOW());

-- 创建用户积分表
CREATE TABLE IF NOT EXISTS `inz_user_points` (
  `id` varchar(36) NOT NULL COMMENT '主键',
  `user_id` varchar(36) NOT NULL COMMENT '用户ID',
  `total_points` int(11) DEFAULT '0' COMMENT '总积分',
  `available_points` int(11) DEFAULT '0' COMMENT '可用积分',
  `used_points` int(11) DEFAULT '0' COMMENT '已使用积分',
  `frozen_points` int(11) DEFAULT '0' COMMENT '冻结积分',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `sys_org_code` varchar(64) DEFAULT NULL COMMENT '所属部门编码',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户积分表';

-- 创建积分记录表
CREATE TABLE IF NOT EXISTS `inz_points_record` (
  `id` varchar(36) NOT NULL COMMENT '主键',
  `user_id` varchar(36) NOT NULL COMMENT '用户ID',
  `points` int(11) DEFAULT '0' COMMENT '积分值',
  `record_type` tinyint(1) DEFAULT NULL COMMENT '记录类型(1:获取,2:使用,3:冻结,4:解冻)',
  `rule_code` varchar(50) DEFAULT NULL COMMENT '规则编码',
  `rule_name` varchar(100) DEFAULT NULL COMMENT '规则名称',
  `biz_id` varchar(36) DEFAULT NULL COMMENT '业务ID',
  `biz_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `sys_org_code` varchar(64) DEFAULT NULL COMMENT '所属部门编码',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_rule_code` (`rule_code`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表';

-- 插入一些初始分类数据
INSERT INTO `inz_points_category` (`id`, `name`, `description`, `icon`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
('1', '虚拟商品', NULL, NULL, 1, 1, 'admin', NOW(), 'admin', NOW()),
('2', '实物商品', NULL, NULL, 2, 1, 'admin', NOW(), 'admin', NOW()),
('3', '优惠券', NULL, NULL, 3, 1, 'admin', NOW(), 'admin', NOW());

-- 创建积分商品表
CREATE TABLE IF NOT EXISTS `inz_points_product` (
  `id` varchar(36) NOT NULL COMMENT '主键',
  `product_name` varchar(100) NOT NULL COMMENT '商品名称',
  `product_desc` varchar(255) DEFAULT NULL COMMENT '商品描述',
  `product_image` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `points` int(11) DEFAULT '0' COMMENT '所需积分',
  `category_id` varchar(36) DEFAULT NULL COMMENT '分类ID',
  `stock` int(11) DEFAULT '0' COMMENT '库存',
  `limit_per_user` int(11) DEFAULT '0' COMMENT '每人限兑(0表示不限制)',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:下架,1:上架)',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `sys_org_code` varchar(64) DEFAULT NULL COMMENT '所属部门编码',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分商品表';

-- 创建积分订单表
CREATE TABLE IF NOT EXISTS `inz_points_order` (
  `id` varchar(36) NOT NULL COMMENT '主键',
  `order_no` varchar(50) NOT NULL COMMENT '订单编号',
  `user_id` varchar(36) NOT NULL COMMENT '用户ID',
  `product_id` varchar(36) NOT NULL COMMENT '商品ID',
  `product_name` varchar(100) NOT NULL COMMENT '商品名称',
  `product_image` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `points` int(11) DEFAULT '0' COMMENT '消耗积分',
  `quantity` int(11) DEFAULT '1' COMMENT '兑换数量',
  `total_points` int(11) DEFAULT '0' COMMENT '总积分',
  `status` tinyint(1) DEFAULT '0' COMMENT '订单状态(0:待处理,1:已处理,2:已取消)',
  `receiver_name` varchar(50) DEFAULT NULL COMMENT '收货人姓名',
  `receiver_phone` varchar(20) DEFAULT NULL COMMENT '收货人电话',
  `receiver_address` varchar(255) DEFAULT NULL COMMENT '收货地址',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `sys_org_code` varchar(64) DEFAULT NULL COMMENT '所属部门编码',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分订单表';

-- 店铺表
CREATE TABLE IF NOT EXISTS `inz_store` (
  `id` varchar(36) NOT NULL COMMENT '主键',
  `store_name` varchar(100) NOT NULL COMMENT '店铺名称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '店铺头像',
  `description` text COMMENT '店铺描述',
  `customer_service_wx` varchar(50) DEFAULT NULL COMMENT '客服微信',
  `fans_count` int(11) DEFAULT '0' COMMENT '粉丝数',
  `sold_count` int(11) DEFAULT '0' COMMENT '已售数量',
  `is_hot` tinyint(1) DEFAULT '0' COMMENT '是否热门',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标记',
  PRIMARY KEY (`id`),
  KEY `idx_store_name` (`store_name`),
  KEY `idx_is_hot` (`is_hot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺表';

-- 店铺关注表
CREATE TABLE IF NOT EXISTS `inz_store_follow` (
  `id` varchar(36) NOT NULL COMMENT '主键',
  `user_id` varchar(36) NOT NULL COMMENT '用户ID',
  `store_id` varchar(36) NOT NULL COMMENT '店铺ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标记',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_store` (`user_id`,`store_id`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺关注表'; 