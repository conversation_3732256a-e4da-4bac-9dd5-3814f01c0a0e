-- 创建店铺表
CREATE TABLE IF NOT EXISTS `inz_store` (
    `id` varchar(32) NOT NULL COMMENT '主键',
    `name` varchar(255) DEFAULT NULL COMMENT '店铺名称',
    `avatar` varchar(255) DEFAULT NULL COMMENT '店铺头像',
    `description` text COMMENT '店铺简介',
    `status` tinyint(4) DEFAULT '0' COMMENT '店铺状态（0-正常，1-关闭）',
    `type` tinyint(4) DEFAULT '1' COMMENT '店铺类型（1-个人，2-企业）',
    `verified` tinyint(4) DEFAULT '0' COMMENT '认证状态（0-未认证，1-已认证）',
    `fans_count` int(11) DEFAULT '0' COMMENT '粉丝数',
    `sales_count` int(11) DEFAULT '0' COMMENT '总销量',
    `rating` decimal(3,1) DEFAULT '5.0' COMMENT '店铺评分',
    `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
    `address` varchar(255) DEFAULT NULL COMMENT '店铺地址',
    `tags` varchar(255) DEFAULT NULL COMMENT '店铺分类标签，逗号分隔',
    `live_enabled` tinyint(4) DEFAULT '0' COMMENT '是否开通直播（0-未开通，1-已开通）',
    `level` int(11) DEFAULT '1' COMMENT '店铺等级',
    `points` int(11) DEFAULT '0' COMMENT '店铺积分',
    `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_status` (`status`),
    KEY `idx_rating` (`rating`),
    KEY `idx_sales` (`sales_count`),
    KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺信息表'; 