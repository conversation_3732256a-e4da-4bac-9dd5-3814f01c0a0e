-- 创建展馆卡片表
CREATE TABLE IF NOT EXISTS `inz_exhibition_card` (
    `id` varchar(32) NOT NULL COMMENT 'ID',
    `exhibition_id` varchar(32) DEFAULT NULL COMMENT '展馆ID',
    `card_id` varchar(32) DEFAULT NULL COMMENT '卡片ID',
    `card_name` varchar(100) DEFAULT NULL COMMENT '卡片名称',
    `card_image` varchar(255) DEFAULT NULL COMMENT '卡片图片',
    `card_grade` varchar(50) DEFAULT NULL COMMENT '卡片分级',
    `add_time` datetime DEFAULT NULL COMMENT '添加时间',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序序号',
    `team` varchar(100) DEFAULT NULL COMMENT '所属球队',
    `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_exhibition_id` (`exhibition_id`),
    KEY `idx_card_id` (`card_id`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='展馆卡片表'; 