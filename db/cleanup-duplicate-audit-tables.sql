-- ==========================================
-- 清理重复审核功能 SQL
-- 执行时间：2025-01-24
-- 目标：删除重复的审核表，集成到现有auditRecord模块
-- ==========================================

-- 1. 删除重复的审核相关表（如果存在）
DROP TABLE IF EXISTS `store_audit_record`;
DROP TABLE IF EXISTS `store_status_record`;

-- 2. 扩展现有audit_record表，支持店铺审核
ALTER TABLE `audit_record` ADD COLUMN `store_id` varchar(32) COMMENT '店铺ID' AFTER `business_type`;
ALTER TABLE `audit_record` ADD COLUMN `store_name` varchar(100) COMMENT '店铺名称' AFTER `store_id`;

-- 3. 创建店铺审核相关索引
CREATE INDEX `idx_audit_store_id` ON `audit_record`(`store_id`);
CREATE INDEX `idx_audit_type_status` ON `audit_record`(`audit_type`, `status`);

-- 4. 更新数据字典，添加店铺审核类型
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`) VALUES
('audit_type_dict', '审核类型', 'audit_type', '审核类型字典', 0, 'admin', NOW(), 'admin', NOW(), 0)
ON DUPLICATE KEY UPDATE `update_time` = NOW();

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
('audit_type_1', 'audit_type_dict', '商品上架', '1', '商品上架审核', 1, 1, 'admin', NOW(), 'admin', NOW()),
('audit_type_2', 'audit_type_dict', '售后申请', '2', '售后申请审核', 2, 1, 'admin', NOW(), 'admin', NOW()),
('audit_type_3', 'audit_type_dict', '店铺审核', '3', '店铺入驻审核', 3, 1, 'admin', NOW(), 'admin', NOW())
ON DUPLICATE KEY UPDATE `update_time` = NOW();

-- 5. 删除重复的权限配置
DELETE FROM `sys_permission` WHERE `perms` LIKE 'inz_store:audit:%';

-- 6. 更新现有权限，支持店铺审核
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`) VALUES
('store_submit_audit', 'store_manage', '提交店铺审核', NULL, NULL, NULL, NULL, 2, 'inz_store:submit_audit', '1', 10, 0, NULL, 1, 1, 0, 0, 0, '提交店铺审核', '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0)
ON DUPLICATE KEY UPDATE `update_time` = NOW();

-- 7. 创建视图，方便查询店铺审核记录
CREATE OR REPLACE VIEW `v_store_audit_records` AS
SELECT 
    ar.id,
    ar.business_id as store_id,
    ar.store_name,
    ar.audit_type,
    ar.status as audit_status,
    ar.comments as audit_remark,
    ar.auditor_id,
    ar.submitter,
    ar.submitter_id,
    ar.audit_time,
    ar.create_time,
    s.name as store_name_current,
    s.user_id as store_owner_id,
    s.phone as store_phone
FROM `audit_record` ar
LEFT JOIN `inz_store` s ON ar.business_id = s.id
WHERE ar.audit_type = 3;

-- 8. 创建触发器，同步店铺审核状态
DELIMITER $$
CREATE TRIGGER `tr_sync_store_audit_status` 
AFTER UPDATE ON `audit_record`
FOR EACH ROW
BEGIN
    -- 当店铺审核状态发生变化时，同步更新店铺表的审核状态
    IF NEW.audit_type = 3 AND OLD.status != NEW.status THEN
        UPDATE `inz_store` 
        SET 
            `audit_status` = NEW.status,
            `audit_remark` = NEW.comments,
            `audit_time` = NEW.audit_time,
            `audit_by` = NEW.auditor_id,
            `status` = CASE 
                WHEN NEW.status = 1 THEN 1  -- 审核通过，设置为营业状态
                WHEN NEW.status = 2 THEN 0  -- 审核拒绝，设置为关闭状态
                ELSE `status`               -- 待审核，保持原状态
            END,
            `update_time` = NOW()
        WHERE `id` = NEW.business_id;
    END IF;
END$$
DELIMITER ;

-- 9. 迁移现有店铺审核数据（如果有的话）
-- 注意：这里假设之前可能有一些店铺审核数据需要迁移
INSERT INTO `audit_record` (
    `id`, `business_id`, `audit_type`, `status`, `comments`, 
    `auditor_id`, `submitter_id`, `store_id`, `store_name`, 
    `audit_time`, `create_time`
)
SELECT 
    CONCAT('store_audit_', s.id) as id,
    s.id as business_id,
    3 as audit_type,
    COALESCE(s.audit_status, 0) as status,
    s.audit_remark as comments,
    s.audit_by as auditor_id,
    s.user_id as submitter_id,
    s.id as store_id,
    s.name as store_name,
    s.audit_time,
    COALESCE(s.audit_time, s.create_time) as create_time
FROM `inz_store` s
WHERE s.audit_status IS NOT NULL
AND NOT EXISTS (
    SELECT 1 FROM `audit_record` ar 
    WHERE ar.business_id = s.id AND ar.audit_type = 3
);

-- 10. 创建存储过程，简化店铺审核提交
DELIMITER $$
CREATE PROCEDURE `sp_submit_store_audit`(
    IN p_store_id VARCHAR(32),
    IN p_submitter_id VARCHAR(32),
    IN p_submitter_name VARCHAR(50)
)
BEGIN
    DECLARE v_store_name VARCHAR(100);
    DECLARE v_audit_id VARCHAR(32);
    
    -- 获取店铺名称
    SELECT `name` INTO v_store_name FROM `inz_store` WHERE `id` = p_store_id;
    
    -- 生成审核记录ID
    SET v_audit_id = CONCAT('store_audit_', p_store_id, '_', UNIX_TIMESTAMP(NOW()));
    
    -- 插入审核记录
    INSERT INTO `audit_record` (
        `id`, `business_id`, `audit_type`, `status`, 
        `submitter`, `submitter_id`, `store_id`, `store_name`, 
        `business_type`, `create_time`
    ) VALUES (
        v_audit_id, p_store_id, 3, 0,
        p_submitter_name, p_submitter_id, p_store_id, v_store_name,
        'STORE_AUDIT', NOW()
    );
    
    -- 更新店铺状态为待审核
    UPDATE `inz_store` 
    SET `audit_status` = 0, `update_time` = NOW()
    WHERE `id` = p_store_id;
    
    SELECT v_audit_id as audit_id, '店铺审核提交成功' as message;
END$$
DELIMITER ;

-- 11. 创建函数，获取店铺审核状态
DELIMITER $$
CREATE FUNCTION `fn_get_store_audit_status`(p_store_id VARCHAR(32))
RETURNS VARCHAR(20)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_status INT DEFAULT 0;
    
    SELECT `status` INTO v_status 
    FROM `audit_record` 
    WHERE `business_id` = p_store_id AND `audit_type` = 3
    ORDER BY `create_time` DESC 
    LIMIT 1;
    
    RETURN CASE v_status
        WHEN 0 THEN '待审核'
        WHEN 1 THEN '审核通过'
        WHEN 2 THEN '审核拒绝'
        ELSE '未知状态'
    END;
END$$
DELIMITER ;

-- 12. 清理重复的定时任务配置
DELETE FROM `sys_quartz_job` WHERE `job_class_name` LIKE '%StoreAudit%';

-- ==========================================
-- 重复功能清理完成
-- 删除表：2个重复表
-- 扩展表：audit_record表支持店铺审核
-- 新增字段：2个店铺相关字段
-- 创建视图：1个店铺审核视图
-- 创建触发器：1个状态同步触发器
-- 创建存储过程：1个审核提交过程
-- 创建函数：1个状态查询函数
-- ==========================================

-- 验证清理结果
SELECT 
    '审核类型统计' as category,
    audit_type,
    COUNT(*) as count
FROM audit_record 
GROUP BY audit_type
UNION ALL
SELECT 
    '店铺审核状态统计' as category,
    status as audit_type,
    COUNT(*) as count
FROM audit_record 
WHERE audit_type = 3
GROUP BY status;
