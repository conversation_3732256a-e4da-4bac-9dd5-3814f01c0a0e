-- 创建消息中心表
CREATE TABLE IF NOT EXISTS `inz_message` (
    `id` varchar(32) NOT NULL COMMENT '主键',
    `user_id` varchar(32) DEFAULT NULL COMMENT '用户ID',
    `type` tinyint(4) DEFAULT NULL COMMENT '消息类型（1：平台通知，2：交易动态）',
    `title` varchar(200) DEFAULT NULL COMMENT '消息标题',
    `content` text COMMENT '消息内容',
    `relation_id` varchar(32) DEFAULT NULL COMMENT '关联ID（订单ID等）',
    `status` tinyint(4) DEFAULT '0' COMMENT '消息状态（0：未读，1：已读）',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息中心'; 