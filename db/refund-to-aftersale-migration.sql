-- ==========================================
-- 退款功能迁移到售后系统 SQL
-- 执行时间：2025-01-24
-- 目标：为订单表添加售后相关字段，建立数据同步机制
-- ==========================================

-- 1. 为订单表添加售后相关字段
ALTER TABLE `inz_order` ADD COLUMN `after_sale_id` varchar(32) COMMENT '关联售后ID' AFTER `refund_time`;
ALTER TABLE `inz_order` ADD COLUMN `after_sale_status` int(1) DEFAULT 0 COMMENT '售后状态(0-无,1-申请中,2-处理中,3-已完成)' AFTER `after_sale_id`;

-- 2. 创建索引优化查询性能
CREATE INDEX `idx_order_after_sale` ON `inz_order`(`after_sale_id`);
CREATE INDEX `idx_order_after_sale_status` ON `inz_order`(`after_sale_status`);
CREATE INDEX `idx_order_user_after_sale` ON `inz_order`(`user_id`, `after_sale_status`);

-- 3. 创建数据同步触发器
DELIMITER $$
CREATE TRIGGER `tr_sync_order_after_sale_status` 
AFTER UPDATE ON `inz_after_sale`
FOR EACH ROW
BEGIN
    -- 当售后状态发生变化时，同步更新订单的售后状态
    IF OLD.status != NEW.status THEN
        UPDATE `inz_order` 
        SET `after_sale_status` = CASE 
            WHEN NEW.status = 1 THEN 1          -- 待审核 → 申请中
            WHEN NEW.status IN (2,4) THEN 2     -- 审核通过/退款中 → 处理中
            WHEN NEW.status = 5 THEN 3          -- 已完成 → 已完成
            WHEN NEW.status IN (3,6) THEN 0     -- 审核拒绝/已取消 → 无售后
            ELSE `after_sale_status`
        END,
        `update_time` = NOW()
        WHERE `id` = NEW.order_id;
    END IF;
END$$
DELIMITER ;

-- 4. 创建售后申请触发器
DELIMITER $$
CREATE TRIGGER `tr_sync_order_after_sale_insert` 
AFTER INSERT ON `inz_after_sale`
FOR EACH ROW
BEGIN
    -- 新增售后申请时，更新订单的售后信息
    UPDATE `inz_order` 
    SET `after_sale_id` = NEW.id,
        `after_sale_status` = 1,  -- 申请中
        `update_time` = NOW()
    WHERE `id` = NEW.order_id;
END$$
DELIMITER ;

-- 5. 创建售后数据统计视图
CREATE OR REPLACE VIEW `v_order_after_sale_stats` AS
SELECT 
    DATE(o.create_time) as order_date,
    COUNT(o.id) as total_orders,
    COUNT(o.after_sale_id) as orders_with_after_sale,
    COUNT(CASE WHEN o.after_sale_status = 1 THEN 1 END) as applying_count,
    COUNT(CASE WHEN o.after_sale_status = 2 THEN 1 END) as processing_count,
    COUNT(CASE WHEN o.after_sale_status = 3 THEN 1 END) as completed_count,
    ROUND(COUNT(o.after_sale_id) * 100.0 / COUNT(o.id), 2) as after_sale_rate
FROM `inz_order` o
GROUP BY DATE(o.create_time)
ORDER BY order_date DESC;

-- 6. 创建存储过程：迁移现有退款数据
DELIMITER $$
CREATE PROCEDURE `sp_migrate_refund_to_after_sale`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_order_id VARCHAR(32);
    DECLARE v_order_no VARCHAR(50);
    DECLARE v_user_id VARCHAR(32);
    DECLARE v_store_id VARCHAR(32);
    DECLARE v_refund_status INT;
    DECLARE v_refund_time DATETIME;
    DECLARE v_pay_price DECIMAL(10,2);
    DECLARE v_after_sale_id VARCHAR(32);
    
    -- 声明游标
    DECLARE refund_cursor CURSOR FOR 
        SELECT id, order_no, user_id, store_id, refund_status, refund_time, pay_price
        FROM `inz_order` 
        WHERE refund_status IS NOT NULL 
        AND refund_status > 1 
        AND after_sale_id IS NULL;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 开始迁移
    OPEN refund_cursor;
    
    read_loop: LOOP
        FETCH refund_cursor INTO v_order_id, v_order_no, v_user_id, v_store_id, v_refund_status, v_refund_time, v_pay_price;
        
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 生成售后ID
        SET v_after_sale_id = CONCAT('AS', DATE_FORMAT(NOW(), '%Y%m%d'), LPAD(FLOOR(RAND() * 10000), 4, '0'));
        
        -- 创建售后记录
        INSERT INTO `inz_after_sale` (
            id, order_id, order_no, user_id, store_id, type, reason, 
            refund_amount, apply_amount, status, apply_time, create_time
        ) VALUES (
            v_after_sale_id,
            v_order_id,
            v_order_no,
            v_user_id,
            v_store_id,
            1, -- 仅退款
            '历史退款数据迁移',
            v_pay_price,
            v_pay_price,
            CASE v_refund_status
                WHEN 2 THEN 1  -- 用户已提交，未退款 → 待审核
                WHEN 3 THEN 4  -- 退款中 → 退款中
                WHEN 4 THEN 5  -- 退款成功 → 已完成
                WHEN 5 THEN 3  -- 退款失败 → 审核拒绝
                WHEN 6 THEN 3  -- 退款驳回 → 审核拒绝
                ELSE 1
            END,
            COALESCE(v_refund_time, NOW()),
            NOW()
        );
        
        -- 更新订单售后信息
        UPDATE `inz_order` 
        SET after_sale_id = v_after_sale_id,
            after_sale_status = CASE v_refund_status
                WHEN 2 THEN 1  -- 申请中
                WHEN 3 THEN 2  -- 处理中
                WHEN 4 THEN 3  -- 已完成
                WHEN 5 THEN 0  -- 无售后
                WHEN 6 THEN 0  -- 无售后
                ELSE 0
            END
        WHERE id = v_order_id;
        
    END LOOP;
    
    CLOSE refund_cursor;
    
    -- 返回迁移结果
    SELECT 
        COUNT(*) as migrated_count,
        '历史退款数据迁移完成' as message
    FROM `inz_after_sale` 
    WHERE reason = '历史退款数据迁移';
    
END$$
DELIMITER ;

-- 7. 创建函数：获取订单售后状态文本
DELIMITER $$
CREATE FUNCTION `fn_get_order_after_sale_status_text`(p_status INT)
RETURNS VARCHAR(20)
READS SQL DATA
DETERMINISTIC
BEGIN
    RETURN CASE p_status
        WHEN 0 THEN '无售后'
        WHEN 1 THEN '申请中'
        WHEN 2 THEN '处理中'
        WHEN 3 THEN '已完成'
        ELSE '未知状态'
    END;
END$$
DELIMITER ;

-- 8. 更新数据字典，添加售后状态
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`) VALUES
('order_after_sale_status_dict', '订单售后状态', 'order_after_sale_status', '订单售后状态字典', 0, 'admin', NOW(), 'admin', NOW(), 0)
ON DUPLICATE KEY UPDATE `update_time` = NOW();

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
('order_after_sale_status_0', 'order_after_sale_status_dict', '无售后', '0', '订单无售后申请', 1, 1, 'admin', NOW(), 'admin', NOW()),
('order_after_sale_status_1', 'order_after_sale_status_dict', '申请中', '1', '售后申请中', 2, 1, 'admin', NOW(), 'admin', NOW()),
('order_after_sale_status_2', 'order_after_sale_status_dict', '处理中', '2', '售后处理中', 3, 1, 'admin', NOW(), 'admin', NOW()),
('order_after_sale_status_3', 'order_after_sale_status_dict', '已完成', '3', '售后已完成', 4, 1, 'admin', NOW(), 'admin', NOW())
ON DUPLICATE KEY UPDATE `update_time` = NOW();

-- 9. 创建售后申请权限
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`) VALUES
('after_sale_front_apply', 'after_sale_manage', '申请售后', NULL, NULL, NULL, NULL, 2, 'after_sale:front:apply', '1', 10, 0, NULL, 1, 1, 0, 0, 0, '前端用户申请售后', '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0),
('after_sale_front_list', 'after_sale_manage', '我的售后', NULL, NULL, NULL, NULL, 2, 'after_sale:front:list', '1', 11, 0, NULL, 1, 1, 0, 0, 0, '前端用户查看售后列表', '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0),
('after_sale_front_detail', 'after_sale_manage', '售后详情', NULL, NULL, NULL, NULL, 2, 'after_sale:front:detail', '1', 12, 0, NULL, 1, 1, 0, 0, 0, '前端用户查看售后详情', '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0),
('after_sale_front_cancel', 'after_sale_manage', '取消售后', NULL, NULL, NULL, NULL, 2, 'after_sale:front:cancel', '1', 13, 0, NULL, 1, 1, 0, 0, 0, '前端用户取消售后申请', '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0)
ON DUPLICATE KEY UPDATE `update_time` = NOW();

-- 10. 验证迁移结果
SELECT 
    '数据库结构检查' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'inz_order' AND COLUMN_NAME = 'after_sale_id') 
        THEN '✅ 订单表售后字段已添加'
        ELSE '❌ 订单表售后字段未添加'
    END as status
UNION ALL
SELECT 
    '触发器检查' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.TRIGGERS WHERE TRIGGER_SCHEMA = DATABASE() AND TRIGGER_NAME = 'tr_sync_order_after_sale_status') 
        THEN '✅ 售后状态同步触发器已创建'
        ELSE '❌ 售后状态同步触发器未创建'
    END as status
UNION ALL
SELECT 
    '索引检查' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = 'inz_order' AND index_name = 'idx_order_after_sale') 
        THEN '✅ 订单售后索引已创建'
        ELSE '❌ 订单售后索引未创建'
    END as status
UNION ALL
SELECT 
    '权限检查' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM `sys_permission` WHERE `perms` = 'after_sale:front:apply') 
        THEN '✅ 前端售后权限已配置'
        ELSE '❌ 前端售后权限未配置'
    END as status;

-- ==========================================
-- 退款功能迁移到售后系统完成
-- 新增功能：
-- 1. 订单表售后字段 (2个)
-- 2. 数据同步触发器 (2个)
-- 3. 查询优化索引 (3个)
-- 4. 数据统计视图 (1个)
-- 5. 数据迁移存储过程 (1个)
-- 6. 状态查询函数 (1个)
-- 7. 数据字典配置 (4个状态)
-- 8. 权限配置 (4个权限)
-- ==========================================

-- 执行数据迁移 (可选)
-- CALL sp_migrate_refund_to_after_sale();
