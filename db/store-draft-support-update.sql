-- ==========================================
-- Store草稿功能支持 SQL
-- 执行时间：2025-01-24
-- 目标：为Store表添加草稿状态支持
-- ==========================================

-- 1. 更新Store表的status字段注释，支持草稿状态
ALTER TABLE `inz_store` MODIFY COLUMN `status` int(1) DEFAULT 0 COMMENT '状态(-1-草稿,0-待审核,1-正常营业,2-暂停营业,3-已关闭)';

-- 2. 创建草稿状态索引，优化草稿查询性能
CREATE INDEX `idx_user_status` ON `inz_store`(`user_id`, `status`);

-- 3. 更新数据字典，添加草稿状态
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
('store_status_draft', 'store_status_dict', '草稿', '-1', '店铺创建草稿状态', 0, 1, 'admin', NOW(), 'admin', NOW())
ON DUPLICATE KEY UPDATE `update_time` = NOW();

-- 4. 创建草稿清理定时任务配置
INSERT INTO `sys_quartz_job` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `job_class_name`, `cron_expression`, `parameter`, `description`, `status`) VALUES
('store_draft_cleanup_job', 'admin', NOW(), 'admin', NOW(), 'org.jeecg.modules.api.store.job.StoreDraftCleanupJob', '0 0 2 * * ?', '{"daysToKeep": 30}', '店铺草稿清理任务(保留30天)', 1)
ON DUPLICATE KEY UPDATE `update_time` = NOW();

-- 5. 创建草稿数据统计视图
CREATE OR REPLACE VIEW `v_store_draft_stats` AS
SELECT 
    DATE(create_time) as draft_date,
    COUNT(*) as draft_count,
    COUNT(DISTINCT user_id) as user_count
FROM `inz_store` 
WHERE `status` = -1
GROUP BY DATE(create_time)
ORDER BY draft_date DESC;

-- 6. 创建存储过程：清理过期草稿
DELIMITER $$
CREATE PROCEDURE `sp_cleanup_expired_drafts`(
    IN p_days_to_keep INT DEFAULT 30
)
BEGIN
    DECLARE v_deleted_count INT DEFAULT 0;
    
    -- 删除超过指定天数的草稿
    DELETE FROM `inz_store` 
    WHERE `status` = -1 
    AND `create_time` < DATE_SUB(NOW(), INTERVAL p_days_to_keep DAY);
    
    -- 获取删除的记录数
    SET v_deleted_count = ROW_COUNT();
    
    -- 记录清理日志
    INSERT INTO `sys_log` (`log_type`, `log_content`, `operate_type`, `userid`, `username`, `ip`, `method`, `request_url`, `request_param`, `request_type`, `cost_time`, `create_by`, `create_time`, `update_by`, `update_time`) 
    VALUES (1, CONCAT('清理过期店铺草稿，删除数量: ', v_deleted_count), 1, 'system', 'system', '127.0.0.1', 'sp_cleanup_expired_drafts', '/system/cleanup', CONCAT('{"daysToKeep": ', p_days_to_keep, '}'), 'PROCEDURE', 0, 'system', NOW(), 'system', NOW());
    
    SELECT v_deleted_count as deleted_count, CONCAT('成功清理 ', v_deleted_count, ' 个过期草稿') as message;
END$$
DELIMITER ;

-- 7. 创建函数：获取用户草稿数量
DELIMITER $$
CREATE FUNCTION `fn_get_user_draft_count`(p_user_id VARCHAR(32))
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_count INT DEFAULT 0;
    
    SELECT COUNT(*) INTO v_count 
    FROM `inz_store` 
    WHERE `user_id` = p_user_id AND `status` = -1;
    
    RETURN v_count;
END$$
DELIMITER ;

-- 8. 创建触发器：草稿数量限制
DELIMITER $$
CREATE TRIGGER `tr_store_draft_limit` 
BEFORE INSERT ON `inz_store`
FOR EACH ROW
BEGIN
    DECLARE v_draft_count INT DEFAULT 0;
    
    -- 如果是草稿状态，检查用户草稿数量
    IF NEW.status = -1 THEN
        SELECT COUNT(*) INTO v_draft_count 
        FROM `inz_store` 
        WHERE `user_id` = NEW.user_id AND `status` = -1;
        
        -- 限制每个用户最多只能有1个草稿
        IF v_draft_count >= 1 THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '每个用户最多只能保存一个店铺创建草稿';
        END IF;
    END IF;
END$$
DELIMITER ;

-- 9. 验证草稿功能配置
SELECT 
    '草稿状态配置检查' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM `sys_dict_item` WHERE `item_value` = '-1' AND `item_text` = '草稿') 
        THEN '✅ 草稿状态字典已配置'
        ELSE '❌ 草稿状态字典未配置'
    END as status
UNION ALL
SELECT 
    '草稿索引检查' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.statistics WHERE table_name = 'inz_store' AND index_name = 'idx_user_status') 
        THEN '✅ 草稿查询索引已创建'
        ELSE '❌ 草稿查询索引未创建'
    END as status
UNION ALL
SELECT 
    '定时任务检查' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM `sys_quartz_job` WHERE `id` = 'store_draft_cleanup_job') 
        THEN '✅ 草稿清理定时任务已配置'
        ELSE '❌ 草稿清理定时任务未配置'
    END as status;

-- ==========================================
-- Store草稿功能支持完成
-- 新增功能：
-- 1. 草稿状态支持 (status = -1)
-- 2. 草稿查询索引优化
-- 3. 草稿清理定时任务
-- 4. 草稿数量限制 (每用户1个)
-- 5. 草稿统计视图
-- 6. 草稿管理存储过程和函数
-- ==========================================
