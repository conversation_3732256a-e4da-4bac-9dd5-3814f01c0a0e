-- 创建新用户数据 (2024年6月13日)

-- 1. 用户基本信息
INSERT INTO `sys_user` 
(`id`, `username`, `realname`, `password`, `salt`, `avatar`, `sex`, `email`, `phone`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`)
VALUES 
('f8d5c5e76c8a9b4e3d2a1f0g9h8i7j6k', 'xiaoming', '小明', '123456', 'abc123', 'avatar/default.jpg', 1, '<EMAIL>', '***********', 1, 0, 'system', '2024-06-13 10:00:00', 'system', '2024-06-13 10:00:00');

-- 2. 常规订单数据
INSERT INTO `inz_order` 
(`id`, `order_no`, `user_id`, `store_id`, `total_amount`, `pay_amount`, `discount_amount`, `freight_amount`, 
`status`, `pay_time`, `pay_type`, `trade_no`, `receiver_name`, `receiver_phone`, `receiver_address`, 
`remark`, `create_time`, `create_by`, `update_time`, `update_by`) 
VALUES
-- 已完成订单
('ord101', 'KL2024061304', 'f8d5c5e76c8a9b4e3d2a1f0g9h8i7j6k', 'store001', 599.00, 579.00, 30.00, 10.00, 
3, '2024-06-13 11:30:00', 1, 'ALI987654321', '小明', '***********', '上海市浦东新区某某路2号', 
'请工作日送货', '2024-06-13 11:25:00', 'system', '2024-06-13 16:00:00', 'system'),

-- 待收货订单
('ord102', 'KL2024061305', 'f8d5c5e76c8a9b4e3d2a1f0g9h8i7j6k', 'store002', 299.00, 299.00, 0.00, 0.00, 
2, '2024-06-13 15:20:00', 2, 'WX123456789', '小明', '***********', '上海市浦东新区某某路2号', 
'周末送货', '2024-06-13 15:15:00', 'system', '2024-06-13 15:20:00', 'system'),

-- 待付款订单
('ord103', 'KL2024061306', 'f8d5c5e76c8a9b4e3d2a1f0g9h8i7j6k', 'store003', 899.00, 879.00, 30.00, 10.00, 
0, NULL, NULL, NULL, '小明', '***********', '上海市浦东新区某某路2号', 
NULL, '2024-06-13 17:00:00', 'system', '2024-06-13 17:00:00', 'system');

-- 3. 订单商品数据
INSERT INTO `inz_order_item` 
(`id`, `order_id`, `product_id`, `product_name`, `product_image`, `price`, `quantity`, `total_amount`, 
`create_time`, `create_by`, `update_time`, `update_by`) 
VALUES
-- 订单1的商品
('item101', 'ord101', 'prod004', '智能手环', 'band001.jpg', 599.00, 1, 599.00, 
'2024-06-13 11:25:00', 'system', '2024-06-13 11:25:00', 'system'),

-- 订单2的商品
('item102', 'ord102', 'prod005', '运动背包', 'bag001.jpg', 299.00, 1, 299.00,
'2024-06-13 15:15:00', 'system', '2024-06-13 15:15:00', 'system'),

-- 订单3的商品
('item103', 'ord103', 'prod006', '无线耳机', 'headphone001.jpg', 899.00, 1, 899.00,
'2024-06-13 17:00:00', 'system', '2024-06-13 17:00:00', 'system');

-- 4. 用户积分账户
INSERT INTO `inz_user_points` 
(`id`, `user_id`, `total_points`, `available_points`, `used_points`, `frozen_points`, 
`create_time`, `create_by`, `update_time`, `update_by`) 
VALUES
('up101', 'f8d5c5e76c8a9b4e3d2a1f0g9h8i7j6k', 2000, 1600, 300, 100,
'2024-06-13 10:00:00', 'system', '2024-06-13 17:00:00', 'system');

-- 5. 积分记录
INSERT INTO `inz_points_record` 
(`id`, `user_id`, `points`, `type`, `source`, `order_id`, `remark`, 
`create_time`, `create_by`, `update_time`, `update_by`) 
VALUES
-- 注册奖励
('pr101', 'f8d5c5e76c8a9b4e3d2a1f0g9h8i7j6k', 1000, 1, 4, NULL,
'新用户注册奖励', '2024-06-13 10:00:00', 'system', '2024-06-13 10:00:00', 'system'),

-- 签到奖励
('pr102', 'f8d5c5e76c8a9b4e3d2a1f0g9h8i7j6k', 10, 1, 1, NULL,
'每日签到奖励', '2024-06-13 10:30:00', 'system', '2024-06-13 10:30:00', 'system'),

-- 购物奖励
('pr103', 'f8d5c5e76c8a9b4e3d2a1f0g9h8i7j6k', 580, 1, 2, 'ord101',
'购物订单KL2024061304获得积分', '2024-06-13 16:00:00', 'system', '2024-06-13 16:00:00', 'system'),

-- 积分兑换
('pr104', 'f8d5c5e76c8a9b4e3d2a1f0g9h8i7j6k', 300, 2, 3, NULL,
'兑换优惠券', '2024-06-13 14:00:00', 'system', '2024-06-13 14:00:00', 'system'),

-- 活动奖励
('pr105', 'f8d5c5e76c8a9b4e3d2a1f0g9h8i7j6k', 500, 1, 5, NULL,
'618购物节活动奖励', '2024-06-13 13:00:00', 'system', '2024-06-13 13:00:00', 'system');

-- 6. 积分订单
INSERT INTO `inz_points_order` 
(`id`, `order_no`, `user_id`, `product_id`, `product_name`, `product_image`, 
`quantity`, `points`, `status`, `receiver_name`, `receiver_phone`, `receiver_address`, 
`tracking_no`, `express_company`, `ship_time`, `finish_time`, `remark`, `product_type`, 
`create_time`, `create_by`, `update_time`, `update_by`) 
VALUES
-- 已完成的积分订单（实物商品）
('po101', 'PO2024061303', 'f8d5c5e76c8a9b4e3d2a1f0g9h8i7j6k', 'pp003', '便携式充电宝', 
'powerbank001.jpg', 1, 200, 2, '小明', '***********', '上海市浦东新区某某路2号',
'SF987654321', '顺丰快递', '2024-06-13 15:00:00', '2024-06-13 17:00:00',
'无', 1, '2024-06-13 14:00:00', 'system', '2024-06-13 17:00:00', 'system'),

-- 待发货的积分订单（优惠券）
('po102', 'PO2024061304', 'f8d5c5e76c8a9b4e3d2a1f0g9h8i7j6k', 'pp004', '50元通用优惠券',
'coupon001.jpg', 1, 100, 0, '小明', '***********', '上海市浦东新区某某路2号',
NULL, NULL, NULL, NULL, '虚拟商品', 3, '2024-06-13 16:30:00', 'system', 
'2024-06-13 16:30:00', 'system');

-- 7. 用户消息
INSERT INTO `inz_message` 
(`id`, `user_id`, `type`, `title`, `content`, `relation_id`, `status`, 
`create_time`, `create_by`, `update_time`, `update_by`) 
VALUES
-- 欢迎消息
('msg101', 'f8d5c5e76c8a9b4e3d2a1f0g9h8i7j6k', 1, '欢迎加入', 
'欢迎加入我们的平台！', NULL, 0, 
'2024-06-13 10:00:00', 'system', '2024-06-13 10:00:00', 'system'),

-- 订单相关消息
('msg102', 'f8d5c5e76c8a9b4e3d2a1f0g9h8i7j6k', 2, '订单支付成功', 
'您的订单 KL2024061304 已支付成功', 'ord101', 0, 
'2024-06-13 11:30:00', 'system', '2024-06-13 11:30:00', 'system'),

-- 积分相关消息
('msg103', 'f8d5c5e76c8a9b4e3d2a1f0g9h8i7j6k', 1, '积分到账提醒', 
'恭喜您获得580积分奖励', NULL, 0, 
'2024-06-13 16:00:00', 'system', '2024-06-13 16:00:00', 'system'),

-- 活动消息
('msg104', 'f8d5c5e76c8a9b4e3d2a1f0g9h8i7j6k', 1, '618活动提醒', 
'618购物节开始啦，快来参与吧！', NULL, 0, 
'2024-06-13 13:00:00', 'system', '2024-06-13 13:00:00', 'system'),

-- 系统消息
('msg105', 'f8d5c5e76c8a9b4e3d2a1f0g9h8i7j6k', 1, '系统通知', 
'您的账户安全等级已提升', NULL, 0, 
'2024-06-13 12:00:00', 'system', '2024-06-13 12:00:00', 'system'); 