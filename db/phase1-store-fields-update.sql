-- ==========================================
-- 第一阶段：店铺核心字段补充 SQL
-- 执行时间：2025-01-24
-- 目标：补充店铺管理核心缺失字段
-- ==========================================

-- 1. 添加店铺编号和店主信息字段
ALTER TABLE `inz_store` ADD COLUMN `shop_code` varchar(50) COMMENT '店铺编号' AFTER `rating`;
ALTER TABLE `inz_store` ADD COLUMN `owner_name` varchar(100) COMMENT '店主姓名' AFTER `shop_code`;
ALTER TABLE `inz_store` ADD COLUMN `owner_phone` varchar(20) COMMENT '店主手机号' AFTER `owner_name`;
ALTER TABLE `inz_store` ADD COLUMN `owner_email` varchar(100) COMMENT '店主邮箱' AFTER `owner_phone`;

-- 2. 添加企业认证信息字段
ALTER TABLE `inz_store` ADD COLUMN `legal_person` varchar(100) COMMENT '法人代表' AFTER `owner_email`;
ALTER TABLE `inz_store` ADD COLUMN `credit_code` varchar(50) COMMENT '统一社会信用代码' AFTER `legal_person`;
ALTER TABLE `inz_store` ADD COLUMN `business_license_img` varchar(500) COMMENT '营业执照图片' AFTER `credit_code`;
ALTER TABLE `inz_store` ADD COLUMN `id_card_front` varchar(500) COMMENT '身份证正面' AFTER `business_license_img`;
ALTER TABLE `inz_store` ADD COLUMN `id_card_back` varchar(500) COMMENT '身份证反面' AFTER `id_card_front`;

-- 3. 添加财务管理字段
ALTER TABLE `inz_store` ADD COLUMN `deposit` decimal(10,2) DEFAULT 0.00 COMMENT '保证金' AFTER `id_card_back`;
ALTER TABLE `inz_store` ADD COLUMN `balance` decimal(10,2) DEFAULT 0.00 COMMENT '账户余额' AFTER `deposit`;
ALTER TABLE `inz_store` ADD COLUMN `frozen_amount` decimal(10,2) DEFAULT 0.00 COMMENT '冻结金额' AFTER `balance`;
ALTER TABLE `inz_store` ADD COLUMN `commission_rate` decimal(5,2) DEFAULT 0.00 COMMENT '佣金比例' AFTER `frozen_amount`;
ALTER TABLE `inz_store` ADD COLUMN `month_sales` decimal(12,2) DEFAULT 0.00 COMMENT '月销售额' AFTER `commission_rate`;
ALTER TABLE `inz_store` ADD COLUMN `total_sales` decimal(15,2) DEFAULT 0.00 COMMENT '总销售额' AFTER `month_sales`;

-- 4. 添加审核流程字段
ALTER TABLE `inz_store` ADD COLUMN `audit_status` int(1) DEFAULT 0 COMMENT '审核状态(0-待审核,1-审核通过,2-审核拒绝)' AFTER `total_sales`;
ALTER TABLE `inz_store` ADD COLUMN `audit_remark` varchar(500) COMMENT '审核备注' AFTER `audit_status`;
ALTER TABLE `inz_store` ADD COLUMN `audit_time` datetime COMMENT '审核时间' AFTER `audit_remark`;
ALTER TABLE `inz_store` ADD COLUMN `audit_by` varchar(50) COMMENT '审核人' AFTER `audit_time`;

-- 5. 添加营业时间管理字段
ALTER TABLE `inz_store` ADD COLUMN `open_time` datetime COMMENT '开店时间' AFTER `audit_by`;
ALTER TABLE `inz_store` ADD COLUMN `close_time` datetime COMMENT '关店时间' AFTER `open_time`;
ALTER TABLE `inz_store` ADD COLUMN `close_reason` varchar(500) COMMENT '关店原因' AFTER `close_time`;
ALTER TABLE `inz_store` ADD COLUMN `business_start_time` varchar(10) DEFAULT '09:00' COMMENT '营业开始时间' AFTER `close_reason`;
ALTER TABLE `inz_store` ADD COLUMN `business_end_time` varchar(10) DEFAULT '21:00' COMMENT '营业结束时间' AFTER `business_start_time`;

-- 6. 添加推广管理字段
ALTER TABLE `inz_store` ADD COLUMN `is_recommend` int(1) DEFAULT 0 COMMENT '是否推荐(0-否,1-是)' AFTER `business_end_time`;
ALTER TABLE `inz_store` ADD COLUMN `sort_order` int(10) DEFAULT 0 COMMENT '排序权重' AFTER `is_recommend`;
ALTER TABLE `inz_store` ADD COLUMN `remark` varchar(500) COMMENT '备注' AFTER `sort_order`;

-- 7. 创建店铺编号唯一索引
ALTER TABLE `inz_store` ADD UNIQUE INDEX `uk_shop_code` (`shop_code`);

-- 8. 创建常用查询索引
ALTER TABLE `inz_store` ADD INDEX `idx_audit_status` (`audit_status`);
ALTER TABLE `inz_store` ADD INDEX `idx_owner_phone` (`owner_phone`);
ALTER TABLE `inz_store` ADD INDEX `idx_credit_code` (`credit_code`);
ALTER TABLE `inz_store` ADD INDEX `idx_is_recommend` (`is_recommend`);
ALTER TABLE `inz_store` ADD INDEX `idx_sort_order` (`sort_order`);

-- 9. 添加数据字典配置
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`) VALUES
('shop_audit_status_dict', '店铺审核状态', 'audit_status', '店铺审核状态字典', 0, 'admin', NOW(), 'admin', NOW(), 0);

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
('audit_status_0', 'shop_audit_status_dict', '待审核', '0', '店铺待审核状态', 1, 1, 'admin', NOW(), 'admin', NOW()),
('audit_status_1', 'shop_audit_status_dict', '审核通过', '1', '店铺审核通过状态', 2, 1, 'admin', NOW(), 'admin', NOW()),
('audit_status_2', 'shop_audit_status_dict', '审核拒绝', '2', '店铺审核拒绝状态', 3, 1, 'admin', NOW(), 'admin', NOW());

-- 10. 更新现有数据的默认值
UPDATE `inz_store` SET 
    `audit_status` = 1,
    `business_start_time` = '09:00',
    `business_end_time` = '21:00',
    `is_recommend` = 0,
    `sort_order` = 0,
    `deposit` = 0.00,
    `balance` = 0.00,
    `frozen_amount` = 0.00,
    `commission_rate` = 5.00,
    `month_sales` = 0.00,
    `total_sales` = 0.00
WHERE `audit_status` IS NULL;

-- 11. 生成店铺编号（为现有店铺生成编号）
UPDATE `inz_store` SET `shop_code` = CONCAT('SHOP', LPAD(id, 8, '0')) WHERE `shop_code` IS NULL OR `shop_code` = '';

-- 12. 同步店主信息（从用户表同步）
UPDATE `inz_store` s 
INNER JOIN `sys_user` u ON s.user_id = u.id 
SET 
    s.owner_name = u.realname,
    s.owner_phone = u.phone,
    s.owner_email = u.email
WHERE s.owner_name IS NULL OR s.owner_name = '';

-- ==========================================
-- 第一阶段字段补充完成
-- 新增字段总数：23个
-- 核心功能：店铺编号、企业认证、财务管理、审核流程
-- ==========================================
