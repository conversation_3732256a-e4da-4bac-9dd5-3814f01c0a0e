-- 更新店铺表结构以匹配实体类
ALTER TABLE `inz_store` 
-- 重命名 logo 列为 avatar
CHANGE COLUMN `logo` `avatar` varchar(255) DEFAULT NULL COMMENT '店铺头像',
-- 添加缺少的字段
ADD COLUMN `type` tinyint(4) DEFAULT '1' COMMENT '店铺类型（1-个人，2-企业）' AFTER `status`,
ADD COLUMN `verified` tinyint(4) DEFAULT '0' COMMENT '认证状态（0-未认证，1-已认证）' AFTER `type`,
ADD COLUMN `fans_count` int(11) DEFAULT '0' COMMENT '粉丝数' AFTER `verified`,
ADD COLUMN `tags` varchar(255) DEFAULT NULL COMMENT '店铺分类标签，逗号分隔' AFTER `address`,
ADD COLUMN `live_enabled` tinyint(4) DEFAULT '0' COMMENT '是否开通直播（0-未开通，1-已开通）' AFTER `tags`,
ADD COLUMN `level` int(11) DEFAULT '1' COMMENT '店铺等级' AFTER `live_enabled`,
ADD COLUMN `points` int(11) DEFAULT '0' COMMENT '店铺积分' AFTER `level`,
-- 重命名 followers_count 列为 fans_count
CHANGE COLUMN `followers_count` `fans_count` int(11) DEFAULT '0' COMMENT '粉丝数',
-- 修改 phone 字段（如果需要）
CHANGE COLUMN `contact_phone` `phone` varchar(20) DEFAULT NULL COMMENT '联系电话';

-- 添加索引
ALTER TABLE `inz_store`
ADD INDEX `idx_rating` (`rating`),
ADD INDEX `idx_level` (`level`),
ADD INDEX `idx_sales` (`sales_count`); 