-- 创建商品表
CREATE TABLE IF NOT EXISTS `inz_product` (
    `id` varchar(32) NOT NULL COMMENT '主键',
    `name` varchar(255) DEFAULT NULL COMMENT '商品名称',
    `product_no` varchar(50) DEFAULT NULL COMMENT '商品编号',
    `store_id` varchar(32) DEFAULT NULL COMMENT '店铺ID',
    `main_image` varchar(255) DEFAULT NULL COMMENT '商品主图',
    `images` text COMMENT '商品图片，多个逗号分隔',
    `price` decimal(10,2) DEFAULT NULL COMMENT '商品价格',
    `original_price` decimal(10,2) DEFAULT NULL COMMENT '商品原价',
    `stock` int(11) DEFAULT '0' COMMENT '商品库存',
    `sales` int(11) DEFAULT '0' COMMENT '商品销量',
    `status` tinyint(4) DEFAULT '0' COMMENT '商品状态（0-下架，1-上架）',
    `type` tinyint(4) DEFAULT '1' COMMENT '商品类型（1-普通商品，2-限量版，3-签名版）',
    `description` varchar(500) DEFAULT NULL COMMENT '商品描述',
    `detail` text COMMENT '商品详情',
    `specifications` text COMMENT '商品规格（JSON格式）',
    `category` varchar(50) DEFAULT NULL COMMENT '商品分类',
    `tags` varchar(255) DEFAULT NULL COMMENT '商品标签，逗号分隔',
    `year` varchar(10) DEFAULT NULL COMMENT '年份',
    `team` varchar(100) DEFAULT NULL COMMENT '球队',
    `player` varchar(100) DEFAULT NULL COMMENT '球员',
    `grade` varchar(50) DEFAULT NULL COMMENT '卡片评级',
    `rating` decimal(3,1) DEFAULT NULL COMMENT '评分',
    `limited_number` varchar(20) DEFAULT NULL COMMENT '限量编号（如1/99）',
    `has_signed` tinyint(4) DEFAULT '0' COMMENT '是否有签名（0-无，1-有）',
    `has_jersey_patch` tinyint(4) DEFAULT '0' COMMENT '是否有球衣碎片（0-无，1-有）',
    `card_type` varchar(50) DEFAULT NULL COMMENT '卡片类型',
    `freight_template_id` varchar(32) DEFAULT NULL COMMENT '运费模板ID',
    `free_shipping` tinyint(4) DEFAULT '0' COMMENT '是否包邮（0-否，1-是）',
    `sort` int(11) DEFAULT '0' COMMENT '排序',
    `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_store` (`store_id`),
    KEY `idx_status` (`status`),
    KEY `idx_category` (`category`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品信息表'; 