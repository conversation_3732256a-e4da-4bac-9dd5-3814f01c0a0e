-- 创建用户展馆表
CREATE TABLE IF NOT EXISTS `inz_exhibition` (
    `id` varchar(32) NOT NULL COMMENT 'ID',
    `user_id` varchar(32) DEFAULT NULL COMMENT '用户ID',
    `name` varchar(100) DEFAULT NULL COMMENT '展馆名称',
    `description` text COMMENT '展馆描述',
    `cover_image` varchar(255) DEFAULT NULL COMMENT '展馆封面图',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `status` tinyint(4) DEFAULT '1' COMMENT '状态 (1-公开 0-私有)',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户展馆表'; 