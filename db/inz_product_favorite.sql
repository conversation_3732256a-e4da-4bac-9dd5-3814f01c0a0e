-- 创建商品收藏表
CREATE TABLE IF NOT EXISTS `inz_product_favorite` (
    `id` varchar(32) NOT NULL COMMENT 'ID',
    `user_id` varchar(32) DEFAULT NULL COMMENT '用户ID',
    `product_id` varchar(32) DEFAULT NULL COMMENT '商品ID',
    `status` tinyint(4) DEFAULT '1' COMMENT '收藏状态（0-已取消，1-收藏中）',
    `price` decimal(10,2) DEFAULT NULL COMMENT '商品价格（收藏时的价格）',
    `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_product` (`user_id`,`product_id`),
    KEY `idx_user_status` (`user_id`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品收藏'; 