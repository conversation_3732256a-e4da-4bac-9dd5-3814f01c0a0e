<template>
    <view>
        <!--标题和返回-->
		<cu-custom :bgColor="NavBarColor" isBack :backRouterName="backRouteName">
			<block slot="backText">返回</block>
			<block slot="content">售后表</block>
		</cu-custom>
		 <!--表单区域-->
		<view>
			<form>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">订单ID：</text></view>
                  <input  placeholder="请输入订单ID" v-model="model.orderId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">订单编号：</text></view>
                  <input  placeholder="请输入订单编号" v-model="model.orderNo"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">用户ID：</text></view>
                  <input  placeholder="请输入用户ID" v-model="model.userId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">店铺ID：</text></view>
                  <input  placeholder="请输入店铺ID" v-model="model.storeId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">售后类型(1:仅退款,2:退货退款,3:换货)：</text></view>
                  <input type="number" placeholder="请输入售后类型(1:仅退款,2:退货退款,3:换货)" v-model="model.type"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">售后原因：</text></view>
                  <input  placeholder="请输入售后原因" v-model="model.reason"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">问题描述：</text></view>
                  <input  placeholder="请输入问题描述" v-model="model.description"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">凭证图片(逗号分隔)：</text></view>
                  <input  placeholder="请输入凭证图片(逗号分隔)" v-model="model.images"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">退款金额：</text></view>
                  <input type="number" placeholder="请输入退款金额" v-model="model.refundAmount"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">售后状态(1:待审核,2:审核通过,3:审核拒绝,4:退款中,5:已完成,6:已取消)：</text></view>
                  <input type="number" placeholder="请输入售后状态(1:待审核,2:审核通过,3:审核拒绝,4:退款中,5:已完成,6:已取消)" v-model="model.status"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">处理备注：</text></view>
                  <input  placeholder="请输入处理备注" v-model="model.handleNote"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">退款流水号：</text></view>
                  <input  placeholder="请输入退款流水号" v-model="model.refundNo"/>
                </view>
              </view>
              <my-date label="审核时间：" v-model="model.auditTime" placeholder="请输入审核时间"></my-date>
              <my-date label="退款时间：" v-model="model.refundTime" placeholder="请输入退款时间"></my-date>
              <my-date label="完成时间：" v-model="model.finishTime" placeholder="请输入完成时间"></my-date>
				<view class="padding">
					<button class="cu-btn block bg-blue margin-tb-sm lg" @click="onSubmit">
						<text v-if="loading" class="cuIcon-loading2 cuIconfont-spin"></text>提交
					</button>
				</view>
			</form>
		</view>
    </view>
</template>

<script>
    import myDate from '@/components/my-componets/my-date.vue'

    export default {
        name: "AfterSaleForm",
        components:{ myDate },
        props:{
          formData:{
              type:Object,
              default:()=>{},
              required:false
          }
        },
        data(){
            return {
				CustomBar: this.CustomBar,
				NavBarColor: this.NavBarColor,
				loading:false,
                model: {},
                backRouteName:'index',
                url: {
                  queryById: "/after_sale/afterSale/queryById",
                  add: "/after_sale/afterSale/add",
                  edit: "/after_sale/afterSale/edit",
                },
            }
        },
        created(){
             this.initFormData();
        },
        methods:{
           initFormData(){
               if(this.formData){
                    let dataId = this.formData.dataId;
                    this.$http.get(this.url.queryById,{params:{id:dataId}}).then((res)=>{
                        if(res.data.success){
                            console.log("表单数据",res);
                            this.model = res.data.result;
                        }
                    })
                }
            },
            onSubmit() {
                let myForm = {...this.model};
                this.loading = true;
                let url = myForm.id?this.url.edit:this.url.add;
				this.$http.post(url,myForm).then(res=>{
				   console.log("res",res)
				   this.loading = false
				   this.$Router.push({name:this.backRouteName})
				}).catch(()=>{
					this.loading = false
				});
            }
        }
    }
</script>
