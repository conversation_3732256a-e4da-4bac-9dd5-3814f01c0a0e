package org.jeecg.modules.inz_configs.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_configs.entity.InzConfigs;
import org.jeecg.modules.inz_configs.service.IInzConfigsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 配置信息表
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Api(tags="配置信息表")
@RestController
@RequestMapping("/inz_configs/inzConfigs")
@Slf4j
public class InzConfigsController extends JeecgController<InzConfigs, IInzConfigsService> {
	@Autowired
	private IInzConfigsService inzConfigsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzConfigs
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "配置信息表-分页列表查询")
	@ApiOperation(value="配置信息表-分页列表查询", notes="配置信息表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<InzConfigs>> queryPageList(InzConfigs inzConfigs,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<InzConfigs> queryWrapper = QueryGenerator.initQueryWrapper(inzConfigs, req.getParameterMap());
		Page<InzConfigs> page = new Page<InzConfigs>(pageNo, pageSize);
		IPage<InzConfigs> pageList = inzConfigsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param inzConfigs
	 * @return
	 */
	@AutoLog(value = "配置信息表-添加")
	@ApiOperation(value="配置信息表-添加", notes="配置信息表-添加")
	@RequiresPermissions("inz_configs:inz_config:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InzConfigs inzConfigs) {
		inzConfigsService.save(inzConfigs);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param inzConfigs
	 * @return
	 */
	@AutoLog(value = "配置信息表-编辑")
	@ApiOperation(value="配置信息表-编辑", notes="配置信息表-编辑")
	@RequiresPermissions("inz_configs:inz_config:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InzConfigs inzConfigs) {
		inzConfigsService.updateById(inzConfigs);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "配置信息表-通过id删除")
	@ApiOperation(value="配置信息表-通过id删除", notes="配置信息表-通过id删除")
	@RequiresPermissions("inz_configs:inz_config:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzConfigsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "配置信息表-批量删除")
	@ApiOperation(value="配置信息表-批量删除", notes="配置信息表-批量删除")
	@RequiresPermissions("inz_configs:inz_config:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzConfigsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "配置信息表-通过id查询")
	@ApiOperation(value="配置信息表-通过id查询", notes="配置信息表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<InzConfigs> queryById(@RequestParam(name="id",required=true) String id) {
		InzConfigs inzConfigs = inzConfigsService.getById(id);
		if(inzConfigs==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzConfigs);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param inzConfigs
    */
    @RequiresPermissions("inz_configs:inz_config:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzConfigs inzConfigs) {
        return super.exportXls(request, inzConfigs, InzConfigs.class, "配置信息表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("inz_configs:inz_config:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzConfigs.class);
    }

}
