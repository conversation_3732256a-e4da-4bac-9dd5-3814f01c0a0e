package org.jeecg.modules.inz_card_lnfo.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_card_lnfo.entity.InzCardInfo;
import org.jeecg.modules.inz_card_lnfo.service.IInzCardInfoService;
import org.jeecg.modules.inz_store.service.IInzStoreService;
import org.jeecg.modules.inz_users_fronts.service.IInzUsersFrontsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 卡片信息表
 * @Author: jeecg-boot
 * @Date:   2025-06-20
 * @Version: V1.0
 */
@Api(tags="卡片信息表")
@RestController
@RequestMapping("/inz_card_lnfo/inzCardInfo")
@Slf4j
@Validated
public class InzCardInfoController extends JeecgController<InzCardInfo, IInzCardInfoService> {
	@Autowired
	private IInzCardInfoService inzCardInfoService;
	
	@Autowired
	private IInzStoreService inzStoreService;
	
	@Autowired
	private IInzUsersFrontsService inzUsersFrontsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzCardInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "卡片信息表-分页列表查询")
	@ApiOperation(value="卡片信息表-分页列表查询", notes="卡片信息表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<InzCardInfo>> queryPageList(InzCardInfo inzCardInfo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   @RequestParam(name="storeName", required=false) String storeName,
								   @RequestParam(name="userName", required=false) String username,
								   @RequestParam(name="cardName", required=false) String cardName,
								   @RequestParam(name="cardType", required=false) String cardType,
								   HttpServletRequest req) {
        QueryWrapper<InzCardInfo> queryWrapper = QueryGenerator.initQueryWrapper(inzCardInfo, req.getParameterMap());
        
        // 处理店铺名称筛选
        if (StringUtils.isNotBlank(storeName)) {
            String storeId = inzStoreService.getStoreIdByName(storeName);
            if (StringUtils.isNotBlank(storeId)) {
                queryWrapper.eq("store_id", storeId);
            } else {
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }
        }
        
        // 处理用户名称筛选
        if (StringUtils.isNotBlank(username)) {
            String userId = inzUsersFrontsService.getUserIdByUsername(username);
            if (StringUtils.isNotBlank(userId)) {
                queryWrapper.eq("user_id", userId);
            } else {
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }
        }
        
        // 处理卡片名称筛选
        if (StringUtils.isNotBlank(cardName)) {
            queryWrapper.like("name", cardName);
        }
        
        // 处理卡片类型筛选
        if (StringUtils.isNotBlank(cardType)) {
            queryWrapper.eq("type", cardType);
        }
        
        // ====== 数据权限控制逻辑开始 ======
        org.jeecg.common.system.vo.LoginUser sysUser = (org.jeecg.common.system.vo.LoginUser) org.apache.shiro.SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null) {
            if (!isAdmin(sysUser)) {
                log.info("非管理员用户访问卡片信息列表，进行数据权限过滤");
                String phone = sysUser.getPhone();
                String userId = null;
                if (StringUtils.isNotBlank(phone)) {
                    try {
                        userId = inzUsersFrontsService.getUserIdByPhone(phone);
                        log.info("通过手机号[{}]查询到前端用户ID: {}", phone, userId);
                    } catch (Exception e) {
                        log.warn("通过手机号查询前端用户失败: {}", e.getMessage());
                    }
                } else {
                    log.warn("后台用户[{}]没有关联手机号", sysUser.getUsername());
                }
                if (StringUtils.isNotBlank(userId)) {
                    QueryWrapper<org.jeecg.modules.inz_store.entity.InzStore> storeQueryWrapper = new QueryWrapper<>();
                    storeQueryWrapper.eq("user_id", userId);
                    org.jeecg.modules.inz_store.entity.InzStore store = inzStoreService.getOne(storeQueryWrapper);
                    if (store != null) {
                        log.info("用户[{}]关联店铺[{}]，只能查看该店铺卡片信息", userId, store.getId());
                        queryWrapper.eq("store_id", store.getId());
                    } else {
                        log.warn("用户[{}]没有关联店铺，返回空结果", userId);
                        return Result.OK(new Page<>(pageNo, pageSize, 0));
                    }
                } else {
                    log.warn("未找到后台用户[{}]对应的前端用户，返回空结果", sysUser.getUsername());
                    return Result.OK(new Page<>(pageNo, pageSize, 0));
                }
            } else {
                log.info("管理员用户访问卡片信息列表，不进行数据权限过滤");
            }
        }
        // ====== 数据权限控制逻辑结束 ======
        
        Page<InzCardInfo> page = new Page<InzCardInfo>(pageNo, pageSize);
        IPage<InzCardInfo> pageList = inzCardInfoService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 判断用户是否为管理员
     * @param user 登录用户
     * @return 是否为管理员
     */
    private boolean isAdmin(org.jeecg.common.system.vo.LoginUser user) {
        if (user == null) {
            return false;
        }
        String roleCode = user.getRoleCode();
        return StringUtils.isNotBlank(roleCode) && (roleCode.contains("admin") || roleCode.contains("ADMIN"));
    }
	
	/**
	 * 通过店铺名称查询卡片信息
	 *
	 * @param storeName 店铺名称
	 * @param pageNo 页码
	 * @param pageSize 每页记录数
	 * @return 卡片信息分页结果
	 */
	@ApiOperation(value = "通过店铺名称查询卡片信息", notes = "根据店铺名称查询对应的卡片信息")
	@GetMapping(value = "/listByStoreName")
	public Result<IPage<InzCardInfo>> queryCardInfosByStoreName(
	                                 @RequestParam(name = "storeName") String storeName,
	                                 @RequestParam(name = "pageNo", defaultValue = "1") @Min(value = 1, message = "页码不能小于1") Integer pageNo,
	                                 @RequestParam(name = "pageSize", defaultValue = "10") @Min(value = 1, message = "每页记录数不能小于1") @Max(value = 100, message = "每页记录数不能超过100") Integer pageSize) {
	    try {
	        if (StringUtils.isBlank(storeName)) {
	            return Result.error("店铺名称不能为空");
	        }
	        
	        // 通过店铺名称获取店铺ID
	        String storeId = inzStoreService.getStoreIdByName(storeName);
	        if (StringUtils.isBlank(storeId)) {
	            return Result.error("未找到名为 '" + storeName + "' 的店铺");
	        }
	        
	        // 创建分页对象
	        Page<InzCardInfo> page = new Page<>(pageNo, pageSize);
	        
	        // 查询卡片信息
	        IPage<InzCardInfo> pageList = inzCardInfoService.queryCardInfosByStoreId(page, storeId);
	        
	        return Result.OK(pageList);
	    } catch (Exception e) {
	        log.error("查询卡片信息失败", e);
	        return Result.error("查询卡片信息失败: " + e.getMessage());
	    }
	}
	
	/**
	 *   添加
	 *
	 * @param inzCardInfo
	 * @return
	 */
	@AutoLog(value = "卡片信息表-添加")
	@ApiOperation(value="卡片信息表-添加", notes="卡片信息表-添加")
	@RequiresPermissions("inz_card_lnfo:card_info:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InzCardInfo inzCardInfo) {
		inzCardInfoService.save(inzCardInfo);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param inzCardInfo
	 * @return
	 */
	@AutoLog(value = "卡片信息表-编辑")
	@ApiOperation(value="卡片信息表-编辑", notes="卡片信息表-编辑")
	@RequiresPermissions("inz_card_lnfo:card_info:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InzCardInfo inzCardInfo) {
		inzCardInfoService.updateById(inzCardInfo);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "卡片信息表-通过id删除")
	@ApiOperation(value="卡片信息表-通过id删除", notes="卡片信息表-通过id删除")
	@RequiresPermissions("inz_card_lnfo:card_info:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzCardInfoService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "卡片信息表-批量删除")
	@ApiOperation(value="卡片信息表-批量删除", notes="卡片信息表-批量删除")
	@RequiresPermissions("inz_card_lnfo:card_info:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzCardInfoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "卡片信息表-通过id查询")
	@ApiOperation(value="卡片信息表-通过id查询", notes="卡片信息表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<InzCardInfo> queryById(@RequestParam(name="id",required=true) String id) {
		InzCardInfo inzCardInfo = inzCardInfoService.getById(id);
		if(inzCardInfo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzCardInfo);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param inzCardInfo
    */
    @RequiresPermissions("inz_card_lnfo:card_info:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzCardInfo inzCardInfo) {
        return super.exportXls(request, inzCardInfo, InzCardInfo.class, "卡片信息表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("inz_card_lnfo:card_info:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzCardInfo.class);
    }

}
