<template>
    <view>
        <!--标题和返回-->
		<cu-custom :bgColor="NavBarColor" isBack :backRouterName="backRouteName">
			<block slot="backText">返回</block>
			<block slot="content">积分商品表</block>
		</cu-custom>
		 <!--表单区域-->
		<view>
			<form>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品名称：</text></view>
                  <input  placeholder="请输入商品名称" v-model="model.name"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品图片：</text></view>
                  <input  placeholder="请输入商品图片" v-model="model.image"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品描述：</text></view>
                  <input  placeholder="请输入商品描述" v-model="model.description"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">所需积分：</text></view>
                  <input type="number" placeholder="请输入所需积分" v-model="model.points"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">库存数量：</text></view>
                  <input type="number" placeholder="请输入库存数量" v-model="model.stock"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">已兑换数量：</text></view>
                  <input type="number" placeholder="请输入已兑换数量" v-model="model.exchangeCount"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">限购数量(0表示不限购)：</text></view>
                  <input type="number" placeholder="请输入限购数量(0表示不限购)" v-model="model.limitCount"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品类型(1:实物商品,2:虚拟商品,3:优惠券)：</text></view>
                  <input type="number" placeholder="请输入商品类型(1:实物商品,2:虚拟商品,3:优惠券)" v-model="model.type"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品状态(0:下架,1:上架)：</text></view>
                  <input type="number" placeholder="请输入商品状态(0:下架,1:上架)" v-model="model.status"/>
                </view>
              </view>
              <my-date label="上架时间：" v-model="model.shelfTime" placeholder="请输入上架时间"></my-date>
              <my-date label="下架时间：" v-model="model.offShelfTime" placeholder="请输入下架时间"></my-date>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">排序：</text></view>
                  <input type="number" placeholder="请输入排序" v-model="model.sort"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">是否限量版(0:否,1:是)：</text></view>
                  <input type="number" placeholder="请输入是否限量版(0:否,1:是)" v-model="model.isLimited"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">限量编号：</text></view>
                  <input  placeholder="请输入限量编号" v-model="model.limitedNumber"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">关联商品ID：</text></view>
                  <input  placeholder="请输入关联商品ID" v-model="model.productId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品标签：</text></view>
                  <input  placeholder="请输入商品标签" v-model="model.tags"/>
                </view>
              </view>
				<view class="padding">
					<button class="cu-btn block bg-blue margin-tb-sm lg" @click="onSubmit">
						<text v-if="loading" class="cuIcon-loading2 cuIconfont-spin"></text>提交
					</button>
				</view>
			</form>
		</view>
    </view>
</template>

<script>
    import myDate from '@/components/my-componets/my-date.vue'

    export default {
        name: "InzPointsProductsForm",
        components:{ myDate },
        props:{
          formData:{
              type:Object,
              default:()=>{},
              required:false
          }
        },
        data(){
            return {
				CustomBar: this.CustomBar,
				NavBarColor: this.NavBarColor,
				loading:false,
                model: {},
                backRouteName:'index',
                url: {
                  queryById: "/inz_points_products/inzPointsProducts/queryById",
                  add: "/inz_points_products/inzPointsProducts/add",
                  edit: "/inz_points_products/inzPointsProducts/edit",
                },
            }
        },
        created(){
             this.initFormData();
        },
        methods:{
           initFormData(){
               if(this.formData){
                    let dataId = this.formData.dataId;
                    this.$http.get(this.url.queryById,{params:{id:dataId}}).then((res)=>{
                        if(res.data.success){
                            console.log("表单数据",res);
                            this.model = res.data.result;
                        }
                    })
                }
            },
            onSubmit() {
                let myForm = {...this.model};
                this.loading = true;
                let url = myForm.id?this.url.edit:this.url.add;
				this.$http.post(url,myForm).then(res=>{
				   console.log("res",res)
				   this.loading = false
				   this.$Router.push({name:this.backRouteName})
				}).catch(()=>{
					this.loading = false
				});
            }
        }
    }
</script>
