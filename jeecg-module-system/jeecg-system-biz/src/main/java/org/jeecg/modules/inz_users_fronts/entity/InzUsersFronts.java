package org.jeecg.modules.inz_users_fronts.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 用户表
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Data
@TableName("inz_user_front")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_user_front对象", description="用户表")
public class InzUsersFronts implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**真实姓名*/
	@Excel(name = "真实姓名", width = 15)
    @ApiModelProperty(value = "真实姓名")
    private java.lang.String realName;
	/**年级*/
	@Excel(name = "年级", width = 15)
    @ApiModelProperty(value = "年级")
    private java.lang.String grade;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private java.lang.String phone;
	/**密码*/
	@Excel(name = "密码", width = 15)
    @ApiModelProperty(value = "密码")
    private java.lang.String password;
	/**地址*/
	@Excel(name = "地址", width = 15)
    @ApiModelProperty(value = "地址")
    private java.lang.String address;
	/**公众号昵称*/
	@Excel(name = "公众号昵称", width = 15)
    @ApiModelProperty(value = "公众号昵称")
    private java.lang.String oaNickname;
	/**公众号 OpenId*/
	@Excel(name = "公众号 OpenId", width = 15)
    @ApiModelProperty(value = "公众号 OpenId")
    private java.lang.String oaOpenid;
	/**小程序OpenId*/
	@Excel(name = "小程序OpenId", width = 15)
    @ApiModelProperty(value = "小程序OpenId")
    private java.lang.String mpOpenid;
	/**企业微信 OpenId*/
	@Excel(name = "企业微信 OpenId", width = 15)
    @ApiModelProperty(value = "企业微信 OpenId")
    private java.lang.String ewOpenid;
	/**联合 Id*/
	@Excel(name = "联合 Id", width = 15)
    @ApiModelProperty(value = "联合 Id")
    private java.lang.String unionId;
	/**角色*/
	@Excel(name = "角色", width = 15)
    @ApiModelProperty(value = "角色")
    private java.lang.String role;
	/**状态 (1正常 0停用)*/
	@Excel(name = "状态 (1正常 0停用)", width = 15)
    @ApiModelProperty(value = "状态 (1正常 0停用)")
    private java.lang.String status;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
	/**头像*/
	@Excel(name = "头像", width = 15)
    @ApiModelProperty(value = "头像")
    private java.lang.String avatar;
	/**最后一次进入系统的时间*/
	@Excel(name = "最后一次进入系统的时间", width = 15)
    @ApiModelProperty(value = "最后一次进入系统的时间")
    private java.lang.String lastUseAt;
	/**会员时间*/
	@Excel(name = "会员时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "会员时间")
    private java.util.Date vipTime;
	/**salt*/
	@Excel(name = "salt", width = 15)
    @ApiModelProperty(value = "salt")
    private java.lang.String salt;
	/**thirdType*/
	@Excel(name = "thirdType", width = 15)
    @ApiModelProperty(value = "thirdType")
    private java.lang.String thirdType;
	/**会员状态 (0-非会员, 1-普通会员, 2-永久会员)*/
	@Excel(name = "会员状态 (0-非会员, 1-普通会员, 2-永久会员)", width = 15)
    @ApiModelProperty(value = "会员状态 (0-非会员, 1-普通会员, 2-永久会员)")
    private java.lang.Integer isVip;
	/**微信OpenID*/
	@Excel(name = "微信OpenID", width = 15)
    @ApiModelProperty(value = "微信OpenID")
    private java.lang.String openid;
	/**微信昵称*/
	@Excel(name = "微信昵称", width = 15)
    @ApiModelProperty(value = "微信昵称")
    private java.lang.String nickname;
	/**微信头像URL*/
	@Excel(name = "微信头像URL", width = 15)
    @ApiModelProperty(value = "微信头像URL")
    private java.lang.String headimgurl;
	/**性别 (1男 2女 0未选择)*/
	@Excel(name = "性别 (1男 2女 0未选择)", width = 15)
    @ApiModelProperty(value = "性别 (1男 2女 0未选择)")
    private java.lang.Integer gender;
	/**玩家号*/
	@Excel(name = "玩家号", width = 15)
    @ApiModelProperty(value = "玩家号")
    private java.lang.String playid;
	/**用户名*/
	@Excel(name = "用户名", width = 15)
    @ApiModelProperty(value = "用户名")
    private java.lang.String username;
	/**玩家号*/
	@Excel(name = "玩家号", width = 15)
    @ApiModelProperty(value = "玩家号")
    private java.lang.String playId;
	/**用户积分*/
	@Excel(name = "用户积分", width = 15)
    @ApiModelProperty(value = "用户积分")
    private java.lang.Integer points;
}
