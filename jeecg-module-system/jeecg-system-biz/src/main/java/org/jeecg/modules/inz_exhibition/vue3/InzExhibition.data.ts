import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '用户ID',
    align:"center",
    dataIndex: 'userId'
   },
   {
    title: '展馆名称',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '展馆描述',
    align:"center",
    dataIndex: 'description'
   },
   {
    title: '展馆封面图',
    align:"center",
    dataIndex: 'coverImage'
   },
   {
    title: '状态 (1-公开 0-私有)',
    align:"center",
    dataIndex: 'status'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '用户ID',
    field: 'userId',
    component: 'Input',
  },
  {
    label: '展馆名称',
    field: 'name',
    component: 'Input',
  },
  {
    label: '展馆描述',
    field: 'description',
    component: 'InputTextArea',
  },
  {
    label: '展馆封面图',
    field: 'coverImage',
    component: 'Input',
  },
  {
    label: '状态 (1-公开 0-私有)',
    field: 'status',
    component: 'InputNumber',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  userId: {title: '用户ID',order: 0,view: 'text', type: 'string',},
  name: {title: '展馆名称',order: 1,view: 'text', type: 'string',},
  description: {title: '展馆描述',order: 2,view: 'textarea', type: 'string',},
  coverImage: {title: '展馆封面图',order: 3,view: 'text', type: 'string',},
  status: {title: '状态 (1-公开 0-私有)',order: 4,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}