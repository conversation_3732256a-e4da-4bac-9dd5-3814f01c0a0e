package org.jeecg.modules.inz_orders_logistics.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 发货记录表
 * @Author: jeecg-boot
 * @Date: 2024-07-28
 * @Version: V1.0
 */
@Data
@TableName("inz_shipping_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_shipping_record对象", description="发货记录表")
public class ShippingRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    
    /**发货单号*/
    @Excel(name = "发货单号", width = 15)
    @ApiModelProperty(value = "发货单号")
    private String shippingNo;
    
    /**快递公司*/
    @Excel(name = "快递公司", width = 15)
    @ApiModelProperty(value = "快递公司")
    private String expressCompany;
    
    /**快递单号*/
    @Excel(name = "快递单号", width = 15)
    @ApiModelProperty(value = "快递单号")
    private String trackingNo;
    
    /**卡片数量*/
    @Excel(name = "卡片数量", width = 15)
    @ApiModelProperty(value = "卡片数量")
    private Integer cardCount;
    
    /**收件人姓名*/
    @Excel(name = "收件人姓名", width = 15)
    @ApiModelProperty(value = "收件人姓名")
    private String receiverName;
    
    /**收件人地址*/
    @Excel(name = "收件人地址", width = 15)
    @ApiModelProperty(value = "收件人地址")
    private String receiverAddress;
    
    /**收件人电话*/
    @Excel(name = "收件人电话", width = 15)
    @ApiModelProperty(value = "收件人电话")
    private String receiverPhone;
    
    /**发货类型(1:系统发货,2:人工发货)*/
    @Excel(name = "发货类型", width = 15)
    @ApiModelProperty(value = "发货类型(1:系统发货,2:人工发货)")
    private Integer shippingType;
    
    /**状态(0:待发货,1:已发货,2:已签收,3:异常)*/
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态(0:待发货,1:已发货,2:已签收,3:异常)")
    private Integer status;
    
    /**物流费用*/
    @Excel(name = "物流费用", width = 15)
    @ApiModelProperty(value = "物流费用")
    private java.math.BigDecimal shippingFee;
    
    /**最新物流信息*/
    @Excel(name = "最新物流信息", width = 15)
    @ApiModelProperty(value = "最新物流信息")
    private String latestLogistics;
    
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
} 