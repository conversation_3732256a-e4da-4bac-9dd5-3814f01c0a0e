package org.jeecg.modules.inz_orders_logistics.util;

import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * HTTP客户端工具类，用于发送HTTP请求
 */
public class HttpClientUtil {
    private static final Logger log = LoggerFactory.getLogger(HttpClientUtil.class);
    private static final int CONNECT_TIMEOUT = 5000;
    private static final int SOCKET_TIMEOUT = 10000;
    
    /**
     * 发送POST请求
     * 
     * @param url 请求URL
     * @param params 请求参数
     * @param headers 请求头
     * @return 响应内容
     */
    public static String doPost(String url, String params, Map<String, String> headers) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String result = "";
        
        try {
            HttpPost httpPost = new HttpPost(url);
            
            // 设置请求超时
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(CONNECT_TIMEOUT)
                    .setSocketTimeout(SOCKET_TIMEOUT)
                    .build();
            httpPost.setConfig(requestConfig);
            
            // 设置请求头
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httpPost.setHeader(entry.getKey(), entry.getValue());
                }
            }
            
            // 设置默认请求头
            if (!httpPost.containsHeader("Content-Type")) {
                httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
            }
            
            // 设置请求体
            if (params != null && !params.isEmpty()) {
                StringEntity entity = new StringEntity(params, StandardCharsets.UTF_8);
                httpPost.setEntity(entity);
            }
            
            // 执行请求
            response = httpClient.execute(httpPost);
            
            // 获取响应
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                result = EntityUtils.toString(entity, StandardCharsets.UTF_8);
            }
        } catch (Exception e) {
            log.error("HTTP请求出错: {}", e.getMessage(), e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (IOException e) {
                log.error("关闭HTTP连接出错: {}", e.getMessage(), e);
            }
        }
        
        return result;
    }
    
    /**
     * 发送JSON格式的POST请求
     * 
     * @param url 请求URL
     * @param jsonParams JSON请求参数
     * @return 响应内容
     */
    public static String doPostJson(String url, String jsonParams) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        return doPost(url, jsonParams, headers);
    }
} 