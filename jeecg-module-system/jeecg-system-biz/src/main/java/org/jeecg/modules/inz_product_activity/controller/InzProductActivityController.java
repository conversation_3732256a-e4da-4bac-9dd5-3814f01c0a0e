package org.jeecg.modules.inz_product_activity.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_product_activity.entity.InzProductActivity;
import org.jeecg.modules.inz_product_activity.service.IInzProductActivityService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 商品活动表
 * @Author: jeecg-boot
 * @Date:   2025-06-21
 * @Version: V1.0
 */
@Api(tags="商品活动表")
@RestController
@RequestMapping("/inz_product_activity/inzProductActivity")
@Slf4j
public class InzProductActivityController extends JeecgController<InzProductActivity, IInzProductActivityService> {
	@Autowired
	private IInzProductActivityService inzProductActivityService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzProductActivity
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "商品活动表-分页列表查询")
	@ApiOperation(value="商品活动表-分页列表查询", notes="商品活动表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<InzProductActivity>> queryPageList(InzProductActivity inzProductActivity,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<InzProductActivity> queryWrapper = QueryGenerator.initQueryWrapper(inzProductActivity, req.getParameterMap());
		Page<InzProductActivity> page = new Page<InzProductActivity>(pageNo, pageSize);
		IPage<InzProductActivity> pageList = inzProductActivityService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param inzProductActivity
	 * @return
	 */
	@AutoLog(value = "商品活动表-添加")
	@ApiOperation(value="商品活动表-添加", notes="商品活动表-添加")
	@RequiresPermissions("inz_product_activity:inz_product_activity:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InzProductActivity inzProductActivity) {
		inzProductActivityService.save(inzProductActivity);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param inzProductActivity
	 * @return
	 */
	@AutoLog(value = "商品活动表-编辑")
	@ApiOperation(value="商品活动表-编辑", notes="商品活动表-编辑")
	@RequiresPermissions("inz_product_activity:inz_product_activity:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InzProductActivity inzProductActivity) {
		inzProductActivityService.updateById(inzProductActivity);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "商品活动表-通过id删除")
	@ApiOperation(value="商品活动表-通过id删除", notes="商品活动表-通过id删除")
	@RequiresPermissions("inz_product_activity:inz_product_activity:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzProductActivityService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "商品活动表-批量删除")
	@ApiOperation(value="商品活动表-批量删除", notes="商品活动表-批量删除")
	@RequiresPermissions("inz_product_activity:inz_product_activity:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzProductActivityService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "商品活动表-通过id查询")
	@ApiOperation(value="商品活动表-通过id查询", notes="商品活动表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<InzProductActivity> queryById(@RequestParam(name="id",required=true) String id) {
		InzProductActivity inzProductActivity = inzProductActivityService.getById(id);
		if(inzProductActivity==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzProductActivity);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param inzProductActivity
    */
    @RequiresPermissions("inz_product_activity:inz_product_activity:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzProductActivity inzProductActivity) {
        return super.exportXls(request, inzProductActivity, InzProductActivity.class, "商品活动表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("inz_product_activity:inz_product_activity:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzProductActivity.class);
    }

}
