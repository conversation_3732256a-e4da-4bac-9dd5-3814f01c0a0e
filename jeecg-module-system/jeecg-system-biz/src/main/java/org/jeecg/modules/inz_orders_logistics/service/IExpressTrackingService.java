package org.jeecg.modules.inz_orders_logistics.service;

import org.jeecg.modules.inz_orders_logistics.model.ExpressTrackDTO;

import java.util.List;
import java.util.Map;

/**
 * 快递物流查询服务接口
 */
public interface IExpressTrackingService {

    /**
     * 根据快递单号和快递公司编码查询物流轨迹
     *
     * @param expressNo 快递单号
     * @param expressCompany 快递公司编码
     * @return 物流轨迹数据
     */
    ExpressTrackDTO queryTrack(String expressNo, String expressCompany);
    
    /**
     * 根据物流订单ID查询物流轨迹
     *
     * @param logisticsId 物流订单ID
     * @return 物流轨迹数据
     */
    ExpressTrackDTO queryTrackByLogisticsId(String logisticsId);
    
    /**
     * 订阅物流轨迹推送
     *
     * @param expressNo 快递单号
     * @param expressCompany 快递公司编码
     * @param phone 手机号码
     * @param orderNo 订单编号，用于回调时识别订单
     * @return 是否订阅成功
     */
    boolean subscribeExpressTrack(String expressNo, String expressCompany, String phone, String orderNo);
    
    /**
     * 批量查询物流轨迹
     *
     * @param expressInfoList 快递信息列表，每个元素需包含 expressNo和expressCompany
     * @return 物流轨迹映射，key为快递单号，value为轨迹数据
     */
    Map<String, ExpressTrackDTO> batchQueryTrack(List<Map<String, String>> expressInfoList);
    
    /**
     * 处理快递100回调数据
     *
     * @param callbackData 回调数据
     * @return 处理结果
     */
    boolean processCallbackData(String callbackData);
} 