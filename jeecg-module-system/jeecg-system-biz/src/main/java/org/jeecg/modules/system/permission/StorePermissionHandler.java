package org.jeecg.modules.system.permission;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.SysPermissionDataRuleModel;
import org.jeecg.modules.inz_store.entity.InzStore;
import org.jeecg.modules.inz_store.service.IInzStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * 店铺数据权限处理器
 */
@Slf4j
@Component("storePermissionHandler")
public class StorePermissionHandler implements PermissionDataHandler {
    
    @Autowired
    private IInzStoreService storeService;
    
    @Override
    public String getSQLSegment(SysPermissionDataRuleModel rule, String mappingField, HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            log.warn("数据权限处理：当前用户未登录");
            return null;
        }
        
        log.info("数据权限处理器被调用，用户ID：{}，用户名：{}，规则：{}，字段：{}", 
                user.getId(), user.getUsername(), rule.getRuleName(), mappingField);
        
        // 判断是否为管理员角色
        if (isAdmin(user)) {
            log.info("用户{}是管理员，不进行数据过滤", user.getUsername());
            // 管理员不做数据过滤
            return null;
        }
        
        // 检查请求参数中是否有storeId
        String requestStoreId = request.getParameter("storeId");
        if (StringUtils.isNotBlank(requestStoreId)) {
            // 验证storeId是否存在
            InzStore requestStore = storeService.getStoreById(requestStoreId);
            if (requestStore != null) {
                log.info("使用请求参数中的storeId: {}, 店铺名称: {}", requestStoreId, requestStore.getName());
                return mappingField + " = '" + requestStoreId + "'";
            }
        }
        
        // 查询用户关联的店铺
        InzStore userStore = storeService.getStoreByUserId(user.getId());
        if (userStore != null) {
            // 返回SQL过滤条件
            String sqlSegment = mappingField + " = '" + userStore.getId() + "'";
            log.info("用户{}关联店铺ID: {}, 店铺名称: {}, 生成的SQL片段: {}", 
                    user.getUsername(), userStore.getId(), userStore.getName(), sqlSegment);
            return sqlSegment;
        } else {
            log.warn("用户{}没有关联店铺，尝试使用用户ID作为店铺ID", user.getUsername());
            
            // 尝试使用用户ID作为店铺ID查询
            InzStore userIdStore = storeService.getStoreById(user.getId());
            if (userIdStore != null) {
                log.info("找到用户ID对应的店铺: {}", userIdStore.getName());
                return mappingField + " = '" + user.getId() + "'";
            }
            
            // 都找不到，返回永假条件
            log.warn("用户{}既没有关联店铺，用户ID也不是有效店铺ID，返回空结果", user.getUsername());
            return "1=0";
        }
    }
    
    /**
     * 判断用户是否为管理员
     */
    private boolean isAdmin(LoginUser user) {
        // 根据角色编码判断
        String roleCode = user.getRoleCode();
        boolean isAdmin = StringUtils.isNotBlank(roleCode) && 
                         (roleCode.contains("admin") || roleCode.contains("ADMIN"));
        
        log.info("用户{}的角色编码: {}, 是否为管理员: {}", user.getUsername(), roleCode, isAdmin);
        return isAdmin;
    }
} 