package org.jeecg.modules.inz_orders.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_orders.entity.InzOrders;
import org.jeecg.modules.inz_orders.service.IInzOrdersService;
import org.jeecg.modules.inz_store.service.IInzStoreService;
import org.jeecg.modules.inz_users_fronts.service.IInzUsersFrontsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: 订单表
 * @Author: jeecg-boot
 * @Date: 2025-06-20
 * @Version: V1.0
 */
/*@Api(tags="订单表")*/
@RestController
@RequestMapping("/inz_orders/inzOrders")
@Slf4j
public class InzOrdersController extends JeecgController<InzOrders, IInzOrdersService> {
    @Autowired
    private IInzOrdersService inzOrdersService;

    @Autowired
    private IInzStoreService inzStoreService;

    @Autowired
    private IInzUsersFrontsService inzUsersFrontsService;

    /**
     * 分页列表查询
     *
     * @param inzOrders  查询条件
     * @param pageNo     页码
     * @param pageSize   每页记录数
     * @param storeName  店铺名称
     * @param username   用户名
     * @param nickname   用户昵称
     * @param payTime    支付时间范围（格式：yyyy-MM-dd HH:mm:ss,yyyy-MM-dd HH:mm:ss）
     * @param finishTime 完成时间范围（格式：yyyy-MM-dd HH:mm:ss,yyyy-MM-dd HH:mm:ss）
     * @param req        HTTP请求
     * @return 分页结果
     */
    //@AutoLog(value = "订单表-分页列表查询")
    /*@ApiOperation(value="订单表-分页列表查询", notes="订单表-分页列表查询")*/
    @GetMapping(value = "/list")
    public Result<IPage<InzOrders>> queryPageList(InzOrders inzOrders,
                                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                  @RequestParam(name = "storeName", required = false) String storeName,
                                                  @RequestParam(name = "username", required = false) String username,
                                                  @RequestParam(name = "nickname", required = false) String nickname,
                                                  @RequestParam(name = "payTime", required = false) String payTime,
                                                  @RequestParam(name = "finishTime", required = false) String finishTime,
                                                  HttpServletRequest req) {
        QueryWrapper<InzOrders> queryWrapper = QueryGenerator.initQueryWrapper(inzOrders, req.getParameterMap());

        // 处理店铺名称筛选
        if (StringUtils.isNotBlank(storeName)) {
            String storeId = inzStoreService.getStoreIdByName(storeName);
            if (StringUtils.isNotBlank(storeId)) {
                queryWrapper.eq("store_id", storeId);
            } else {
                // 如果找不到店铺，返回空结果
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }
        }

        // 处理用户名筛选
        if (StringUtils.isNotBlank(username)) {
            String userId = inzUsersFrontsService.getUserIdByUsername(username);
            if (StringUtils.isNotBlank(userId)) {
                queryWrapper.eq("user_id", userId);
            } else {
                // 如果找不到用户，返回空结果
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }
        }

        // 处理用户昵称筛选
        if (StringUtils.isNotBlank(nickname)) {
            String userId = inzUsersFrontsService.getUserIdByNickname(nickname);
            if (StringUtils.isNotBlank(userId)) {
                queryWrapper.eq("user_id", userId);
            } else {
                // 如果找不到用户，返回空结果
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }
        }

        // 处理支付时间范围筛选
        if (StringUtils.isNotBlank(payTime)) {
            String[] payTimeArr = payTime.split(",");
            if (payTimeArr.length == 2) {
                String startTime = payTimeArr[0].trim();
                String endTime = payTimeArr[1].trim();
                if (StringUtils.isNotBlank(startTime)) {
                    queryWrapper.ge("pay_time", startTime);
                }
                if (StringUtils.isNotBlank(endTime)) {
                    queryWrapper.le("pay_time", endTime);
                }
            }
        }

        // 处理完成时间范围筛选
        if (StringUtils.isNotBlank(finishTime)) {
            String[] finishTimeArr = finishTime.split(",");
            if (finishTimeArr.length == 2) {
                String startTime = finishTimeArr[0].trim();
                String endTime = finishTimeArr[1].trim();
                if (StringUtils.isNotBlank(startTime)) {
                    queryWrapper.ge("finish_time", startTime);
                }
                if (StringUtils.isNotBlank(endTime)) {
                    queryWrapper.le("finish_time", endTime);
                }
            }
        }

        // ====== 数据权限控制逻辑开始 ======
        org.jeecg.common.system.vo.LoginUser sysUser = (org.jeecg.common.system.vo.LoginUser) org.apache.shiro.SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null) {
            if (!isAdmin(sysUser)) {
                log.info("非管理员用户访问订单列表，进行数据权限过滤");
                String phone = sysUser.getPhone();
                String userId = null;
                if (StringUtils.isNotBlank(phone)) {
                    try {
                        userId = inzUsersFrontsService.getUserIdByPhone(phone);
                        log.info("通过手机号[{}]查询到前端用户ID: {}", phone, userId);
                    } catch (Exception e) {
                        log.warn("通过手机号查询前端用户失败: {}", e.getMessage());
                    }
                } else {
                    log.warn("后台用户[{}]没有关联手机号", sysUser.getUsername());
                }
                if (StringUtils.isNotBlank(userId)) {
                    QueryWrapper<org.jeecg.modules.inz_store.entity.InzStore> storeQueryWrapper = new QueryWrapper<>();
                    storeQueryWrapper.eq("user_id", userId);
                    org.jeecg.modules.inz_store.entity.InzStore store = inzStoreService.getOne(storeQueryWrapper);
                    if (store != null) {
                        log.info("用户[{}]关联店铺[{}]，只能查看该店铺订单", userId, store.getId());
                        queryWrapper.eq("store_id", store.getId());
                    } else {
                        log.warn("用户[{}]没有关联店铺，返回空结果", userId);
                        return Result.OK(new Page<>(pageNo, pageSize, 0));
                    }
                } else {
                    log.warn("未找到后台用户[{}]对应的前端用户，返回空结果", sysUser.getUsername());
                    return Result.OK(new Page<>(pageNo, pageSize, 0));
                }
            } else {
                log.info("管理员用户访问订单列表，不进行数据权限过滤");
            }
        }
        // ====== 数据权限控制逻辑结束 ======

        Page<InzOrders> page = new Page<InzOrders>(pageNo, pageSize);
        IPage<InzOrders> pageList = inzOrdersService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 判断用户是否为管理员
     *
     * @param user 登录用户
     * @return 是否为管理员
     */
    private boolean isAdmin(org.jeecg.common.system.vo.LoginUser user) {
        if (user == null) {
            return false;
        }
        String roleCode = user.getRoleCode();
        return StringUtils.isNotBlank(roleCode) && (roleCode.contains("admin") || roleCode.contains("ADMIN"));
    }

    /**
     * 添加
     *
     * @param inzOrders
     * @return
     */
    @AutoLog(value = "订单表-添加")
    @ApiOperation(value = "订单表-添加", notes = "订单表-添加")
    @RequiresPermissions("inz_orders:inz_order:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InzOrders inzOrders) {
        inzOrdersService.save(inzOrders);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param inzOrders
     * @return
     */
    @AutoLog(value = "订单表-编辑")
    @ApiOperation(value = "订单表-编辑", notes = "订单表-编辑")
    @RequiresPermissions("inz_orders:inz_order:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InzOrders inzOrders) {
        inzOrdersService.updateById(inzOrders);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "订单表-通过id删除")
    @ApiOperation(value = "订单表-通过id删除", notes = "订单表-通过id删除")
    @RequiresPermissions("inz_orders:inz_order:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        inzOrdersService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "订单表-批量删除")
    @ApiOperation(value = "订单表-批量删除", notes = "订单表-批量删除")
    @RequiresPermissions("inz_orders:inz_order:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzOrdersService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "订单表-通过id查询")
    @ApiOperation(value = "订单表-通过id查询", notes = "订单表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<InzOrders> queryById(@RequestParam(name = "id", required = true) String id) {
        InzOrders inzOrders = inzOrdersService.getById(id);
        if (inzOrders == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzOrders);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param inzOrders
     */
    @RequiresPermissions("inz_orders:inz_order:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzOrders inzOrders) {
        return super.exportXls(request, inzOrders, InzOrders.class, "订单表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("inz_orders:inz_order:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzOrders.class);
    }
}
