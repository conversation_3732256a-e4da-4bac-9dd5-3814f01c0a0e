import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '用户ID',
    align:"center",
    dataIndex: 'userId'
   },
   {
    title: '商品ID',
    align:"center",
    dataIndex: 'productId'
   },
   {
    title: '店铺ID',
    align:"center",
    dataIndex: 'storeId'
   },
   {
    title: '数量',
    align:"center",
    dataIndex: 'quantity'
   },
   {
    title: '价格',
    align:"center",
    dataIndex: 'price'
   },
   {
    title: '规格',
    align:"center",
    dataIndex: 'specification'
   },
   {
    title: '是否选中(0:未选中,1:已选中)',
    align:"center",
    dataIndex: 'selected'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '用户ID',
    field: 'userId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入用户ID!'},
          ];
     },
  },
  {
    label: '商品ID',
    field: 'productId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入商品ID!'},
          ];
     },
  },
  {
    label: '店铺ID',
    field: 'storeId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入店铺ID!'},
          ];
     },
  },
  {
    label: '数量',
    field: 'quantity',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入数量!'},
          ];
     },
  },
  {
    label: '价格',
    field: 'price',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入价格!'},
          ];
     },
  },
  {
    label: '规格',
    field: 'specification',
    component: 'Input',
  },
  {
    label: '是否选中(0:未选中,1:已选中)',
    field: 'selected',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入是否选中(0:未选中,1:已选中)!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  userId: {title: '用户ID',order: 0,view: 'text', type: 'string',},
  productId: {title: '商品ID',order: 1,view: 'text', type: 'string',},
  storeId: {title: '店铺ID',order: 2,view: 'text', type: 'string',},
  quantity: {title: '数量',order: 3,view: 'number', type: 'number',},
  price: {title: '价格',order: 4,view: 'number', type: 'number',},
  specification: {title: '规格',order: 5,view: 'text', type: 'string',},
  selected: {title: '是否选中(0:未选中,1:已选中)',order: 6,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}