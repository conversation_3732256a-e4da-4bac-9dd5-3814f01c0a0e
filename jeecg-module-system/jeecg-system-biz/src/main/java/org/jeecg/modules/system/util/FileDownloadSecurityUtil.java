package org.jeecg.modules.system.util;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * @Description: 文件下载安全工具类
 * @Author: jeecg-boot
 * @Date: 2025-01-24
 * @Version: V1.0
 */
@Slf4j
public class FileDownloadSecurityUtil {

    // 公开访问的文件类型（无需权限验证）
    private static final List<String> PUBLIC_FILE_TYPES = Arrays.asList(
        "jpg", "jpeg", "png", "gif", "bmp", "ico", "svg", "webp"
    );
    
    // 需要权限验证的敏感文件类型
    private static final List<String> SENSITIVE_FILE_TYPES = Arrays.asList(
        "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "csv",
        "zip", "rar", "7z", "tar", "gz"
    );
    
    // 禁止下载的文件类型
    private static final List<String> FORBIDDEN_FILE_TYPES = Arrays.asList(
        "jsp", "php", "asp", "aspx", "exe", "bat", "cmd", "sh", "py", "rb",
        "class", "jar", "war", "ear", "conf", "config", "ini", "sql"
    );
    
    // 危险路径模式
    private static final Pattern DANGEROUS_PATH_PATTERN = 
        Pattern.compile(".*(\\.\\./|\\.\\.\\\\|/etc/|/proc/|/sys/|/var/|/tmp/|/boot/|/root/|/home/<USER>", 
                       Pattern.CASE_INSENSITIVE);
    
    // 系统文件模式
    private static final Pattern SYSTEM_FILE_PATTERN = 
        Pattern.compile(".*(passwd|shadow|hosts|fstab|crontab|sudoers|ssh_config|authorized_keys).*", 
                       Pattern.CASE_INSENSITIVE);
    
    /**
     * 验证文件下载权限
     * @param filePath 文件路径
     * @param user 当前用户
     * @param request HTTP请求
     * @return 验证结果，null表示验证通过
     */
    public static String validateDownloadPermission(String filePath, LoginUser user, HttpServletRequest request) {
        if (oConvertUtils.isEmpty(filePath)) {
            return "文件路径不能为空";
        }
        
        // 1. 检查路径安全性
        String pathSecurityResult = checkPathSecurity(filePath);
        if (pathSecurityResult != null) {
            return pathSecurityResult;
        }
        
        // 2. 检查文件类型安全性
        String fileTypeResult = checkFileTypeSecurity(filePath);
        if (fileTypeResult != null) {
            return fileTypeResult;
        }
        
        // 3. 检查用户权限
        String permissionResult = checkUserPermission(filePath, user);
        if (permissionResult != null) {
            return permissionResult;
        }
        
        // 4. 记录下载日志
        logFileDownload(filePath, user, request);
        
        return null; // 验证通过
    }
    
    /**
     * 检查路径安全性
     */
    private static String checkPathSecurity(String filePath) {
        // 标准化路径
        String normalizedPath = filePath.replace("\\", "/").toLowerCase();
        
        // 检查路径遍历攻击
        if (normalizedPath.contains("../") || normalizedPath.contains("..\\")) {
            log.warn("检测到路径遍历攻击尝试：{}", filePath);
            return "非法的文件路径";
        }
        
        // 检查危险路径
        if (DANGEROUS_PATH_PATTERN.matcher(normalizedPath).matches()) {
            log.warn("检测到危险路径访问尝试：{}", filePath);
            return "禁止访问系统路径";
        }
        
        // 检查系统文件
        if (SYSTEM_FILE_PATTERN.matcher(normalizedPath).matches()) {
            log.warn("检测到系统文件访问尝试：{}", filePath);
            return "禁止访问系统文件";
        }
        
        // 检查隐藏文件
        if (normalizedPath.contains("/.") && !normalizedPath.endsWith("/.")) {
            log.warn("检测到隐藏文件访问尝试：{}", filePath);
            return "禁止访问隐藏文件";
        }
        
        return null;
    }
    
    /**
     * 检查文件类型安全性
     */
    private static String checkFileTypeSecurity(String filePath) {
        String fileExtension = getFileExtension(filePath);
        
        if (oConvertUtils.isEmpty(fileExtension)) {
            return "文件必须有扩展名";
        }
        
        // 检查是否为禁止下载的文件类型
        if (FORBIDDEN_FILE_TYPES.contains(fileExtension.toLowerCase())) {
            log.warn("检测到禁止下载的文件类型：{}, 文件：{}", fileExtension, filePath);
            return "禁止下载此类型文件：" + fileExtension;
        }
        
        return null;
    }
    
    /**
     * 检查用户权限
     */
    private static String checkUserPermission(String filePath, LoginUser user) {
        String fileExtension = getFileExtension(filePath);
        
        // 公开文件类型无需权限验证
        if (PUBLIC_FILE_TYPES.contains(fileExtension.toLowerCase())) {
            return null;
        }
        
        // 敏感文件需要登录用户
        if (SENSITIVE_FILE_TYPES.contains(fileExtension.toLowerCase())) {
            if (user == null) {
                log.warn("未登录用户尝试下载敏感文件：{}", filePath);
                return "下载此类文件需要登录";
            }
            
            // 可以在这里添加更细粒度的权限检查
            // 例如：检查用户角色、部门权限等
            
            return null;
        }
        
        // 其他文件类型需要管理员权限
        if (user == null) {
            return "需要登录后才能下载";
        }
        
        // 检查是否为管理员
        if (!isAdmin(user)) {
            log.warn("非管理员用户尝试下载文件：{}, 用户：{}", filePath, user.getUsername());
            return "权限不足，需要管理员权限";
        }
        
        return null;
    }
    
    /**
     * 检查是否为管理员
     */
    private static boolean isAdmin(LoginUser user) {
        if (user == null || user.getUserIdentity() == null) {
            return false;
        }
        
        // 检查用户身份
        return user.getUserIdentity() == 1; // 1表示管理员
    }
    
    /**
     * 记录文件下载日志
     */
    private static void logFileDownload(String filePath, LoginUser user, HttpServletRequest request) {
        String username = user != null ? user.getUsername() : "anonymous";
        String clientIp = getClientIp(request);
        String userAgent = request.getHeader("User-Agent");
        
        log.info("文件下载记录 - 文件：{}, 用户：{}, IP：{}, UserAgent：{}", 
                filePath, username, clientIp, userAgent);
        
        // 可以在这里添加更详细的审计日志记录
        // 例如：记录到数据库、发送到日志系统等
    }
    
    /**
     * 获取客户端IP
     */
    private static String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
    
    /**
     * 获取文件扩展名
     */
    private static String getFileExtension(String filePath) {
        if (oConvertUtils.isEmpty(filePath)) {
            return "";
        }
        
        int lastDotIndex = filePath.lastIndexOf(".");
        if (lastDotIndex == -1) {
            return "";
        }
        
        return filePath.substring(lastDotIndex + 1);
    }
    
    /**
     * 验证文件是否存在且可读
     * @param filePath 文件路径
     * @return 验证结果，null表示验证通过
     */
    public static String validateFileAccess(String filePath) {
        try {
            java.io.File file = new java.io.File(filePath);
            
            if (!file.exists()) {
                return "文件不存在";
            }
            
            if (!file.isFile()) {
                return "路径不是文件";
            }
            
            if (!file.canRead()) {
                return "文件不可读";
            }
            
            // 检查文件大小（防止下载过大文件）
            long fileSize = file.length();
            long maxSize = 100 * 1024 * 1024; // 100MB
            if (fileSize > maxSize) {
                log.warn("文件过大，拒绝下载：{}, 大小：{} bytes", filePath, fileSize);
                return "文件过大，无法下载";
            }
            
            return null;
        } catch (Exception e) {
            log.error("验证文件访问权限时发生异常：{}", e.getMessage());
            return "文件访问验证失败";
        }
    }
    
    /**
     * 生成安全的下载URL
     * @param filePath 文件路径
     * @param user 用户信息
     * @return 安全的下载URL
     */
    public static String generateSecureDownloadUrl(String filePath, LoginUser user) {
        // 生成临时下载令牌
        String token = generateDownloadToken(filePath, user);
        
        // 构建安全的下载URL
        try {
            return "/sys/common/secureDownload?file=" +
                   java.net.URLEncoder.encode(filePath, "UTF-8") +
                   "&token=" + token;
        } catch (java.io.UnsupportedEncodingException e) {
            log.error("URL编码失败: {}", e.getMessage());
            return "/sys/common/secureDownload?file=" + filePath + "&token=" + token;
        }
    }
    
    /**
     * 生成下载令牌
     */
    private static String generateDownloadToken(String filePath, LoginUser user) {
        String data = filePath + (user != null ? user.getId() : "anonymous") + System.currentTimeMillis();
        return org.jeecg.common.util.Md5Util.md5Encode(data, "UTF-8");
    }
}
