package org.jeecg.modules.inz_product_settlement.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Description: 商品结算
 * @Author: jeecg-boot
 * @Date: 2025-06-26
 * @Version: V1.0
 */
@Data
@TableName("inz_product_settlement")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_product_settlement对象", description="商品结算")
public class InzProductSettlement implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 结算状态枚举
     */
    public static final int STATUS_PENDING = 1;    // 待结算
    public static final int STATUS_SETTLED = 2;    // 已结算
    public static final int STATUS_CANCELLED = 3;  // 已取消

    /**
     * 结算类型枚举
     */
    public static final int TYPE_NORMAL = 1;       // 正常结算
    public static final int TYPE_REFUND = 2;       // 退款结算

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**结算编号*/
    @Excel(name = "结算编号", width = 15)
    @ApiModelProperty(value = "结算编号")
    private String settlementNo;

    /**订单ID*/
    @Excel(name = "订单ID", width = 15)
    @ApiModelProperty(value = "订单ID")
    private String orderId;

    /**订单编号*/
    @Excel(name = "订单编号", width = 15)
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**店铺ID*/
    @Excel(name = "店铺ID", width = 15)
    @ApiModelProperty(value = "店铺ID")
    private String storeId;

    /**店铺名称*/
    @Excel(name = "店铺名称", width = 15)
    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    /**商品ID*/
    @Excel(name = "商品ID", width = 15)
    @ApiModelProperty(value = "商品ID")
    private String productId;

    /**商品名称*/
    @Excel(name = "商品名称", width = 15)
    @ApiModelProperty(value = "商品名称")
    private String productName;

    /**商品编号*/
    @Excel(name = "商品编号", width = 15)
    @ApiModelProperty(value = "商品编号")
    private String productNo;

    /**订单金额*/
    @Excel(name = "订单金额", width = 15)
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    /**实付金额*/
    @Excel(name = "实付金额", width = 15)
    @ApiModelProperty(value = "实付金额")
    private BigDecimal payAmount;

    /**优惠金额*/
    @Excel(name = "优惠金额", width = 15)
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmount;

    /**平台佣金*/
    @Excel(name = "平台佣金", width = 15)
    @ApiModelProperty(value = "平台佣金")
    private BigDecimal platformCommission;

    /**佣金比例*/
    @Excel(name = "佣金比例", width = 15)
    @ApiModelProperty(value = "佣金比例")
    private BigDecimal commissionRate;

    /**店铺收入*/
    @Excel(name = "店铺收入", width = 15)
    @ApiModelProperty(value = "店铺收入")
    private BigDecimal storeIncome;

    /**退款金额*/
    @Excel(name = "退款金额", width = 15)
    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    /**结算金额*/
    @Excel(name = "结算金额", width = 15)
    @ApiModelProperty(value = "结算金额")
    private BigDecimal settlementAmount;

    /**结算状态（1-待结算，2-已结算，3-已取消）*/
    @Excel(name = "结算状态", width = 15, dicCode = "settlement_status")
    @ApiModelProperty(value = "结算状态（1-待结算，2-已结算，3-已取消）")
    private Integer settlementStatus;

    /**结算类型（1-正常结算，2-退款结算）*/
    @Excel(name = "结算类型", width = 15, dicCode = "settlement_type")
    @ApiModelProperty(value = "结算类型（1-正常结算，2-退款结算）")
    private Integer settlementType;

    /**结算时间*/
    @Excel(name = "结算时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结算时间")
    private Date settlementTime;

    /**结算批次号*/
    @Excel(name = "结算批次号", width = 15)
    @ApiModelProperty(value = "结算批次号")
    private String settlementBatchNo;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    /**备注*/
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 获取结算状态描述
     */
    public String getSettlementStatusText() {
        switch (this.settlementStatus) {
            case STATUS_PENDING:
                return "待结算";
            case STATUS_SETTLED:
                return "已结算";
            case STATUS_CANCELLED:
                return "已取消";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取结算类型描述
     */
    public String getSettlementTypeText() {
        switch (this.settlementType) {
            case TYPE_NORMAL:
                return "正常结算";
            case TYPE_REFUND:
                return "退款结算";
            default:
                return "未知类型";
        }
    }

    /**
     * 计算结算金额
     * 结算金额 = 店铺收入 = 实付金额 - 平台佣金 - 退款金额
     */
    public void calculateSettlementAmount() {
        if (this.payAmount == null) this.payAmount = BigDecimal.ZERO;
        if (this.platformCommission == null) this.platformCommission = BigDecimal.ZERO;
        if (this.refundAmount == null) this.refundAmount = BigDecimal.ZERO;

        this.storeIncome = this.payAmount.subtract(this.platformCommission).subtract(this.refundAmount);
        this.settlementAmount = this.storeIncome;
    }

    /**
     * 计算平台佣金
     * 平台佣金 = 实付金额 × 佣金比例
     */
    public void calculatePlatformCommission() {
        if (this.payAmount == null) this.payAmount = BigDecimal.ZERO;
        if (this.commissionRate == null) this.commissionRate = new BigDecimal("0.05"); // 默认5%

        this.platformCommission = this.payAmount.multiply(this.commissionRate);
    }

    /**
     * 自动计算所有金额
     */
    public void calculateAllAmounts() {
        calculatePlatformCommission();
        calculateSettlementAmount();
    }
}
