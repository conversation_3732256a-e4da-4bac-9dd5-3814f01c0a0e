<template>
    <view>
        <!--标题和返回-->
		<cu-custom :bgColor="NavBarColor" isBack :backRouterName="backRouteName">
			<block slot="backText">返回</block>
			<block slot="content">积分规则表</block>
		</cu-custom>
		 <!--表单区域-->
		<view>
			<form>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">规则编码：</text></view>
                  <input  placeholder="请输入规则编码" v-model="model.ruleCode"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">规则名称：</text></view>
                  <input  placeholder="请输入规则名称" v-model="model.ruleName"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">规则描述：</text></view>
                  <input  placeholder="请输入规则描述" v-model="model.ruleDesc"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">积分值：</text></view>
                  <input type="number" placeholder="请输入积分值" v-model="model.points"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">规则类型(1:获取规则,2:使用规则)：</text></view>
                  <input type="number" placeholder="请输入规则类型(1:获取规则,2:使用规则)" v-model="model.ruleType"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">任务类型(1:签到,2:分享,3:评价,4:关注,5:购物)：</text></view>
                  <input type="number" placeholder="请输入任务类型(1:签到,2:分享,3:评价,4:关注,5:购物)" v-model="model.taskType"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">每日限制次数(0表示不限制)：</text></view>
                  <input type="number" placeholder="请输入每日限制次数(0表示不限制)" v-model="model.dailyLimit"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">状态(0:禁用,1:启用)：</text></view>
                  <input type="number" placeholder="请输入状态(0:禁用,1:启用)" v-model="model.status"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">排序：</text></view>
                  <input type="number" placeholder="请输入排序" v-model="model.sort"/>
                </view>
              </view>
				<view class="padding">
					<button class="cu-btn block bg-blue margin-tb-sm lg" @click="onSubmit">
						<text v-if="loading" class="cuIcon-loading2 cuIconfont-spin"></text>提交
					</button>
				</view>
			</form>
		</view>
    </view>
</template>

<script>
    import myDate from '@/components/my-componets/my-date.vue'

    export default {
        name: "InzPointsRulesForm",
        components:{ myDate },
        props:{
          formData:{
              type:Object,
              default:()=>{},
              required:false
          }
        },
        data(){
            return {
				CustomBar: this.CustomBar,
				NavBarColor: this.NavBarColor,
				loading:false,
                model: {},
                backRouteName:'index',
                url: {
                  queryById: "/inz_points_rule/inzPointsRules/queryById",
                  add: "/inz_points_rule/inzPointsRules/add",
                  edit: "/inz_points_rule/inzPointsRules/edit",
                },
            }
        },
        created(){
             this.initFormData();
        },
        methods:{
           initFormData(){
               if(this.formData){
                    let dataId = this.formData.dataId;
                    this.$http.get(this.url.queryById,{params:{id:dataId}}).then((res)=>{
                        if(res.data.success){
                            console.log("表单数据",res);
                            this.model = res.data.result;
                        }
                    })
                }
            },
            onSubmit() {
                let myForm = {...this.model};
                this.loading = true;
                let url = myForm.id?this.url.edit:this.url.add;
				this.$http.post(url,myForm).then(res=>{
				   console.log("res",res)
				   this.loading = false
				   this.$Router.push({name:this.backRouteName})
				}).catch(()=>{
					this.loading = false
				});
            }
        }
    }
</script>
