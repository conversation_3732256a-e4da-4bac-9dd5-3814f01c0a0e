package org.jeecg.modules.inz_product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.inz_points_products.entity.InzPointsProducts;
import org.jeecg.modules.inz_points_products.service.IInzPointsProductsService;
import org.jeecg.modules.inz_product.entity.InzProduct;
import org.jeecg.modules.inz_product.service.IInzProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 商品定时任务服务
 * 处理商品的定时上架、下架等操作
 */
@Service
@Slf4j
public class ProductScheduledTaskServiceImpl {

    @Autowired
    private IInzProductService inzProductService;
    
    @Autowired
    private IInzPointsProductsService inzPointsProductsService;

    /**
     * 定时检查需要下架的商品
     * 每分钟执行一次
     */
    @Scheduled(cron = "0 * * * * ?")
    public void checkProductsToTakeDown() {
        log.info("开始执行商品定时下架检查...");
        Date now = new Date();
        
        try {
            // 查询设置了下架时间且时间已到的商品
            LambdaQueryWrapper<InzProduct> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(InzProduct::getStatus, InzProduct.STATUS_ONLINE)  // 上架状态
                        .isNotNull(InzProduct::getScheduledOffTime)  // 设置了下架时间
                        .le(InzProduct::getScheduledOffTime, now);  // 下架时间已到
            
            List<InzProduct> productsToTakeDown = inzProductService.list(queryWrapper);
            
            if (productsToTakeDown != null && !productsToTakeDown.isEmpty()) {
                log.info("找到{}个需要下架的商品", productsToTakeDown.size());
                
                for (InzProduct product : productsToTakeDown) {
                    try {
                        // 更新商品状态为下架
                        product.setStatus(InzProduct.STATUS_OFFLINE);
                        inzProductService.updateById(product);
                        log.info("商品[{}]({})已自动下架", product.getName(), product.getId());
                    } catch (Exception e) {
                        log.error("商品[{}]({})自动下架失败: {}", product.getName(), product.getId(), e.getMessage());
                    }
                }
            } else {
                log.debug("没有需要下架的商品");
            }
            
            // 检查积分商品定时下架
            checkPointsProductsToTakeDown(now);
            
        } catch (Exception e) {
            log.error("执行商品定时下架任务时发生错误: {}", e.getMessage());
        }
    }

    /**
     * 定时检查需要上架的商品
     * 每分钟执行一次
     */
    @Scheduled(cron = "0 * * * * ?")
    public void checkProductsToPublish() {
        log.info("开始执行商品定时上架检查...");
        Date now = new Date();
        
        try {
            // 查询设置了上架时间且时间已到的商品
            LambdaQueryWrapper<InzProduct> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(InzProduct::getStatus, InzProduct.STATUS_OFFLINE)  // 下架状态
                        .isNotNull(InzProduct::getScheduledOnTime)  // 设置了上架时间
                        .le(InzProduct::getScheduledOnTime, now);  // 上架时间已到
            
            List<InzProduct> productsToPublish = inzProductService.list(queryWrapper);
            
            if (productsToPublish != null && !productsToPublish.isEmpty()) {
                log.info("找到{}个需要上架的商品", productsToPublish.size());
                
                for (InzProduct product : productsToPublish) {
                    try {
                        // 更新商品状态为上架
                        product.setStatus(InzProduct.STATUS_ONLINE);
                        inzProductService.updateById(product);
                        log.info("商品[{}]({})已自动上架", product.getName(), product.getId());
                    } catch (Exception e) {
                        log.error("商品[{}]({})自动上架失败: {}", product.getName(), product.getId(), e.getMessage());
                    }
                }
            } else {
                log.debug("没有需要上架的商品");
            }
            
            // 检查积分商品定时上架
            checkPointsProductsToPublish(now);
            
        } catch (Exception e) {
            log.error("执行商品定时上架任务时发生错误: {}", e.getMessage());
        }
    }
    
    /**
     * 检查需要下架的积分商品
     * @param now 当前时间
     */
    private void checkPointsProductsToTakeDown(Date now) {
        try {
            // 查询设置了下架时间且时间已到的积分商品
            LambdaQueryWrapper<InzPointsProducts> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(InzPointsProducts::getStatus, InzPointsProducts.STATUS_ONLINE)  // 上架状态
                        .isNotNull(InzPointsProducts::getScheduledOffTime)  // 设置了下架时间
                        .le(InzPointsProducts::getScheduledOffTime, now);  // 下架时间已到
            
            List<InzPointsProducts> productsToTakeDown = inzPointsProductsService.list(queryWrapper);
            
            if (productsToTakeDown != null && !productsToTakeDown.isEmpty()) {
                log.info("找到{}个需要下架的积分商品", productsToTakeDown.size());
                
                for (InzPointsProducts product : productsToTakeDown) {
                    try {
                        // 更新积分商品状态为下架
                        product.setStatus(InzPointsProducts.STATUS_OFFLINE);
                        product.setOffShelfTime(now); // 记录实际下架时间
                        inzPointsProductsService.updateById(product);
                        log.info("积分商品[{}]({})已自动下架", product.getName(), product.getId());
                    } catch (Exception e) {
                        log.error("积分商品[{}]({})自动下架失败: {}", product.getName(), product.getId(), e.getMessage());
                    }
                }
            } else {
                log.debug("没有需要下架的积分商品");
            }
        } catch (Exception e) {
            log.error("执行积分商品定时下架任务时发生错误: {}", e.getMessage());
        }
    }
    
    /**
     * 检查需要上架的积分商品
     * @param now 当前时间
     */
    private void checkPointsProductsToPublish(Date now) {
        try {
            // 查询设置了上架时间且时间已到的积分商品
            LambdaQueryWrapper<InzPointsProducts> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(InzPointsProducts::getStatus, InzPointsProducts.STATUS_OFFLINE)  // 下架状态
                        .isNotNull(InzPointsProducts::getScheduledOnTime)  // 设置了上架时间
                        .le(InzPointsProducts::getScheduledOnTime, now);  // 上架时间已到
            
            List<InzPointsProducts> productsToPublish = inzPointsProductsService.list(queryWrapper);
            
            if (productsToPublish != null && !productsToPublish.isEmpty()) {
                log.info("找到{}个需要上架的积分商品", productsToPublish.size());
                
                for (InzPointsProducts product : productsToPublish) {
                    try {
                        // 更新积分商品状态为上架
                        product.setStatus(InzPointsProducts.STATUS_ONLINE);
                        product.setShelfTime(now); // 记录实际上架时间
                        inzPointsProductsService.updateById(product);
                        log.info("积分商品[{}]({})已自动上架", product.getName(), product.getId());
                    } catch (Exception e) {
                        log.error("积分商品[{}]({})自动上架失败: {}", product.getName(), product.getId(), e.getMessage());
                    }
                }
            } else {
                log.debug("没有需要上架的积分商品");
            }
        } catch (Exception e) {
            log.error("执行积分商品定时上架任务时发生错误: {}", e.getMessage());
        }
    }
} 