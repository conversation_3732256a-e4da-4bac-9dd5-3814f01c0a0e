package org.jeecg.modules.inz_points_rule.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 积分规则表
 * @Author: jeecg-boot
 * @Date:   2025-06-15
 * @Version: V1.0
 */
@Data
@TableName("inz_points_rule")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_points_rule对象", description="积分规则表")
public class InzPointsRules implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**规则编码*/
	@Excel(name = "规则编码", width = 15)
    @ApiModelProperty(value = "规则编码")
    private java.lang.String ruleCode;
	/**规则名称*/
	@Excel(name = "规则名称", width = 15)
    @ApiModelProperty(value = "规则名称")
    private java.lang.String ruleName;
	/**规则描述*/
	@Excel(name = "规则描述", width = 15)
    @ApiModelProperty(value = "规则描述")
    private java.lang.String ruleDesc;
	/**积分值*/
	@Excel(name = "积分值", width = 15)
    @ApiModelProperty(value = "积分值")
    private java.lang.Integer points;
	/**规则类型(1:获取规则,2:使用规则)*/
	@Excel(name = "规则类型(1:获取规则,2:使用规则)", width = 15)
    @ApiModelProperty(value = "规则类型(1:获取规则,2:使用规则)")
    private java.lang.Integer ruleType;
	/**任务类型(1:签到,2:分享,3:评价,4:关注,5:购物)*/
	@Excel(name = "任务类型(1:签到,2:分享,3:评价,4:关注,5:购物)", width = 15)
    @ApiModelProperty(value = "任务类型(1:签到,2:分享,3:评价,4:关注,5:购物)")
    private java.lang.Integer taskType;
	/**每日限制次数(0表示不限制)*/
	@Excel(name = "每日限制次数(0表示不限制)", width = 15)
    @ApiModelProperty(value = "每日限制次数(0表示不限制)")
    private java.lang.Integer dailyLimit;
	/**状态(0:禁用,1:启用)*/
	@Excel(name = "状态(0:禁用,1:启用)", width = 15)
    @ApiModelProperty(value = "状态(0:禁用,1:启用)")
    private java.lang.Integer status;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private java.lang.Integer sort;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**所属部门编码*/
    @ApiModelProperty(value = "所属部门编码")
    private java.lang.String sysOrgCode;
}
