import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '活动名称',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '商品ID',
    align:"center",
    dataIndex: 'productId'
   },
   {
    title: '活动标签（如限时购、爆款推荐等）',
    align:"center",
    dataIndex: 'tag'
   },
   {
    title: '活动开始时间',
    align:"center",
    dataIndex: 'startTime'
   },
   {
    title: '活动结束时间',
    align:"center",
    dataIndex: 'endTime'
   },
   {
    title: '活动价格（用于覆盖原价）',
    align:"center",
    dataIndex: 'activityPrice'
   },
   {
    title: '活动库存（为空则取商品原始库存）',
    align:"center",
    dataIndex: 'activityStock'
   },
   {
    title: '活动说明',
    align:"center",
    dataIndex: 'description'
   },
   {
    title: '是否启用（0-关闭，1-启用）',
    align:"center",
    dataIndex: 'status'
   },
   {
    title: '是否全平台可见',
    align:"center",
    dataIndex: 'globalVisible'
   },
   {
    title: '排序值',
    align:"center",
    dataIndex: 'sort'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '活动名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入活动名称!'},
          ];
     },
  },
  {
    label: '商品ID',
    field: 'productId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入商品ID!'},
          ];
     },
  },
  {
    label: '活动标签（如限时购、爆款推荐等）',
    field: 'tag',
    component: 'Input',
  },
  {
    label: '活动开始时间',
    field: 'startTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入活动开始时间!'},
          ];
     },
  },
  {
    label: '活动结束时间',
    field: 'endTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入活动结束时间!'},
          ];
     },
  },
  {
    label: '活动价格（用于覆盖原价）',
    field: 'activityPrice',
    component: 'InputNumber',
  },
  {
    label: '活动库存（为空则取商品原始库存）',
    field: 'activityStock',
    component: 'InputNumber',
  },
  {
    label: '活动说明',
    field: 'description',
    component: 'Input',
  },
  {
    label: '是否启用（0-关闭，1-启用）',
    field: 'status',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入是否启用（0-关闭，1-启用）!'},
          ];
     },
  },
  {
    label: '是否全平台可见',
    field: 'globalVisible',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入是否全平台可见!'},
          ];
     },
  },
  {
    label: '排序值',
    field: 'sort',
    component: 'InputNumber',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '活动名称',order: 0,view: 'text', type: 'string',},
  productId: {title: '商品ID',order: 1,view: 'text', type: 'string',},
  tag: {title: '活动标签（如限时购、爆款推荐等）',order: 2,view: 'text', type: 'string',},
  startTime: {title: '活动开始时间',order: 3,view: 'datetime', type: 'string',},
  endTime: {title: '活动结束时间',order: 4,view: 'datetime', type: 'string',},
  activityPrice: {title: '活动价格（用于覆盖原价）',order: 5,view: 'number', type: 'number',},
  activityStock: {title: '活动库存（为空则取商品原始库存）',order: 6,view: 'number', type: 'number',},
  description: {title: '活动说明',order: 7,view: 'text', type: 'string',},
  status: {title: '是否启用（0-关闭，1-启用）',order: 8,view: 'number', type: 'number',},
  globalVisible: {title: '是否全平台可见',order: 9,view: 'number', type: 'number',},
  sort: {title: '排序值',order: 10,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}