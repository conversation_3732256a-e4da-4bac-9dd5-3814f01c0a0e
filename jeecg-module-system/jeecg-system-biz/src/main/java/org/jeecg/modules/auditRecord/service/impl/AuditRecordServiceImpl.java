package org.jeecg.modules.auditRecord.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.after_sale.entity.AfterSale;
import org.jeecg.modules.after_sale.service.IAfterSaleService;
import org.jeecg.modules.auditRecord.entity.AuditAftersaleRelation;
import org.jeecg.modules.auditRecord.entity.AuditProductRelation;
import org.jeecg.modules.auditRecord.entity.AuditRecord;
import org.jeecg.modules.auditRecord.mapper.AuditAftersaleRelationMapper;
import org.jeecg.modules.auditRecord.mapper.AuditProductRelationMapper;
import org.jeecg.modules.auditRecord.mapper.AuditRecordMapper;
import org.jeecg.modules.auditRecord.service.IAuditRecordService;
import org.jeecg.modules.inz_product.entity.InzProduct;
import org.jeecg.modules.inz_product.service.IInzProductService;
import org.jeecg.modules.inz_store.entity.InzStore;
import org.jeecg.modules.inz_store.service.IInzStoreService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Description: 审核表
 * @Author: jeecg-boot
 * @Date:   2025-06-21
 * @Version: V1.0
 */
@Service
public class AuditRecordServiceImpl extends ServiceImpl<AuditRecordMapper, AuditRecord> implements IAuditRecordService {

    private static final Logger log = LoggerFactory.getLogger(AuditRecordServiceImpl.class);

    @Autowired
    private AuditProductRelationMapper auditProductRelationMapper;
    
    @Autowired
    private AuditAftersaleRelationMapper auditAftersaleRelationMapper;
    
    @Autowired
    private IInzProductService inzProductService;
    
    @Autowired
    private IAfterSaleService afterSaleService;

    @Override
    @Transactional
    public Result<AuditRecord> submitForAudit(String businessId, Integer auditType) {
        AuditRecord auditRecord = new AuditRecord();
        auditRecord.setBusinessId(businessId);
        auditRecord.setAuditType(auditType);
        auditRecord.setStatus(0); // 待审核
        auditRecord.setCreateTime(new Date());

        this.save(auditRecord);

        // TODO: 这里可以添加通知审核人员的逻辑，比如发送邮件或系统消息

        return Result.OK("提交审核成功", auditRecord);
    }

    @Override
    @Transactional
    public Result<AuditRecord> performAudit(String auditRecordId, Integer status, String comments) {
        // 获取审核记录
        AuditRecord auditRecord = this.getById(auditRecordId);
        if (auditRecord == null) {
            return Result.error("审核记录不存在");
        }

        if (auditRecord.getStatus() != 0) {
            return Result.error("该记录已审核，无法重复审核");
        }

        // 更新审核记录
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        auditRecord.setStatus(status);
        auditRecord.setComments(comments);
        auditRecord.setAuditorId(sysUser.getId());
        auditRecord.setAuditTime(new Date());
        this.updateById(auditRecord);

        // 根据审核类型处理不同业务
        if (auditRecord.getAuditType() == 1) { // 商品审核
            handleProductAuditResult(auditRecord);
        } else if (auditRecord.getAuditType() == 2) { // 售后审核
            handleAftersaleAuditResult(auditRecord);
        }
        
        return Result.OK("审核成功", auditRecord);
    }
    
    /**
     * 处理商品审核结果
     * @param auditRecord 审核记录
     */
    private void handleProductAuditResult(AuditRecord auditRecord) {
        // 查询关联的商品ID
        LambdaQueryWrapper<AuditProductRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AuditProductRelation::getAuditId, auditRecord.getId());
        List<AuditProductRelation> relations = auditProductRelationMapper.selectList(queryWrapper);
        
        if (relations == null || relations.isEmpty()) {
            // 没有关联的商品
            return;
        }
        
        String productId = relations.get(0).getProductId();
        InzProduct product = inzProductService.getById(productId);
        if (product == null) {
            // 商品不存在
            return;
        }
        
        // 根据审核结果更新商品状态
        if (auditRecord.getStatus() == 1) { // 审核通过
            product.setStatus(1); // 设置为上架状态
            inzProductService.updateById(product);
            
            // TODO: 发送通知给商家
            
        } else if (auditRecord.getStatus() == 2) { // 审核驳回
            product.setStatus(0); // 设置为草稿状态
            inzProductService.updateById(product);
            
            // TODO: 发送通知给商家
        }
    }
    
    /**
     * 处理售后审核结果
     * @param auditRecord 审核记录
     */
    private void handleAftersaleAuditResult(AuditRecord auditRecord) {
        // 查询关联的售后ID
        LambdaQueryWrapper<AuditAftersaleRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AuditAftersaleRelation::getAuditId, auditRecord.getId());
        List<AuditAftersaleRelation> relations = auditAftersaleRelationMapper.selectList(queryWrapper);
        
        if (relations == null || relations.isEmpty()) {
            // 没有关联的售后申请
            return;
        }
        
        String aftersaleId = relations.get(0).getAftersaleId();
        AfterSale afterSale = afterSaleService.getById(aftersaleId);
        if (afterSale == null) {
            // 售后申请不存在
            return;
        }
        
        // 根据审核结果更新售后状态
        if (auditRecord.getStatus() == 1) { // 审核通过
            afterSale.setStatus(2); // 设置为处理中状态
            afterSaleService.updateById(afterSale);
            
            // TODO: 发送通知给用户
            
        } else if (auditRecord.getStatus() == 2) { // 审核驳回
            afterSale.setStatus(5); // 设置为已拒绝状态
            afterSaleService.updateById(afterSale);
            
            // TODO: 发送通知给用户
        }
    }
    
    @Override
    @Transactional
    public Result<AuditRecord> submitProductForAudit(String productId) {
        // 检查该商品是否已有待审核记录
        AuditRecord existingRecord = getAuditRecordByProductId(productId);
        if (existingRecord != null && existingRecord.getStatus() == 0) {
            return Result.error("该商品已提交审核，请勿重复提交");
        }
        
        // 获取商品信息
        InzProduct product = inzProductService.getById(productId);
        if (product == null) {
            return Result.error("商品不存在");
        }
        
        // 创建审核记录
        AuditRecord auditRecord = new AuditRecord();
        auditRecord.setBusinessId(productId); // 业务ID为商品ID
        auditRecord.setAuditType(1); // 商品上架审核
        auditRecord.setStatus(0); // 待审核
        auditRecord.setCreateTime(new Date());
        
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        auditRecord.setSubmitter(sysUser.getRealname());
        auditRecord.setSubmitterId(sysUser.getId());
        
        // 设置商品相关信息
        auditRecord.setProductId(productId);
        auditRecord.setProductName(product.getName());
        auditRecord.setBusinessType(product.getCategory()); // 商品分类作为业务类型
        
        // 设置店铺相关信息
        auditRecord.setStoreId(product.getStoreId());
        
        // 如果有店铺ID，尝试获取店铺名称
        if (product.getStoreId() != null && !product.getStoreId().isEmpty()) {
            // 这里需要根据您的实际情况调用获取店铺信息的服务
            try {
                // 尝试从Spring上下文中获取storeService
                IInzStoreService storeService = SpringContextUtils.getBean(IInzStoreService.class);
                if (storeService != null) {
                    InzStore store = storeService.getById(product.getStoreId());
                    if (store != null) {
                        auditRecord.setStoreName(store.getName());
                    }
                }
            } catch (Exception e) {
                // 如果获取店铺信息失败，记录日志但不影响主流程
                log.error("获取店铺信息失败: " + e.getMessage());
            }
        }
        
        this.save(auditRecord);
        
        // 创建审核商品关联记录
        AuditProductRelation relation = new AuditProductRelation();
        relation.setAuditId(auditRecord.getId());
        relation.setProductId(productId);
        relation.setCreateTime(new Date());
        relation.setUpdateTime(new Date());
        
        auditProductRelationMapper.insert(relation);
        
        // TODO: 可以添加通知审核人员的逻辑
        
        return Result.OK("商品提交审核成功", auditRecord);
    }
    
    @Override
    @Transactional
    public Result<AuditRecord> submitAftersaleForAudit(String aftersaleId) {
        // 检查该售后是否已有待审核记录
        AuditRecord existingRecord = getAuditRecordByAftersaleId(aftersaleId);
        if (existingRecord != null && existingRecord.getStatus() == 0) {
            return Result.error("该售后申请已提交审核，请勿重复提交");
        }
        
        // 获取售后信息
        AfterSale afterSale = afterSaleService.getById(aftersaleId);
        if (afterSale == null) {
            return Result.error("售后申请不存在");
        }
        
        // 查询关联的商品信息
        String productId = afterSale.getOrderId(); // 临时使用订单ID，实际应该有商品ID
        InzProduct product = null;
        if (productId != null) {
            product = inzProductService.getById(productId);
        }
        
        // 创建审核记录
        AuditRecord auditRecord = new AuditRecord();
        auditRecord.setBusinessId(aftersaleId); // 业务ID为售后ID
        auditRecord.setAuditType(2); // 售后申请审核
        auditRecord.setStatus(0); // 待审核
        auditRecord.setCreateTime(new Date());
        
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        auditRecord.setSubmitter(sysUser.getRealname());
        auditRecord.setSubmitterId(sysUser.getId());
        
        // 设置售后相关信息
        auditRecord.setOrderId(afterSale.getOrderId());
        auditRecord.setAftersaleReason(afterSale.getReason());
        
        // 如果能获取到商品信息，则设置商品相关字段
        if (product != null) {
            auditRecord.setProductId(product.getId());
            auditRecord.setProductName(product.getName());
        }
        
        // 设置业务类型为售后类型的字符串表示
        if (afterSale.getType() != null) {
            String businessType;
            switch (afterSale.getType()) {
                case 1:
                    businessType = "仅退款";
                    break;
                case 2:
                    businessType = "退货退款";
                    break;
                case 3:
                    businessType = "换货";
                    break;
                default:
                    businessType = "其他售后";
            }
            auditRecord.setBusinessType(businessType);
        }
        
        // 设置店铺相关信息
        auditRecord.setStoreId(afterSale.getStoreId());
        
        // 如果有店铺ID，尝试获取店铺名称
        if (afterSale.getStoreId() != null && !afterSale.getStoreId().isEmpty()) {
            try {
                // 尝试从Spring上下文中获取storeService
                IInzStoreService storeService = SpringContextUtils.getBean(IInzStoreService.class);
                if (storeService != null) {
                    InzStore store = storeService.getById(afterSale.getStoreId());
                    if (store != null) {
                        auditRecord.setStoreName(store.getName());
                    }
                }
            } catch (Exception e) {
                // 如果获取店铺信息失败，记录日志但不影响主流程
                log.error("获取售后申请店铺信息失败: " + e.getMessage());
            }
        }
        
        this.save(auditRecord);
        
        // 创建审核售后关联记录
        AuditAftersaleRelation relation = new AuditAftersaleRelation();
        relation.setAuditId(auditRecord.getId());
        relation.setAftersaleId(aftersaleId);
        relation.setCreateTime(new Date());
        relation.setUpdateTime(new Date());
        
        auditAftersaleRelationMapper.insert(relation);
        
        // TODO: 可以添加通知审核人员的逻辑
        
        return Result.OK("售后申请提交审核成功", auditRecord);
    }
    
    @Override
    public AuditRecord getAuditRecordByProductId(String productId) {
        // 查询关联表获取审核ID
        LambdaQueryWrapper<AuditProductRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AuditProductRelation::getProductId, productId);
        List<AuditProductRelation> relations = auditProductRelationMapper.selectList(queryWrapper);
        
        if (relations != null && !relations.isEmpty()) {
            // 获取最新的一条关联记录
            AuditProductRelation relation = relations.get(0);
            return this.getById(relation.getAuditId());
        }
        
        return null;
    }
    
    @Override
    public AuditRecord getAuditRecordByAftersaleId(String aftersaleId) {
        // 查询关联表获取审核ID
        LambdaQueryWrapper<AuditAftersaleRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AuditAftersaleRelation::getAftersaleId, aftersaleId);
        List<AuditAftersaleRelation> relations = auditAftersaleRelationMapper.selectList(queryWrapper);
        
        if (relations != null && !relations.isEmpty()) {
            // 获取最新的一条关联记录
            AuditAftersaleRelation relation = relations.get(0);
            return this.getById(relation.getAuditId());
        }
        
        return null;
    }
    
    @Override
    @Transactional
    public Result<String> cancelProductAudit(String productId) {
        // 获取商品信息
        InzProduct product = inzProductService.getById(productId);
        if (product == null) {
            return Result.error("商品不存在");
        }
        
        if (product.getStatus() != 2) { // 假设2是审核中状态
            return Result.error("商品不在审核状态，无法取消审核");
        }
        
        // 获取审核记录
        AuditRecord auditRecord = getAuditRecordByProductId(productId);
        if (auditRecord == null) {
            return Result.error("未找到审核记录");
        }
        
        if (auditRecord.getStatus() != 0) { // 0是待审核状态
            return Result.error("审核已完成，无法取消");
        }
        
        // 删除审核记录和关联记录
        LambdaQueryWrapper<AuditProductRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AuditProductRelation::getProductId, productId);
        auditProductRelationMapper.delete(queryWrapper);
        
        this.removeById(auditRecord.getId());
        
        // 更新商品状态为草稿
        product.setStatus(0); // 假设0是草稿状态
        inzProductService.updateById(product);
        
        return Result.OK("取消审核成功");
    }
    
    @Override
    @Transactional
    public Result<String> cancelAftersaleAudit(String aftersaleId) {
        // 获取售后信息
        AfterSale afterSale = afterSaleService.getById(aftersaleId);
        if (afterSale == null) {
            return Result.error("售后申请不存在");
        }
        
        if (afterSale.getStatus() != 1) { // 假设1是待审核状态
            return Result.error("售后申请不在审核状态，无法取消审核");
        }
        
        // 获取审核记录
        AuditRecord auditRecord = getAuditRecordByAftersaleId(aftersaleId);
        if (auditRecord == null) {
            return Result.error("未找到审核记录");
        }

        if (auditRecord.getStatus() != 0) { // 0是待审核状态
            return Result.error("审核已完成，无法取消");
        }
        
        // 删除审核记录和关联记录
        LambdaQueryWrapper<AuditAftersaleRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AuditAftersaleRelation::getAftersaleId, aftersaleId);
        auditAftersaleRelationMapper.delete(queryWrapper);
        
        this.removeById(auditRecord.getId());
        
        // 更新售后状态为已取消
        afterSale.setStatus(6); // 假设6是已取消状态
        afterSaleService.updateById(afterSale);
        
        return Result.OK("取消审核成功");
    }
}
