package org.jeecg.modules.inz_orders.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.inz_orders.entity.InzOrders;
import org.jeecg.modules.inz_orders.mapper.InzOrdersMapper;
import org.jeecg.modules.inz_orders.service.IInzOrdersService;
import org.jeecg.modules.inz_users_fronts.service.IInzUsersFrontsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 订单表
 * @Author: jeecg-boot
 * @Date:   2025-06-20
 * @Version: V1.0
 */
@Service
public class InzOrdersServiceImpl extends ServiceImpl<InzOrdersMapper, InzOrders> implements IInzOrdersService {

    @Override
    public List<String> getOrderIdsByUserId(String userId) {
        if (StringUtils.isBlank(userId)) {
            return new ArrayList<>();
        }
        
        // 查询该用户的所有订单
        LambdaQueryWrapper<InzOrders> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InzOrders::getUserId, userId);
        queryWrapper.select(InzOrders::getId); // 只查询ID字段，提高性能
        
        // 获取订单列表并提取ID
        List<InzOrders> ordersList = this.list(queryWrapper);
        if (ordersList == null || ordersList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 提取订单ID
        return ordersList.stream()
                .map(InzOrders::getId)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getOrderIdsByStoreId(String storeId) {
        if (StringUtils.isBlank(storeId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<InzOrders> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InzOrders::getStoreId, storeId);
        List<InzOrders> orders = this.list(queryWrapper);
        List<String> orderIds = new ArrayList<>();
        for (InzOrders order : orders) {
            orderIds.add(order.getId());
        }
        return orderIds;
    }
}
