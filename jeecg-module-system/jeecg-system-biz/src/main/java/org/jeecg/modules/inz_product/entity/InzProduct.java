package org.jeecg.modules.inz_product.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 商品表
 * @Author: jeecg-boot
 * @Date:   2025-06-15
 * @Version: V1.0
 */
@Data
@TableName("inz_product")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_product对象", description="商品表")
public class InzProduct implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 商品状态枚举
     */
    public static final int STATUS_OFFLINE = 0; // 下架
    public static final int STATUS_ONLINE = 1;  // 上架
    public static final int STATUS_AUDITING = 2; // 审核中
    public static final int STATUS_WAITING_UNPACK = 3; // 待拆
    public static final int STATUS_UNPACKING = 4; // 拆卡中
    public static final int STATUS_UNPACKED = 5; // 已拆

    /**
     * 展示标签类型枚举
     */
    public static final int TAB_TYPE_FEATURED = 1; // 精选
    public static final int TAB_TYPE_ORIGINAL_BOX = 2; // 原箱
    public static final int TAB_TYPE_SINGLE_PACK = 3; // 单包
    public static final int TAB_TYPE_TRENDY = 4; // 潮玩
    public static final int TAB_TYPE_PERIPHERAL = 5; // 周边

    /**
     * 商品类型枚举
     */
    public static final int TYPE_RANDOM_CARD = 1;      // 随机卡种
    public static final int TYPE_RANDOM_PLAYER = 2;    // 随机球员
    public static final int TYPE_RANDOM_TEAM = 3;      // 随机球队
    public static final int TYPE_SELECT_TEAM_RANDOM = 4; // 选队随机
    public static final int TYPE_CUSTOM_TEAM = 5;      // 自选球队
    public static final int TYPE_TEAM_RANDOM = 6;      // 队伍随机

    /**
     * 商品筛选分类枚举
     */
    public static final int FILTER_FEATURED = 1;       // 精选
    public static final int FILTER_HOT_SALE = 2;       // 热卖
    public static final int FILTER_BASKETBALL = 3;     // 篮球
    public static final int FILTER_FOOTBALL = 4;       // 足球
    public static final int FILTER_COMPREHENSIVE = 5;  // 综合收藏
    public static final int FILTER_OTHER_SPORTS = 6;   // 其他运动

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**商品名称*/
	@Excel(name = "商品名称", width = 15)
    @ApiModelProperty(value = "商品名称")
    private java.lang.String name;
	/**商品编号*/
	@Excel(name = "商品编号", width = 15)
    @ApiModelProperty(value = "商品编号")
    private java.lang.String productNo;
	/**店铺ID*/
	@Excel(name = "店铺ID", width = 15)
    @ApiModelProperty(value = "店铺ID")
    private java.lang.String storeId;
	/**商品主图*/
	@Excel(name = "商品主图", width = 15)
    @ApiModelProperty(value = "商品主图")
    private java.lang.String mainImage;
	/**商品图片，多个逗号分隔*/
	@Excel(name = "商品图片，多个逗号分隔", width = 15)
    @ApiModelProperty(value = "商品图片，多个逗号分隔")
    private java.lang.String images;
	/**商品价格*/
	@Excel(name = "商品价格", width = 15)
    @ApiModelProperty(value = "商品价格")
    private java.math.BigDecimal price;
	/**商品原价*/
	@Excel(name = "商品原价", width = 15)
    @ApiModelProperty(value = "商品原价")
    private java.math.BigDecimal originalPrice;
	/**商品库存*/
	@Excel(name = "商品库存", width = 15)
    @ApiModelProperty(value = "商品库存")
    private java.lang.Integer stock;
	/**商品销量*/
	@Excel(name = "商品销量", width = 15)
    @ApiModelProperty(value = "商品销量")
    private java.lang.Integer sales;
	/**商品状态（0-下架，1-上架，2-待拆，3-拆卡中，4-已拆）*/
	@Excel(name = "商品状态（0-下架，1-上架，2-待拆，3-拆卡中，4-已拆）", width = 15)
    @ApiModelProperty(value = "商品状态（0-下架，1-上架，2-待拆，3-拆卡中，4-已拆）")
    private java.lang.Integer status;
	/**商品类型（1-随机卡种，2-随机球员，3-随机球队，4-选队随机，5-自选球队，6-队伍随机）*/
	@Excel(name = "商品类型（1-随机卡种，2-随机球员，3-随机球队，4-选队随机，5-自选球队，6-队伍随机）", width = 15)
    @ApiModelProperty(value = "商品类型（1-随机卡种，2-随机球员，3-随机球队，4-选队随机，5-自选球队，6-队伍随机）")
    private java.lang.Integer type;
	/**商品描述*/
	@Excel(name = "商品描述", width = 15)
    @ApiModelProperty(value = "商品描述")
    private java.lang.String description;
	/**商品详情*/
	@Excel(name = "商品详情", width = 15)
    @ApiModelProperty(value = "商品详情")
    private java.lang.String detail;
	/**商品规格（JSON格式）*/
	@Excel(name = "商品规格（JSON格式）", width = 15)
    @ApiModelProperty(value = "商品规格（JSON格式）")
    private java.lang.String specifications;
	/**商品分类*/
	@Excel(name = "商品分类", width = 15)
    @ApiModelProperty(value = "商品分类")
    private java.lang.String category;
	/**商品标签，逗号分隔*/
	@Excel(name = "商品标签，逗号分隔", width = 15)
    @ApiModelProperty(value = "商品标签，逗号分隔")
    private java.lang.String tags;
	/**年份*/
	@Excel(name = "年份", width = 15)
    @ApiModelProperty(value = "年份")
    private java.lang.String year;
	/**球队*/
	@Excel(name = "球队", width = 15)
    @ApiModelProperty(value = "球队")
    private java.lang.String team;
	/**球员*/
	@Excel(name = "球员", width = 15)
    @ApiModelProperty(value = "球员")
    private java.lang.String player;
	/**卡片评级*/
	@Excel(name = "卡片评级", width = 15)
    @ApiModelProperty(value = "卡片评级")
    private java.lang.String grade;
	/**评分*/
	@Excel(name = "评分", width = 15)
    @ApiModelProperty(value = "评分")
    private java.lang.Double rating;
	/**限量编号（如1/99）*/
	@Excel(name = "限量编号（如1/99）", width = 15)
    @ApiModelProperty(value = "限量编号（如1/99）")
    private java.lang.String limitedNumber;
	/**是否有签名（0-无，1-有）*/
	@Excel(name = "是否有签名（0-无，1-有）", width = 15)
    @ApiModelProperty(value = "是否有签名（0-无，1-有）")
    private java.lang.Integer hasSigned;
	/**是否有球衣碎片（0-无，1-有）*/
	@Excel(name = "是否有球衣碎片（0-无，1-有）", width = 15)
    @ApiModelProperty(value = "是否有球衣碎片（0-无，1-有）")
    private java.lang.Integer hasJerseyPatch;
	/**卡片类型*/
	@Excel(name = "卡片类型", width = 15)
    @ApiModelProperty(value = "卡片类型（如：折射卡、金属雕刻卡、3D全息卡等）")
    private java.lang.String cardType;
	/**运费模板ID*/
	@Excel(name = "运费模板ID", width = 15)
    @ApiModelProperty(value = "运费模板ID")
    private java.lang.String freightTemplateId;
	/**是否包邮（0-否，1-是）*/
	@Excel(name = "是否包邮（0-否，1-是）", width = 15)
    @ApiModelProperty(value = "是否包邮（0-否，1-是）")
    private java.lang.Integer freeShipping;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private java.lang.Integer sort;
	/**当前助力值*/
    @Excel(name = "当前助力值", width = 15)
    @ApiModelProperty(value = "当前助力值")
    private Integer currentSupport;
	/**目标助力值*/
    @Excel(name = "目标助力值", width = 15)
    @ApiModelProperty(value = "目标助力值")
    private Integer targetSupport;
	/**助力截止时间*/
    @Excel(name = "助力截止时间", width = 15)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "助力截止时间")
    private Date supportEndTime;
	/**直播ID*/
    @Excel(name = "直播ID", width = 15)
    @ApiModelProperty(value = "直播ID")
    private String liveId;
	/**是否为特色商品: 0-否, 1-是*/
    @Excel(name = "是否为特色商品", width = 15)
    @ApiModelProperty(value = "是否为特色商品: 0-否, 1-是")
    private Integer isFeatured;
	/**差值助力*/
    @TableField(exist = false)
    @ApiModelProperty(value = "差值助力")
    private Integer remainingSupport;
	/**卡片系列*/
    @Excel(name = "卡片系列", width = 15)
    @ApiModelProperty(value = "卡片系列")
    private String cardSeries;
	/**卡片编号*/
    @Excel(name = "卡片编号", width = 15)
    @ApiModelProperty(value = "卡片编号")
    private String cardNumber;
	/**评级公司*/
    @Excel(name = "评级公司", width = 15)
    @ApiModelProperty(value = "评级公司")
    private String gradingCompany;
	/**评级序列号*/
    @Excel(name = "评级序列号", width = 15)
    @ApiModelProperty(value = "评级序列号")
    private String gradingSerial;
	/**市场价格区间*/
    @Excel(name = "市场价格区间", width = 15)
    @ApiModelProperty(value = "市场价格区间")
    private String marketPriceRange;
	/**稀有度级别*/
    @Excel(name = "稀有度级别", width = 15)
    @ApiModelProperty(value = "稀有度级别")
    private String rarityLevel;
	/**卡片特性，如签名版、限量版等*/
    @Excel(name = "卡片特性", width = 15)
    @ApiModelProperty(value = "卡片特性，如签名版、限量版等")
    private String cardFeatures;
	/**视频链接*/
    @Excel(name = "视频链接", width = 15)
    @ApiModelProperty(value = "视频链接")
    private String videoUrl;
	/**积分价格*/
    @Excel(name = "积分价格", width = 15)
    @ApiModelProperty(value = "积分价格")
    private Integer pointsPrice;
	/**支付类型（1-现金支付，2-积分支付）*/
    @Excel(name = "支付类型", width = 15)
    @ApiModelProperty(value = "支付类型（1-现金支付，2-积分支付）")
    private Integer paymentType;
	/**展示标签（1-精选，2-原箱，3-单包，4-潮玩，5-周边）*/
    @Excel(name = "展示标签", width = 15)
    @ApiModelProperty(value = "展示标签（1-精选，2-原箱，3-单包，4-潮玩，5-周边）")
    private Integer tabType;
	/**须知事项*/
    @Excel(name = "须知事项", width = 15)
    @ApiModelProperty(value = "须知事项")
    private String notice;
	/**价格说明*/
    @Excel(name = "价格说明", width = 15)
    @ApiModelProperty(value = "价格说明")
    private String priceDescription;
	/**助力说明*/
    @Excel(name = "助力说明", width = 15)
    @ApiModelProperty(value = "助力说明")
    private String helpDescription;
	/**特别提示*/
    @Excel(name = "特别提示", width = 15)
    @ApiModelProperty(value = "特别提示")
    private String specialTips;
	/**发行方*/
    @Excel(name = "发行方", width = 15)
    @ApiModelProperty(value = "发行方")
    private String publisher;
	/**拼团编号*/
    @Excel(name = "拼团编号", width = 15)
    @ApiModelProperty(value = "拼团编号")
    private String groupId;
	/**产品特色*/
    @Excel(name = "产品特色", width = 15)
    @ApiModelProperty(value = "产品特色")
    private String features;
	/**产品配置*/
    @Excel(name = "产品配置", width = 15)
    @ApiModelProperty(value = "产品配置")
    private String configuration;
	/**设计预览*/
    @Excel(name = "设计预览", width = 15)
    @ApiModelProperty(value = "设计预览")
    private String designPreview;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
    
    /**定时上架时间*/
    @Excel(name = "定时上架时间", width = 20)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "定时上架时间")
    private java.util.Date scheduledOnTime;
    
    /**定时下架时间*/
    @Excel(name = "定时下架时间", width = 20)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "定时下架时间")
    private java.util.Date scheduledOffTime;
    
    /**起售数量*/
    @Excel(name = "起售数量", width = 15)
    @ApiModelProperty(value = "起售数量")
    private java.lang.Integer minPurchaseQty;
    
    /**是否启用限购(0-否，1-是)*/
    @Excel(name = "是否启用限购", width = 15)
    @ApiModelProperty(value = "是否启用限购(0-否，1-是)")
    private java.lang.Integer purchaseLimitEnabled;
    
    /**每人限购数量*/
    @Excel(name = "每人限购数量", width = 15)
    @ApiModelProperty(value = "每人限购数量")
    private java.lang.Integer purchaseLimitQty;
    
    /**是否顺丰包邮(0-否，1-是)*/
    @Excel(name = "是否顺丰包邮", width = 15)
    @ApiModelProperty(value = "是否顺丰包邮(0-否，1-是)")
    private java.lang.Integer hasSfFreeShipping;
    
    /**是否假一赔十(0-否，1-是)*/
    @Excel(name = "是否假一赔十", width = 15)
    @ApiModelProperty(value = "是否假一赔十(0-否，1-是)")
    private java.lang.Integer hasCompensation;
    
    /**是否24小时发货(0-否，1-是)*/
    @Excel(name = "是否24小时发货", width = 15)
    @ApiModelProperty(value = "是否24小时发货(0-否，1-是)")
    private java.lang.Integer hasFastDelivery;
    
    /**是否预售商品(0-否，1-是)*/
    @Excel(name = "是否预售商品", width = 15)
    @ApiModelProperty(value = "是否预售商品(0-否，1-是)")
    private java.lang.Integer isPresale;
    
    /**预售发货时间*/
    @Excel(name = "预售发货时间", width = 20)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "预售发货时间")
    private java.util.Date presaleDeliveryTime;
    
    /**付款后发货天数*/
    @Excel(name = "付款后发货天数", width = 15)
    @ApiModelProperty(value = "付款后发货天数")
    private java.lang.Integer presaleDaysAfterPayment;
    
    /**商品品牌*/
    @Excel(name = "商品品牌", width = 15)
    @ApiModelProperty(value = "商品品牌")
    private java.lang.String brand;
    
    /**商品系列*/
    @Excel(name = "商品系列", width = 15)
    @ApiModelProperty(value = "商品系列")
    private java.lang.String series;
    
    /**商品卖点*/
    @Excel(name = "商品卖点", width = 15)
    @ApiModelProperty(value = "商品卖点")
    private java.lang.String sellingPoint;
    
    /**成本价*/
    @Excel(name = "成本价", width = 15)
    @ApiModelProperty(value = "成本价")
    private java.math.BigDecimal costPrice;
    
    /**运费类型（1-包邮，2-统一邮费）*/
    @Excel(name = "运费类型", width = 15)
    @ApiModelProperty(value = "运费类型（1-包邮，2-统一邮费）")
    private java.lang.Integer shippingType;
    
    /**统一邮费金额*/
    @Excel(name = "统一邮费金额", width = 15)
    @ApiModelProperty(value = "统一邮费金额")
    private java.math.BigDecimal shippingFee;
    
    /**售后服务（多个用逗号分隔）*/
    @Excel(name = "售后服务", width = 15)
    @ApiModelProperty(value = "售后服务（多个用逗号分隔）")
    private java.lang.String afterSaleServices;
    
    /**开售时间类型（1-立即开售，2-定时开售，3-手动上架）*/
    @Excel(name = "开售时间类型", width = 15)
    @ApiModelProperty(value = "开售时间类型（1-立即开售，2-定时开售，3-手动上架）")
    private java.lang.Integer saleTimeType;
    
    /**定时开售时间*/
    @Excel(name = "定时开售时间", width = 20)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "定时开售时间")
    private java.util.Date scheduledSaleTime;
    
    /**是否定时下架（0-否，1-是）*/
    @Excel(name = "是否定时下架", width = 15)
    @ApiModelProperty(value = "是否定时下架（0-否，1-是）")
    private java.lang.Integer autoOffShelf;
    
    /**定时下架时间*/
    @Excel(name = "定时下架时间", width = 20)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "定时下架时间")
    private java.util.Date scheduledOffShelfTime;
    
    /**是否限购（0-否，1-是）*/
    @Excel(name = "是否限购", width = 15)
    @ApiModelProperty(value = "是否限购（0-否，1-是）")
    private java.lang.Integer isLimitPurchase;
    
    /**限购数量*/
    @Excel(name = "限购数量", width = 15)
    @ApiModelProperty(value = "限购数量")
    private java.lang.Integer limitPurchaseQuantity;
    
    /**起售数量*/
    @Excel(name = "起售数量", width = 15)
    @ApiModelProperty(value = "起售数量")
    private java.lang.Integer minPurchaseQuantity;

    /**商品筛选分类（1-精选，2-热卖，3-篮球，4-足球，5-综合收藏，6-其他运动）*/
    @Excel(name = "商品筛选分类", width = 15)
    @ApiModelProperty(value = "商品筛选分类（1-精选，2-热卖，3-篮球，4-足球，5-综合收藏，6-其他运动）")
    private java.lang.Integer filter;

    /**
     * 计算剩余助力值
     */
    public Integer getRemainingSupport() {
        if (targetSupport == null || currentSupport == null) {
            return null;
        }
        return Math.max(0, targetSupport - currentSupport);
    }
}
