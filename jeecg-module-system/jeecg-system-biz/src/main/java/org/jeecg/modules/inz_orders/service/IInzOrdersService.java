package org.jeecg.modules.inz_orders.service;

import java.util.List;
import org.jeecg.modules.inz_orders.entity.InzOrders;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 订单表
 * @Author: jeecg-boot
 * @Date:   2025-06-20
 * @Version: V1.0
 */
public interface IInzOrdersService extends IService<InzOrders> {
    /**
     * 根据用户ID获取订单ID列表
     * @param userId 用户ID
     * @return 订单ID列表
     */
    List<String> getOrderIdsByUserId(String userId);

    /**
     * 通过店铺ID查询订单ID列表
     * @param storeId 店铺ID
     * @return 订单ID列表
     */
    java.util.List<String> getOrderIdsByStoreId(String storeId);
}
