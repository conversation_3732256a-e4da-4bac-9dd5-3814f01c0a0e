<template>
    <view>
        <!--标题和返回-->
		<cu-custom :bgColor="NavBarColor" isBack :backRouterName="backRouteName">
			<block slot="backText">返回</block>
			<block slot="content">商品表</block>
		</cu-custom>
		 <!--表单区域-->
		<view>
			<form>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品名称：</text></view>
                  <input  placeholder="请输入商品名称" v-model="model.name"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品编号：</text></view>
                  <input  placeholder="请输入商品编号" v-model="model.productNo"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">店铺ID：</text></view>
                  <input  placeholder="请输入店铺ID" v-model="model.storeId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品主图：</text></view>
                  <input  placeholder="请输入商品主图" v-model="model.mainImage"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品图片，多个逗号分隔：</text></view>
                  <input  placeholder="请输入商品图片，多个逗号分隔" v-model="model.images"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品价格：</text></view>
                  <input type="number" placeholder="请输入商品价格" v-model="model.price"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品原价：</text></view>
                  <input type="number" placeholder="请输入商品原价" v-model="model.originalPrice"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品库存：</text></view>
                  <input type="number" placeholder="请输入商品库存" v-model="model.stock"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品销量：</text></view>
                  <input type="number" placeholder="请输入商品销量" v-model="model.sales"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品状态（0-下架，1-上架）：</text></view>
                  <input type="number" placeholder="请输入商品状态（0-下架，1-上架）" v-model="model.status"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品类型（1-普通商品，2-限量版，3-签名版）：</text></view>
                  <input type="number" placeholder="请输入商品类型（1-普通商品，2-限量版，3-签名版）" v-model="model.type"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品描述：</text></view>
                  <input  placeholder="请输入商品描述" v-model="model.description"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品详情：</text></view>
                  <input  placeholder="请输入商品详情" v-model="model.detail"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品规格（JSON格式）：</text></view>
                  <input  placeholder="请输入商品规格（JSON格式）" v-model="model.specifications"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品分类：</text></view>
                  <input  placeholder="请输入商品分类" v-model="model.category"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品标签，逗号分隔：</text></view>
                  <input  placeholder="请输入商品标签，逗号分隔" v-model="model.tags"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">年份：</text></view>
                  <input  placeholder="请输入年份" v-model="model.year"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">球队：</text></view>
                  <input  placeholder="请输入球队" v-model="model.team"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">球员：</text></view>
                  <input  placeholder="请输入球员" v-model="model.player"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">卡片评级：</text></view>
                  <input  placeholder="请输入卡片评级" v-model="model.grade"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">评分：</text></view>
                  <input type="number" placeholder="请输入评分" v-model="model.rating"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">限量编号（如1/99）：</text></view>
                  <input  placeholder="请输入限量编号（如1/99）" v-model="model.limitedNumber"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">是否有签名（0-无，1-有）：</text></view>
                  <input type="number" placeholder="请输入是否有签名（0-无，1-有）" v-model="model.hasSigned"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">是否有球衣碎片（0-无，1-有）：</text></view>
                  <input type="number" placeholder="请输入是否有球衣碎片（0-无，1-有）" v-model="model.hasJerseyPatch"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">卡片类型：</text></view>
                  <input  placeholder="请输入卡片类型" v-model="model.cardType"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">运费模板ID：</text></view>
                  <input  placeholder="请输入运费模板ID" v-model="model.freightTemplateId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">是否包邮（0-否，1-是）：</text></view>
                  <input type="number" placeholder="请输入是否包邮（0-否，1-是）" v-model="model.freeShipping"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">排序：</text></view>
                  <input type="number" placeholder="请输入排序" v-model="model.sort"/>
                </view>
              </view>
				<view class="padding">
					<button class="cu-btn block bg-blue margin-tb-sm lg" @click="onSubmit">
						<text v-if="loading" class="cuIcon-loading2 cuIconfont-spin"></text>提交
					</button>
				</view>
			</form>
		</view>
    </view>
</template>

<script>
    import myDate from '@/components/my-componets/my-date.vue'

    export default {
        name: "InzProductForm",
        components:{ myDate },
        props:{
          formData:{
              type:Object,
              default:()=>{},
              required:false
          }
        },
        data(){
            return {
				CustomBar: this.CustomBar,
				NavBarColor: this.NavBarColor,
				loading:false,
                model: {},
                backRouteName:'index',
                url: {
                  queryById: "/inz_product/inzProduct/queryById",
                  add: "/inz_product/inzProduct/add",
                  edit: "/inz_product/inzProduct/edit",
                },
            }
        },
        created(){
             this.initFormData();
        },
        methods:{
           initFormData(){
               if(this.formData){
                    let dataId = this.formData.dataId;
                    this.$http.get(this.url.queryById,{params:{id:dataId}}).then((res)=>{
                        if(res.data.success){
                            console.log("表单数据",res);
                            this.model = res.data.result;
                        }
                    })
                }
            },
            onSubmit() {
                let myForm = {...this.model};
                this.loading = true;
                let url = myForm.id?this.url.edit:this.url.add;
				this.$http.post(url,myForm).then(res=>{
				   console.log("res",res)
				   this.loading = false
				   this.$Router.push({name:this.backRouteName})
				}).catch(()=>{
					this.loading = false
				});
            }
        }
    }
</script>
