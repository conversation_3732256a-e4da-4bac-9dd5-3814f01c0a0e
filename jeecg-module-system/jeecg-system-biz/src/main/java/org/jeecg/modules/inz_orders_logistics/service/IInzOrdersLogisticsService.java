package org.jeecg.modules.inz_orders_logistics.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.inz_orders_logistics.entity.InzOrdersLogistics;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * @Description: 订单物流信息表
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
public interface IInzOrdersLogisticsService extends IService<InzOrdersLogistics> {
    /**
     * 根据店铺ID查询订单物流信息
     * @param page 分页参数
     * @param logistics 查询条件
     * @param storeId 店铺ID
     * @param parameterMap 请求参数
     * @return 分页结果
     */
    IPage<InzOrdersLogistics> queryLogisticsByStoreId(Page<InzOrdersLogistics> page,
                                                    InzOrdersLogistics logistics,
                                                    String storeId,
                                                    Map<String, String[]> parameterMap);
    
    /**
     * 更新物流信息
     * @param logistics 物流实体
     * @return 操作结果
     */
    Result<String> updateLogistics(InzOrdersLogistics logistics);
    
    /**
     * 根据订单ID查询物流信息
     * @param orderId 订单ID
     * @return 物流信息
     */
    InzOrdersLogistics getLogisticsByOrderId(String orderId);
}
