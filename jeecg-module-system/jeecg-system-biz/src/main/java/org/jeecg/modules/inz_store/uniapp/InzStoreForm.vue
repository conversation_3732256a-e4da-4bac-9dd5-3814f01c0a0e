<template>
    <view>
        <!--标题和返回-->
		<cu-custom :bgColor="NavBarColor" isBack :backRouterName="backRouteName">
			<block slot="backText">返回</block>
			<block slot="content">店铺表</block>
		</cu-custom>
		 <!--表单区域-->
		<view>
			<form>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">店主用户id：</text></view>
                  <input  placeholder="请输入店主用户id" v-model="model.userId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">店铺名称：</text></view>
                  <input  placeholder="请输入店铺名称" v-model="model.name"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">店铺头像：</text></view>
                  <input  placeholder="请输入店铺头像" v-model="model.avatar"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">店铺banner：</text></view>
                  <input  placeholder="请输入店铺banner" v-model="model.banner"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">店铺描述：</text></view>
                  <input  placeholder="请输入店铺描述" v-model="model.description"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">店铺公告：</text></view>
                  <input  placeholder="请输入店铺公告" v-model="model.announcement"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">营业执照：</text></view>
                  <input  placeholder="请输入营业执照" v-model="model.businessLicense"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">联系电话：</text></view>
                  <input  placeholder="请输入联系电话" v-model="model.phone"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">省份：</text></view>
                  <input  placeholder="请输入省份" v-model="model.province"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">城市：</text></view>
                  <input  placeholder="请输入城市" v-model="model.city"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">区县：</text></view>
                  <input  placeholder="请输入区县" v-model="model.district"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">详细地址：</text></view>
                  <input  placeholder="请输入详细地址" v-model="model.address"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">店铺分类标签，逗号分隔：</text></view>
                  <input  placeholder="请输入店铺分类标签，逗号分隔" v-model="model.tags"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">是否开通直播（0-未开通，1-已开通）：</text></view>
                  <input type="number" placeholder="请输入是否开通直播（0-未开通，1-已开通）" v-model="model.liveEnabled"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">店铺等级：</text></view>
                  <input type="number" placeholder="请输入店铺等级" v-model="model.level"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">店铺积分：</text></view>
                  <input type="number" placeholder="请输入店铺积分" v-model="model.points"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">状态(0-关闭,1-营业中)：</text></view>
                  <input type="number" placeholder="请输入状态(0-关闭,1-营业中)" v-model="model.status"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">店铺类型（1-个人，2-企业）：</text></view>
                  <input type="number" placeholder="请输入店铺类型（1-个人，2-企业）" v-model="model.type"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">认证状态（0-未认证，1-已认证）：</text></view>
                  <input type="number" placeholder="请输入认证状态（0-未认证，1-已认证）" v-model="model.verified"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">粉丝数：</text></view>
                  <input type="number" placeholder="请输入粉丝数" v-model="model.fanscount"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品数量：</text></view>
                  <input type="number" placeholder="请输入商品数量" v-model="model.productsCount"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">总销量：</text></view>
                  <input type="number" placeholder="请输入总销量" v-model="model.salesCount"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">店铺评分：</text></view>
                  <input type="number" placeholder="请输入店铺评分" v-model="model.rating"/>
                </view>
              </view>
				<view class="padding">
					<button class="cu-btn block bg-blue margin-tb-sm lg" @click="onSubmit">
						<text v-if="loading" class="cuIcon-loading2 cuIconfont-spin"></text>提交
					</button>
				</view>
			</form>
		</view>
    </view>
</template>

<script>
    import myDate from '@/components/my-componets/my-date.vue'

    export default {
        name: "InzStoreForm",
        components:{ myDate },
        props:{
          formData:{
              type:Object,
              default:()=>{},
              required:false
          }
        },
        data(){
            return {
				CustomBar: this.CustomBar,
				NavBarColor: this.NavBarColor,
				loading:false,
                model: {},
                backRouteName:'index',
                url: {
                  queryById: "/inz_store/inzStore/queryById",
                  add: "/inz_store/inzStore/add",
                  edit: "/inz_store/inzStore/edit",
                },
            }
        },
        created(){
             this.initFormData();
        },
        methods:{
           initFormData(){
               if(this.formData){
                    let dataId = this.formData.dataId;
                    this.$http.get(this.url.queryById,{params:{id:dataId}}).then((res)=>{
                        if(res.data.success){
                            console.log("表单数据",res);
                            this.model = res.data.result;
                        }
                    })
                }
            },
            onSubmit() {
                let myForm = {...this.model};
                this.loading = true;
                let url = myForm.id?this.url.edit:this.url.add;
				this.$http.post(url,myForm).then(res=>{
				   console.log("res",res)
				   this.loading = false
				   this.$Router.push({name:this.backRouteName})
				}).catch(()=>{
					this.loading = false
				});
            }
        }
    }
</script>
