package org.jeecg.modules.inz_announcements.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.aspect.annotation.PermissionData;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_announcements.entity.InzAnnouncement;
import org.jeecg.modules.inz_announcements.service.IInzAnnouncementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 平台公告管理
 * @Author: jeecg-boot
 * @Date: 2025-01-24
 * @Version: V1.0
 */
@Api(tags = "平台公告管理")
@RestController
@RequestMapping("/inz_announcements/inzAnnouncement")
@Slf4j
public class InzAnnouncementController extends JeecgController<InzAnnouncement, IInzAnnouncementService> {

    @Autowired
    private IInzAnnouncementService inzAnnouncementService;

    /**
     * 分页列表查询
     *
     * @param inzAnnouncement
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "平台公告管理-分页列表查询")
    @ApiOperation(value = "平台公告管理-分页列表查询", notes = "平台公告管理-分页列表查询")
    @GetMapping(value = "/list")
    @PermissionData(pageComponent = "inz_announcements/InzAnnouncementList")
    public Result<IPage<InzAnnouncement>> queryPageList(InzAnnouncement inzAnnouncement,
                                                        @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                        @RequestParam(name = "type", required = false) Integer type,
                                                        HttpServletRequest req) {
        QueryWrapper<InzAnnouncement> queryWrapper = QueryGenerator.initQueryWrapper(inzAnnouncement, req.getParameterMap());
        if (type != null) {
            queryWrapper.eq("type", type);
        }
        Page<InzAnnouncement> page = new Page<InzAnnouncement>(pageNo, pageSize);
        IPage<InzAnnouncement> pageList = inzAnnouncementService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param inzAnnouncement
     * @return
     */
    @AutoLog(value = "平台公告管理-添加")
    @ApiOperation(value = "平台公告管理-添加", notes = "平台公告管理-添加")
    @RequiresPermissions("inz_announcements:inz_announcement:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InzAnnouncement inzAnnouncement) {
        // 设置默认值
        if (inzAnnouncement.getStatus() == null) {
            inzAnnouncement.setStatus(0); // 默认草稿状态
        }
        if (inzAnnouncement.getIsTop() == null) {
            inzAnnouncement.setIsTop(0); // 默认不置顶
        }
        if (inzAnnouncement.getPriority() == null) {
            inzAnnouncement.setPriority(0); // 默认优先级
        }
        if (inzAnnouncement.getReadCount() == null) {
            inzAnnouncement.setReadCount(0); // 默认阅读次数
        }
        if (inzAnnouncement.getSendNotification() == null) {
            inzAnnouncement.setSendNotification(0); // 默认不发送通知
        }
        if (inzAnnouncement.getTargetUserType() == null) {
            inzAnnouncement.setTargetUserType(0); // 默认全部用户
        }

        inzAnnouncementService.save(inzAnnouncement);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param inzAnnouncement
     * @return
     */
    @AutoLog(value = "平台公告管理-编辑")
    @ApiOperation(value = "平台公告管理-编辑", notes = "平台公告管理-编辑")
    @RequiresPermissions("inz_announcements:inz_announcement:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InzAnnouncement inzAnnouncement) {
        inzAnnouncementService.updateById(inzAnnouncement);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "平台公告管理-通过id删除")
    @ApiOperation(value = "平台公告管理-通过id删除", notes = "平台公告管理-通过id删除")
    @RequiresPermissions("inz_announcements:inz_announcement:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        inzAnnouncementService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "平台公告管理-批量删除")
    @ApiOperation(value = "平台公告管理-批量删除", notes = "平台公告管理-批量删除")
    @RequiresPermissions("inz_announcements:inz_announcement:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzAnnouncementService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "平台公告管理-通过id查询")
    @ApiOperation(value = "平台公告管理-通过id查询", notes = "平台公告管理-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<InzAnnouncement> queryById(@RequestParam(name = "id", required = true) String id) {
        InzAnnouncement inzAnnouncement = inzAnnouncementService.getById(id);
        if (inzAnnouncement == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzAnnouncement);
    }

    /**
     * 发布公告
     *
     * @param id
     * @return
     */
    @AutoLog(value = "平台公告管理-发布公告")
    @ApiOperation(value = "发布公告", notes = "发布公告")
    @RequiresPermissions("inz_announcements:inz_announcement:publish")
    @PostMapping(value = "/publish")
    public Result<String> publish(@RequestParam(name = "id", required = true) String id) {
        boolean success = inzAnnouncementService.publishAnnouncement(id);
        if (success) {
            return Result.OK("发布成功!");
        } else {
            return Result.error("发布失败!");
        }
    }

    /**
     * 下线公告
     *
     * @param id
     * @return
     */
    @AutoLog(value = "平台公告管理-下线公告")
    @ApiOperation(value = "下线公告", notes = "下线公告")
    @RequiresPermissions("inz_announcements:inz_announcement:offline")
    @PostMapping(value = "/offline")
    public Result<String> offline(@RequestParam(name = "id", required = true) String id) {
        boolean success = inzAnnouncementService.offlineAnnouncement(id);
        if (success) {
            return Result.OK("下线成功!");
        } else {
            return Result.error("下线失败!");
        }
    }

    /**
     * 批量发布公告
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "平台公告管理-批量发布")
    @ApiOperation(value = "批量发布公告", notes = "批量发布公告")
    @RequiresPermissions("inz_announcements:inz_announcement:batchPublish")
    @PostMapping(value = "/batchPublish")
    public Result<String> batchPublish(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        boolean success = inzAnnouncementService.batchPublish(idList);
        if (success) {
            return Result.OK("批量发布成功!");
        } else {
            return Result.error("批量发布失败!");
        }
    }

    /**
     * 批量下线公告
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "平台公告管理-批量下线")
    @ApiOperation(value = "批量下线公告", notes = "批量下线公告")
    @RequiresPermissions("inz_announcements:inz_announcement:batchOffline")
    @PostMapping(value = "/batchOffline")
    public Result<String> batchOffline(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        boolean success = inzAnnouncementService.batchOffline(idList);
        if (success) {
            return Result.OK("批量下线成功!");
        } else {
            return Result.error("批量下线失败!");
        }
    }

    /**
     * 置顶/取消置顶
     *
     * @param id
     * @param isTop
     * @return
     */
    @AutoLog(value = "平台公告管理-置顶操作")
    @ApiOperation(value = "置顶/取消置顶", notes = "置顶/取消置顶")
    @RequiresPermissions("inz_announcements:inz_announcement:top")
    @PostMapping(value = "/toggleTop")
    public Result<String> toggleTop(@RequestParam(name = "id", required = true) String id,
                                    @RequestParam(name = "isTop", required = true) Integer isTop) {
        boolean success = inzAnnouncementService.toggleTop(id, isTop);
        if (success) {
            return Result.OK(isTop == 1 ? "置顶成功!" : "取消置顶成功!");
        } else {
            return Result.error("操作失败!");
        }
    }

    /**
     * 导出excel
     *
     * @param request
     * @param inzAnnouncement
     */
    @RequiresPermissions("inz_announcements:inz_announcement:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzAnnouncement inzAnnouncement) {
        return super.exportXls(request, inzAnnouncement, InzAnnouncement.class, "平台公告管理");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("inz_announcements:inz_announcement:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzAnnouncement.class);
    }


    /**
     * 获取有效公告列表（前端展示用）
     *
     * @param pageNo
     * @param pageSize
     * @param type
     * @param targetUserType
     * @return
     */
    @ApiOperation(value = "获取有效公告列表", notes = "获取有效公告列表")
    @GetMapping(value = "/front/list")
    public Result<IPage<InzAnnouncement>> getFrontList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                       @RequestParam(name = "type", required = false) Integer type,
                                                       @RequestParam(name = "targetUserType", required = false) Integer targetUserType) {
        Page<InzAnnouncement> page = new Page<>(pageNo, pageSize);
        IPage<InzAnnouncement> pageList = inzAnnouncementService.getValidAnnouncements(page, type, targetUserType);
        return Result.OK(pageList);
    }

    /**
     * 获取置顶公告
     *
     * @param targetUserType
     * @return
     */
    @ApiOperation(value = "获取置顶公告", notes = "获取置顶公告")
    @GetMapping(value = "/front/top")
    public Result<List<InzAnnouncement>> getTopAnnouncements(@RequestParam(name = "targetUserType", required = false) Integer targetUserType) {
        List<InzAnnouncement> list = inzAnnouncementService.getTopAnnouncements(targetUserType);
        return Result.OK(list);
    }

    /**
     * 获取最新公告（首页展示用）
     *
     * @param limit
     * @param targetUserType
     * @return
     */
    @ApiOperation(value = "获取最新公告", notes = "获取最新公告")
    @GetMapping(value = "/front/latest")
    public Result<List<InzAnnouncement>> getLatestAnnouncements(@RequestParam(name = "limit", defaultValue = "5") Integer limit,
                                                                @RequestParam(name = "targetUserType", required = false) Integer targetUserType) {
        List<InzAnnouncement> list = inzAnnouncementService.getLatestAnnouncements(limit, targetUserType);
        return Result.OK(list);
    }

    /**
     * 获取公告详情（增加阅读次数）- 支持查询参数（保持兼容性）
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "获取公告详情", notes = "获取公告详情")
    @GetMapping(value = "/front/detail")
    public Result<InzAnnouncement> getAnnouncementDetail(@RequestParam(name = "id") String id) {
        try {
            log.info("获取公告详情（查询参数），ID: {}", id);
            InzAnnouncement announcement = inzAnnouncementService.getAnnouncementDetail(id);
            if (announcement == null) {
                log.warn("公告不存在或未发布，ID: {}", id);
                return Result.error("公告不存在或未发布");
            }
            log.info("成功获取公告详情，标题: {}", announcement.getTitle());
            return Result.OK(announcement);
        } catch (Exception e) {
            log.error("获取公告详情失败，ID: {}", id, e);
            return Result.error("获取公告详情失败：" + e.getMessage());
        }
    }

    /**
     * 统计各类型公告数量
     *
     * @return
     */
    @ApiOperation(value = "统计各类型公告数量", notes = "统计各类型公告数量")
    @GetMapping(value = "/front/countByType")
    public Result<List<Object>> countByType() {
        List<Object> list = inzAnnouncementService.countByType();
        return Result.OK(list);
    }

    /**
     * 增加公告阅读次数
     *
     * @param id 公告ID
     * @return 当前阅读次数
     */
    @ApiOperation(value = "增加公告阅读次数", notes = "增加公告阅读次数")
    @GetMapping(value = "/front/incrementReadCount")
    public Result<Integer> incrementReadCount(@RequestParam(name = "id") String id) {
        Integer readCount = inzAnnouncementService.incrementReadCount(id);
        if (readCount == null) {
            return Result.error("公告不存在或未发布");
        }
        return Result.OK(readCount);
    }

}