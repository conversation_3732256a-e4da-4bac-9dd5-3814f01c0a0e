package org.jeecg.modules.inz_points_products.controller;

import java.util.Arrays;
import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_points_products.entity.InzPointsProducts;
import org.jeecg.modules.inz_points_products.service.IInzPointsProductsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import java.text.ParseException;
import java.text.SimpleDateFormat;

 /**
 * @Description: 积分商品表
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
/*@Api(tags="积分商品表")*/
@RestController
@RequestMapping("/inz_points_products/inzPointsProducts")
@Slf4j
public class InzPointsProductsController extends JeecgController<InzPointsProducts, IInzPointsProductsService> {
	@Autowired
	private IInzPointsProductsService inzPointsProductsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzPointsProducts
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "积分商品表-分页列表查询")
	/*@ApiOperation(value="积分商品表-分页列表查询", notes="积分商品表-分页列表查询")*/
	@GetMapping(value = "/list")
	public Result<IPage<InzPointsProducts>> queryPageList(InzPointsProducts inzPointsProducts,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<InzPointsProducts> queryWrapper = QueryGenerator.initQueryWrapper(inzPointsProducts, req.getParameterMap());
		Page<InzPointsProducts> page = new Page<InzPointsProducts>(pageNo, pageSize);
		IPage<InzPointsProducts> pageList = inzPointsProductsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param inzPointsProducts
	 * @return
	 */
	/*@AutoLog(value = "积分商品表-添加")
	@ApiOperation(value="积分商品表-添加", notes="积分商品表-添加")*/
	@RequiresPermissions("inz_points_products:inz_points_product:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InzPointsProducts inzPointsProducts) {
		inzPointsProductsService.save(inzPointsProducts);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param inzPointsProducts
	 * @return
	 */
	@AutoLog(value = "积分商品表-编辑")
	/*@ApiOperation(value="积分商品表-编辑", notes="积分商品表-编辑")*/
	@RequiresPermissions("inz_points_products:inz_points_product:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InzPointsProducts inzPointsProducts) {
		inzPointsProductsService.updateById(inzPointsProducts);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "积分商品表-通过id删除")
	/*@ApiOperation(value="积分商品表-通过id删除", notes="积分商品表-通过id删除")*/
	@RequiresPermissions("inz_points_products:inz_points_product:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzPointsProductsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "积分商品表-批量删除")
	/*@ApiOperation(value="积分商品表-批量删除", notes="积分商品表-批量删除")*/
	@RequiresPermissions("inz_points_products:inz_points_product:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzPointsProductsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "积分商品表-通过id查询")
	/*@ApiOperation(value="积分商品表-通过id查询", notes="积分商品表-通过id查询")*/
	@GetMapping(value = "/queryById")
	public Result<InzPointsProducts> queryById(@RequestParam(name="id",required=true) String id) {
		InzPointsProducts inzPointsProducts = inzPointsProductsService.getById(id);
		if(inzPointsProducts==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzPointsProducts);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param inzPointsProducts
    */
    @RequiresPermissions("inz_points_products:inz_points_product:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzPointsProducts inzPointsProducts) {
        return super.exportXls(request, inzPointsProducts, InzPointsProducts.class, "积分商品表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("inz_points_products:inz_points_product:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzPointsProducts.class);
    }

    /**
     * 设置定时上架
     *
     * @param id 积分商品ID
     * @param scheduledTime 定时上架时间
     * @return 操作结果
     */
    @AutoLog(value = "积分商品表-设置定时上架")
    @ApiOperation(value = "积分商品表-设置定时上架", notes = "积分商品表-设置定时上架")
    @PostMapping(value = "/setScheduledOnTime")
    public Result<?> setScheduledOnTime(@RequestParam(name = "id") String id, 
                                        @RequestParam(name = "scheduledTime") String scheduledTime) {
        try {
            // 查询商品
            InzPointsProducts product = inzPointsProductsService.getById(id);
            if (product == null) {
                return Result.error("积分商品不存在");
            }
            
            // 解析时间
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date scheduledDate = dateFormat.parse(scheduledTime);
            
            // 验证时间是否有效
            Date now = new Date();
            if (scheduledDate.before(now)) {
                return Result.error("定时上架时间不能早于当前时间");
            }
            
            // 设置定时上架时间
            product.setScheduledOnTime(scheduledDate);
            
            // 如果商品当前是上架状态，先将其下架
            if (InzPointsProducts.STATUS_ONLINE.equals(product.getStatus())) {
                product.setStatus(InzPointsProducts.STATUS_OFFLINE);
            }
            
            // 更新商品
            inzPointsProductsService.updateById(product);
            
            return Result.OK("设置定时上架成功，将在 " + scheduledTime + " 自动上架");
            
        } catch (ParseException e) {
            log.error("解析定时上架时间出错", e);
            return Result.error("时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式");
        } catch (Exception e) {
            log.error("设置定时上架出错", e);
            return Result.error("设置定时上架失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置定时下架
     *
     * @param id 积分商品ID
     * @param scheduledTime 定时下架时间
     * @return 操作结果
     */
    @AutoLog(value = "积分商品表-设置定时下架")
    @ApiOperation(value = "积分商品表-设置定时下架", notes = "积分商品表-设置定时下架")
    @PostMapping(value = "/setScheduledOffTime")
    public Result<?> setScheduledOffTime(@RequestParam(name = "id") String id, 
                                         @RequestParam(name = "scheduledTime") String scheduledTime) {
        try {
            // 查询商品
            InzPointsProducts product = inzPointsProductsService.getById(id);
            if (product == null) {
                return Result.error("积分商品不存在");
            }
            
            // 解析时间
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date scheduledDate = dateFormat.parse(scheduledTime);
            
            // 验证时间是否有效
            Date now = new Date();
            if (scheduledDate.before(now)) {
                return Result.error("定时下架时间不能早于当前时间");
            }
            
            // 设置定时下架时间
            product.setScheduledOffTime(scheduledDate);
            
            // 更新商品
            inzPointsProductsService.updateById(product);
            
            return Result.OK("设置定时下架成功，将在 " + scheduledTime + " 自动下架");
            
        } catch (ParseException e) {
            log.error("解析定时下架时间出错", e);
            return Result.error("时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式");
        } catch (Exception e) {
            log.error("设置定时下架出错", e);
            return Result.error("设置定时下架失败: " + e.getMessage());
        }
    }
    
    /**
     * 取消定时上架
     *
     * @param id 积分商品ID
     * @return 操作结果
     */
    @AutoLog(value = "积分商品表-取消定时上架")
    @ApiOperation(value = "积分商品表-取消定时上架", notes = "积分商品表-取消定时上架")
    @PostMapping(value = "/cancelScheduledOnTime")
    public Result<?> cancelScheduledOnTime(@RequestParam(name = "id") String id) {
        try {
            // 查询商品
            InzPointsProducts product = inzPointsProductsService.getById(id);
            if (product == null) {
                return Result.error("积分商品不存在");
            }
            
            // 检查是否设置了定时上架
            if (product.getScheduledOnTime() == null) {
                return Result.error("该商品未设置定时上架");
            }
            
            // 取消定时上架
            product.setScheduledOnTime(null);
            
            // 更新商品
            inzPointsProductsService.updateById(product);
            
            return Result.OK("取消定时上架成功");
            
        } catch (Exception e) {
            log.error("取消定时上架出错", e);
            return Result.error("取消定时上架失败: " + e.getMessage());
        }
    }
    
    /**
     * 取消定时下架
     *
     * @param id 积分商品ID
     * @return 操作结果
     */
    @AutoLog(value = "积分商品表-取消定时下架")
    @ApiOperation(value = "积分商品表-取消定时下架", notes = "积分商品表-取消定时下架")
    @PostMapping(value = "/cancelScheduledOffTime")
    public Result<?> cancelScheduledOffTime(@RequestParam(name = "id") String id) {
        try {
            // 查询商品
            InzPointsProducts product = inzPointsProductsService.getById(id);
            if (product == null) {
                return Result.error("积分商品不存在");
            }
            
            // 检查是否设置了定时下架
            if (product.getScheduledOffTime() == null) {
                return Result.error("该商品未设置定时下架");
            }
            
            // 取消定时下架
            product.setScheduledOffTime(null);
            
            // 更新商品
            inzPointsProductsService.updateById(product);
            
            return Result.OK("取消定时下架成功");
            
        } catch (Exception e) {
            log.error("取消定时下架出错", e);
            return Result.error("取消定时下架失败: " + e.getMessage());
        }
    }
}
