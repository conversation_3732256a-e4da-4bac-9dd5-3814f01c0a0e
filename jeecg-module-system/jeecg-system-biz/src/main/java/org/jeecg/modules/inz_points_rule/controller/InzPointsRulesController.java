package org.jeecg.modules.inz_points_rule.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_points_rule.entity.InzPointsRules;
import org.jeecg.modules.inz_points_rule.service.IInzPointsRulesService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 积分规则表
 * @Author: jeecg-boot
 * @Date:   2025-06-15
 * @Version: V1.0
 */
/*@Api(tags="积分规则表")*/
@RestController
@RequestMapping("/inz_points_rule/inzPointsRules")
@Slf4j
public class InzPointsRulesController extends JeecgController<InzPointsRules, IInzPointsRulesService> {
	@Autowired
	private IInzPointsRulesService inzPointsRulesService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzPointsRules
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "积分规则表-分页列表查询")
	/*@ApiOperation(value="积分规则表-分页列表查询", notes="积分规则表-分页列表查询")*/
	@GetMapping(value = "/list")
	public Result<IPage<InzPointsRules>> queryPageList(InzPointsRules inzPointsRules,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<InzPointsRules> queryWrapper = QueryGenerator.initQueryWrapper(inzPointsRules, req.getParameterMap());
		Page<InzPointsRules> page = new Page<InzPointsRules>(pageNo, pageSize);
		IPage<InzPointsRules> pageList = inzPointsRulesService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param inzPointsRules
	 * @return
	 */
	/*@AutoLog(value = "积分规则表-添加")
	@ApiOperation(value="积分规则表-添加", notes="积分规则表-添加")*/
	@RequiresPermissions("inz_points_rule:inz_points_rule:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InzPointsRules inzPointsRules) {
		inzPointsRulesService.save(inzPointsRules);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param inzPointsRules
	 * @return
	 */
	/*@AutoLog(value = "积分规则表-编辑")
	@ApiOperation(value="积分规则表-编辑", notes="积分规则表-编辑")*/
	@RequiresPermissions("inz_points_rule:inz_points_rule:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InzPointsRules inzPointsRules) {
		inzPointsRulesService.updateById(inzPointsRules);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	/*@AutoLog(value = "积分规则表-通过id删除")
	@ApiOperation(value="积分规则表-通过id删除", notes="积分规则表-通过id删除")*/
	@RequiresPermissions("inz_points_rule:inz_points_rule:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzPointsRulesService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	/*@AutoLog(value = "积分规则表-批量删除")
	@ApiOperation(value="积分规则表-批量删除", notes="积分规则表-批量删除")*/
	@RequiresPermissions("inz_points_rule:inz_points_rule:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzPointsRulesService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "积分规则表-通过id查询")
	/*@ApiOperation(value="积分规则表-通过id查询", notes="积分规则表-通过id查询")*/
	@GetMapping(value = "/queryById")
	public Result<InzPointsRules> queryById(@RequestParam(name="id",required=true) String id) {
		InzPointsRules inzPointsRules = inzPointsRulesService.getById(id);
		if(inzPointsRules==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzPointsRules);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param inzPointsRules
    */
    @RequiresPermissions("inz_points_rule:inz_points_rule:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzPointsRules inzPointsRules) {
        return super.exportXls(request, inzPointsRules, InzPointsRules.class, "积分规则表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("inz_points_rule:inz_points_rule:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzPointsRules.class);
    }

}
