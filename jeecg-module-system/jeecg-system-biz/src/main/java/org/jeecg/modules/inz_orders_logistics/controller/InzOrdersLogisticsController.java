package org.jeecg.modules.inz_orders_logistics.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_orders.entity.InzOrders;
import org.jeecg.modules.inz_orders_logistics.entity.InzOrdersLogistics;
import org.jeecg.modules.inz_orders_logistics.service.IInzOrdersLogisticsService;
import org.jeecg.modules.inz_store.service.IInzStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.jeecg.modules.inz_orders.service.IInzOrdersService;
import org.jeecg.modules.inz_users_fronts.service.IInzUsersFrontsService;
import org.jeecg.modules.inz_orders_logistics.model.ExpressTrackDTO;
import org.jeecg.modules.inz_orders_logistics.service.IExpressTrackingService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 订单物流信息表
 * @Author: jeecg-boot
 * @Date: 2025-06-16
 * @Version: V1.0
 */
/*@Api(tags="订单物流信息表")*/
@RestController
@RequestMapping("/inz_orders_logistics/inzOrdersLogistics")
@Slf4j
public class InzOrdersLogisticsController extends JeecgController<InzOrdersLogistics, IInzOrdersLogisticsService> {
    @Autowired
    private IInzOrdersLogisticsService inzOrdersLogisticsService;

    @Autowired
    private IInzOrdersService inzOrdersService;
    
    @Autowired
    private IInzStoreService inzStoreService;

    @Autowired
    private IInzUsersFrontsService inzUsersFrontsService;

    @Autowired
    private IExpressTrackingService expressTrackingService;

    /**
     * 分页列表查询
     *
     * @param inzOrdersLogistics 查询条件
     * @param pageNo 页码
     * @param pageSize 每页记录数
     * @param storeId 店铺ID
     * @param storeName 店铺名称
     * @param userName 用户名
     * @param shipTime 发货时间范围（格式：yyyy-MM-dd HH:mm:ss,yyyy-MM-dd HH:mm:ss）
     * @param finishTime 完成时间范围（格式：yyyy-MM-dd HH:mm:ss,yyyy-MM-dd HH:mm:ss）
     * @param req HTTP请求
     * @return 分页结果
     */
    //@AutoLog(value = "订单物流信息表-分页列表查询")
    /*@ApiOperation(value="订单物流信息表-分页列表查询", notes="订单物流信息表-分页列表查询")*/
    @GetMapping(value = "/list")
    public Result<IPage<InzOrdersLogistics>> queryPageList(InzOrdersLogistics inzOrdersLogistics,
                                                           @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                           @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                           @RequestParam(name = "storeId", required = false) String storeId,
                                                           @RequestParam(name = "storeName", required = false) String storeName,
                                                           @RequestParam(name = "userName", required = false) String username,
                                                           @RequestParam(name = "shipTime", required = false) String shipTime,
                                                           @RequestParam(name = "finishTime", required = false) String finishTime,
                                                           HttpServletRequest req) {
        QueryWrapper<InzOrdersLogistics> queryWrapper = QueryGenerator.initQueryWrapper(inzOrdersLogistics, req.getParameterMap());
        Page<InzOrdersLogistics> page = new Page<InzOrdersLogistics>(pageNo, pageSize);
        
        // 处理店铺ID筛选（通过订单表间接实现）
        if (StringUtils.isNotBlank(storeId)) {
            // 通过storeId查找所有订单ID
            java.util.List<String> orderIds = inzOrdersService.getOrderIdsByStoreId(storeId);
            if (orderIds != null && !orderIds.isEmpty()) {
                queryWrapper.in("order_id", orderIds);
            } else {
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }
        }
        
        // 处理店铺名称筛选（通过订单表间接实现）
        if (StringUtils.isNotBlank(storeName)) {
            String storeIdFromName = inzStoreService.getStoreIdByName(storeName);
            if (StringUtils.isNotBlank(storeIdFromName)) {
                java.util.List<String> orderIds = inzOrdersService.getOrderIdsByStoreId(storeIdFromName);
                if (orderIds != null && !orderIds.isEmpty()) {
                    queryWrapper.in("order_id", orderIds);
                } else {
                    return Result.OK(new Page<>(pageNo, pageSize, 0));
                }
            } else {
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }
        }
        
        // 处理用户名筛选
        if (StringUtils.isNotBlank(username)) {
            String userId = inzUsersFrontsService.getUserIdByUsername(username);
            if (StringUtils.isNotBlank(userId)) {
                java.util.List<String> orderIds = inzOrdersService.getOrderIdsByUserId(userId);
                if (orderIds != null && !orderIds.isEmpty()) {
                    queryWrapper.in("order_id", orderIds);
                } else {
                    return Result.OK(new Page<>(pageNo, pageSize, 0));
                }
            } else {
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }
        }
        
        // 处理发货时间范围筛选
        if (StringUtils.isNotBlank(shipTime)) {
            String[] shipTimeArr = shipTime.split(",");
            if (shipTimeArr.length == 2) {
                String startTime = shipTimeArr[0].trim();
                String endTime = shipTimeArr[1].trim();
                if (StringUtils.isNotBlank(startTime)) {
                    queryWrapper.ge("ship_time", startTime);
                }
                if (StringUtils.isNotBlank(endTime)) {
                    queryWrapper.le("ship_time", endTime);
                }
            }
        }
        
        // 处理完成时间范围筛选
        if (StringUtils.isNotBlank(finishTime)) {
            String[] finishTimeArr = finishTime.split(",");
            if (finishTimeArr.length == 2) {
                String startTime = finishTimeArr[0].trim();
                String endTime = finishTimeArr[1].trim();
                if (StringUtils.isNotBlank(startTime)) {
                    queryWrapper.ge("finish_time", startTime);
                }
                if (StringUtils.isNotBlank(endTime)) {
                    queryWrapper.le("finish_time", endTime);
                }
            }
        }
        
        // ====== 数据权限控制逻辑开始 ======
        org.jeecg.common.system.vo.LoginUser sysUser = (org.jeecg.common.system.vo.LoginUser) org.apache.shiro.SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null) {
            if (!isAdmin(sysUser)) {
                log.info("非管理员用户访问订单物流列表，进行数据权限过滤");
                String phone = sysUser.getPhone();
                String userId = null;
                if (StringUtils.isNotBlank(phone)) {
                    try {
                        userId = inzUsersFrontsService.getUserIdByPhone(phone);
                        log.info("通过手机号[{}]查询到前端用户ID: {}", phone, userId);
                    } catch (Exception e) {
                        log.warn("通过手机号查询前端用户失败: {}", e.getMessage());
                    }
                } else {
                    log.warn("后台用户[{}]没有关联手机号", sysUser.getUsername());
                }
                if (StringUtils.isNotBlank(userId)) {
                    QueryWrapper<org.jeecg.modules.inz_store.entity.InzStore> storeQueryWrapper = new QueryWrapper<>();
                    storeQueryWrapper.eq("user_id", userId);
                    org.jeecg.modules.inz_store.entity.InzStore store = inzStoreService.getOne(storeQueryWrapper);
                    if (store != null) {
                        log.info("用户[{}]关联店铺[{}]，只能查看该店铺订单物流", userId, store.getId());
                        java.util.List<String> orderIds = inzOrdersService.getOrderIdsByStoreId(store.getId());
                        if (orderIds != null && !orderIds.isEmpty()) {
                            queryWrapper.in("order_id", orderIds);
                        } else {
                            return Result.OK(new Page<>(pageNo, pageSize, 0));
                        }
                    } else {
                        log.warn("用户[{}]没有关联店铺，返回空结果", userId);
                        return Result.OK(new Page<>(pageNo, pageSize, 0));
                    }
                } else {
                    log.warn("未找到后台用户[{}]对应的前端用户，返回空结果", sysUser.getUsername());
                    return Result.OK(new Page<>(pageNo, pageSize, 0));
                }
            } else {
                log.info("管理员用户访问订单物流列表，不进行数据权限过滤");
            }
        }
        // ====== 数据权限控制逻辑结束 ======
        
        IPage<InzOrdersLogistics> pageList = inzOrdersLogisticsService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 判断用户是否为管理员
     * @param user 登录用户
     * @return 是否为管理员
     */
    private boolean isAdmin(org.jeecg.common.system.vo.LoginUser user) {
        if (user == null) {
            return false;
        }
        String roleCode = user.getRoleCode();
        return StringUtils.isNotBlank(roleCode) && (roleCode.contains("admin") || roleCode.contains("ADMIN"));
    }

    /**
     * 通过店铺名称查询订单物流信息
     *
     * @param storeName 店铺名称
     * @param pageNo 页码
     * @param pageSize 每页记录数
     * @param req HTTP请求
     * @return 分页结果
     */
    @ApiOperation(value = "通过店铺名称查询订单物流信息", notes = "根据店铺名称查询对应的订单物流信息")
    @GetMapping(value = "/listByStoreName")
    public Result<IPage<InzOrdersLogistics>> queryLogisticsByStoreName(
                                                           @RequestParam(name = "storeName") String storeName,
                                                           @RequestParam(name = "pageNo", defaultValue = "1") @Min(value = 1, message = "页码不能小于1") Integer pageNo,
                                                           @RequestParam(name = "pageSize", defaultValue = "10") @Min(value = 1, message = "每页记录数不能小于1") @Max(value = 100, message = "每页记录数不能超过100") Integer pageSize,
                                                           HttpServletRequest req) {
        try {
            if (StringUtils.isBlank(storeName)) {
                return Result.error("店铺名称不能为空");
            }
            
            // 通过店铺名称获取店铺ID
            String storeId = inzStoreService.getStoreIdByName(storeName);
            if (StringUtils.isBlank(storeId)) {
                return Result.error("未找到名为 '" + storeName + "' 的店铺");
            }
            
            // 创建分页对象
            Page<InzOrdersLogistics> page = new Page<>(pageNo, pageSize);
            
            // 创建查询条件
            InzOrdersLogistics logistics = new InzOrdersLogistics();
            
            // 使用店铺ID查询物流信息
            IPage<InzOrdersLogistics> pageList = inzOrdersLogisticsService.queryLogisticsByStoreId(
                page, logistics, storeId, req.getParameterMap());
            
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error("查询订单物流信息失败", e);
            return Result.error("查询订单物流信息失败: " + e.getMessage());
        }
    }

    /**
     * 添加
     *
     * @param inzOrdersLogistics
     * @return
     */
    @AutoLog(value = "订单物流信息表-添加")
    /*@ApiOperation(value="订单物流信息表-添加", notes="订单物流信息表-添加")*/
    @RequiresPermissions("inz_orders_logistics:inz_order_logistics:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InzOrdersLogistics inzOrdersLogistics) {
        inzOrdersLogisticsService.save(inzOrdersLogistics);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param inzOrdersLogistics
     * @return
     */
    @AutoLog(value = "订单物流信息表-编辑")
    /*@ApiOperation(value="订单物流信息表-编辑", notes="订单物流信息表-编辑")*/
    @RequiresPermissions("inz_orders_logistics:inz_order_logistics:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InzOrdersLogistics inzOrdersLogistics) {
        inzOrdersLogisticsService.updateById(inzOrdersLogistics);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "订单物流信息表-通过id删除")
    /*@ApiOperation(value="订单物流信息表-通过id删除", notes="订单物流信息表-通过id删除")*/
    @RequiresPermissions("inz_orders_logistics:inz_order_logistics:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        inzOrdersLogisticsService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "订单物流信息表-批量删除")
    /*@ApiOperation(value="订单物流信息表-批量删除", notes="订单物流信息表-批量删除")*/
    @RequiresPermissions("inz_orders_logistics:inz_order_logistics:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzOrdersLogisticsService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "订单物流信息表-通过id查询")
    /*@ApiOperation(value="订单物流信息表-通过id查询", notes="订单物流信息表-通过id查询")*/
    @GetMapping(value = "/queryById")
    public Result<InzOrdersLogistics> queryById(@RequestParam(name = "id", required = true) String id) {
        InzOrdersLogistics inzOrdersLogistics = inzOrdersLogisticsService.getById(id);
        if (inzOrdersLogistics == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzOrdersLogistics);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param inzOrdersLogistics
     */
    @RequiresPermissions("inz_orders_logistics:inz_order_logistics:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzOrdersLogistics inzOrdersLogistics) {
        return super.exportXls(request, inzOrdersLogistics, InzOrdersLogistics.class, "订单物流信息表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("inz_orders_logistics:inz_order_logistics:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzOrdersLogistics.class);
    }
    
    /**
     * 修改物流信息
     *
     * @param inzOrdersLogistics 物流信息
     * @return 操作结果
     */
    @ApiOperation(value = "修改物流信息", notes = "修改订单的物流信息，包括物流公司、物流单号、物流状态等")
    @RequiresPermissions("inz_orders_logistics:inz_order_logistics:updateLogistics")
    @PostMapping(value = "/updateLogistics")
    public Result<String> updateLogistics(@RequestBody InzOrdersLogistics inzOrdersLogistics) {
        return inzOrdersLogisticsService.updateLogistics(inzOrdersLogistics);
    }
    
    /**
     * 通过订单ID查询物流信息
     *
     * @param orderId 订单ID
     * @return 物流信息
     */
    @ApiOperation(value = "通过订单ID查询物流信息", notes = "根据订单ID查询对应的物流信息")
    @GetMapping(value = "/queryByOrderId")
    public Result<InzOrdersLogistics> queryByOrderId(@RequestParam(name = "orderId", required = true) String orderId) {
        InzOrdersLogistics logistics = inzOrdersLogisticsService.getLogisticsByOrderId(orderId);
        if (logistics == null) {
            return Result.error("未找到对应订单的物流信息");
        }
        return Result.OK(logistics);
    }

    /**
     * 查询物流轨迹
     *
     * @param id 物流记录ID
     * @return 物流轨迹信息
     */
    @ApiOperation(value = "查询物流轨迹", notes = "查询物流轨迹")
    @GetMapping(value = "/track/{id}")
    public Result<ExpressTrackDTO> queryTrack(@PathVariable(name = "id") String id) {
        log.info("查询物流轨迹, ID: {}", id);
        
        ExpressTrackDTO trackDTO = expressTrackingService.queryTrackByLogisticsId(id);
        if (trackDTO == null) {
            return Result.error("查询物流信息失败，请稍后重试");
        }
        
        return Result.OK(trackDTO);
    }
}
