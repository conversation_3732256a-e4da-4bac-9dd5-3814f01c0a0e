package org.jeecg.modules.inz_store.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 店铺表
 * @Author: jeecg-boot
 * @Date:   2025-06-15
 * @Version: V1.0
 */
@Data
@TableName("inz_store")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_store对象", description="店铺表")
public class InzStore implements Serializable {
    private static final long serialVersionUID = 1L;

	/**ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "ID")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**店主用户id*/
	@Excel(name = "店主用户id", width = 15)
    @ApiModelProperty(value = "店主用户id")
    private java.lang.String userId;
	/**店铺名称*/
	@Excel(name = "店铺名称", width = 15)
    @ApiModelProperty(value = "店铺名称")
    private java.lang.String name;
	/**店铺头像*/
	@Excel(name = "店铺头像", width = 15)
    @ApiModelProperty(value = "店铺头像")
    private java.lang.String avatar;
	/**店铺banner*/
	@Excel(name = "店铺banner", width = 15)
    @ApiModelProperty(value = "店铺banner")
    private java.lang.String banner;
	/**店铺描述*/
	@Excel(name = "店铺描述", width = 15)
    @ApiModelProperty(value = "店铺描述")
    private java.lang.String description;
	/**店铺公告*/
	@Excel(name = "店铺公告", width = 15)
    @ApiModelProperty(value = "店铺公告")
    private java.lang.String announcement;
	/**营业执照*/
	@Excel(name = "营业执照", width = 15)
    @ApiModelProperty(value = "营业执照")
    private java.lang.String businessLicense;
	/**联系电话*/
	@Excel(name = "联系电话", width = 15)
    @ApiModelProperty(value = "联系电话")
    private java.lang.String phone;
	/**省份*/
	@Excel(name = "省份", width = 15)
    @ApiModelProperty(value = "省份")
    private java.lang.String province;
	/**城市*/
	@Excel(name = "城市", width = 15)
    @ApiModelProperty(value = "城市")
    private java.lang.String city;
	/**区县*/
	@Excel(name = "区县", width = 15)
    @ApiModelProperty(value = "区县")
    private java.lang.String district;
	/**详细地址*/
	@Excel(name = "详细地址", width = 15)
    @ApiModelProperty(value = "详细地址")
    private java.lang.String address;
	/**店铺分类标签，逗号分隔*/
	@Excel(name = "店铺分类标签，逗号分隔", width = 15)
    @ApiModelProperty(value = "店铺分类标签，逗号分隔")
    private java.lang.String tags;
	/**是否开通直播（0-未开通，1-已开通）*/
	@Excel(name = "是否开通直播（0-未开通，1-已开通）", width = 15)
    @ApiModelProperty(value = "是否开通直播（0-未开通，1-已开通）")
    private java.lang.Integer liveEnabled;
	/**店铺等级*/
	@Excel(name = "店铺等级", width = 15)
    @ApiModelProperty(value = "店铺等级")
    private java.lang.Integer level;
	/**店铺积分*/
	@Excel(name = "店铺积分", width = 15)
    @ApiModelProperty(value = "店铺积分")
    private java.lang.Integer points;
	/**状态(0-关闭,1-营业中)*/
	@Excel(name = "状态(0-关闭,1-营业中)", width = 15)
    @ApiModelProperty(value = "状态(0-关闭,1-营业中)")
    private java.lang.Integer status;
	/**店铺类型（1-个人，2-企业）*/
	@Excel(name = "店铺类型（1-个人，2-企业）", width = 15)
    @ApiModelProperty(value = "店铺类型（1-个人，2-企业）")
    private java.lang.Integer type;
	/**认证状态（0-未认证，1-已认证）*/
	@Excel(name = "认证状态（0-未认证，1-已认证）", width = 15)
    @ApiModelProperty(value = "认证状态（0-未认证，1-已认证）")
    private java.lang.Integer verified;
	/**粉丝数*/
	@Excel(name = "粉丝数", width = 15)
    @ApiModelProperty(value = "粉丝数")
    private java.lang.Integer fanscount;
	/**商品数量*/
	@Excel(name = "商品数量", width = 15)
    @ApiModelProperty(value = "商品数量")
    private java.lang.Integer productsCount;
	/**总销量*/
	@Excel(name = "总销量", width = 15)
    @ApiModelProperty(value = "总销量")
    private java.lang.Integer salesCount;
	/**店铺评分*/
	@Excel(name = "店铺评分", width = 15)
    @ApiModelProperty(value = "店铺评分")
    private java.math.BigDecimal rating;

	// ==================== 第一阶段：核心缺失字段补充 ====================

	/**店铺编号*/
	@Excel(name = "店铺编号", width = 15)
	@ApiModelProperty(value = "店铺编号")
	private java.lang.String shopCode;

	/**店主姓名*/
	@Excel(name = "店主姓名", width = 15)
	@ApiModelProperty(value = "店主姓名")
	private java.lang.String ownerName;

	/**店主手机号*/
	@Excel(name = "店主手机号", width = 15)
	@ApiModelProperty(value = "店主手机号")
	private java.lang.String ownerPhone;

	/**店主邮箱*/
	@Excel(name = "店主邮箱", width = 15)
	@ApiModelProperty(value = "店主邮箱")
	private java.lang.String ownerEmail;

	/**法人代表*/
	@Excel(name = "法人代表", width = 15)
	@ApiModelProperty(value = "法人代表")
	private java.lang.String legalPerson;

	/**统一社会信用代码*/
	@Excel(name = "统一社会信用代码", width = 15)
	@ApiModelProperty(value = "统一社会信用代码")
	private java.lang.String creditCode;

	/**营业执照图片*/
	@Excel(name = "营业执照图片", width = 15)
	@ApiModelProperty(value = "营业执照图片")
	private java.lang.String businessLicenseImg;

	/**身份证正面*/
	@Excel(name = "身份证正面", width = 15)
	@ApiModelProperty(value = "身份证正面")
	private java.lang.String idCardFront;

	/**身份证反面*/
	@Excel(name = "身份证反面", width = 15)
	@ApiModelProperty(value = "身份证反面")
	private java.lang.String idCardBack;

	/**保证金*/
	@Excel(name = "保证金", width = 15)
	@ApiModelProperty(value = "保证金")
	private java.math.BigDecimal deposit;

	/**账户余额*/
	@Excel(name = "账户余额", width = 15)
	@ApiModelProperty(value = "账户余额")
	private java.math.BigDecimal balance;

	/**冻结金额*/
	@Excel(name = "冻结金额", width = 15)
	@ApiModelProperty(value = "冻结金额")
	private java.math.BigDecimal frozenAmount;

	/**佣金比例*/
	@Excel(name = "佣金比例", width = 15)
	@ApiModelProperty(value = "佣金比例")
	private java.math.BigDecimal commissionRate;

	/**月销售额*/
	@Excel(name = "月销售额", width = 15)
	@ApiModelProperty(value = "月销售额")
	private java.math.BigDecimal monthSales;

	/**总销售额*/
	@Excel(name = "总销售额", width = 15)
	@ApiModelProperty(value = "总销售额")
	private java.math.BigDecimal totalSales;

	/**审核状态*/
	@Excel(name = "审核状态", width = 15, dicCode = "audit_status")
	@Dict(dicCode = "audit_status")
	@ApiModelProperty(value = "审核状态")
	private java.lang.Integer auditStatus;

	/**审核备注*/
	@Excel(name = "审核备注", width = 15)
	@ApiModelProperty(value = "审核备注")
	private java.lang.String auditRemark;

	/**审核时间*/
	@Excel(name = "审核时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "审核时间")
	private java.util.Date auditTime;

	/**审核人*/
	@Excel(name = "审核人", width = 15)
	@ApiModelProperty(value = "审核人")
	private java.lang.String auditBy;

	/**开店时间*/
	@Excel(name = "开店时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "开店时间")
	private java.util.Date openTime;

	/**关店时间*/
	@Excel(name = "关店时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "关店时间")
	private java.util.Date closeTime;

	/**关店原因*/
	@Excel(name = "关店原因", width = 15)
	@ApiModelProperty(value = "关店原因")
	private java.lang.String closeReason;

	/**营业开始时间*/
	@Excel(name = "营业开始时间", width = 15)
	@ApiModelProperty(value = "营业开始时间")
	private java.lang.String businessStartTime;

	/**营业结束时间*/
	@Excel(name = "营业结束时间", width = 15)
	@ApiModelProperty(value = "营业结束时间")
	private java.lang.String businessEndTime;

	/**是否推荐*/
	@Excel(name = "是否推荐", width = 15, dicCode = "yn")
	@Dict(dicCode = "yn")
	@ApiModelProperty(value = "是否推荐")
	private java.lang.Integer isRecommend;

	/**排序权重*/
	@Excel(name = "排序权重", width = 15)
	@ApiModelProperty(value = "排序权重")
	private java.lang.Integer sortOrder;

	/**备注*/
	@Excel(name = "备注", width = 15)
	@ApiModelProperty(value = "备注")
	private java.lang.String remark;

	// ==================== 结算账户信息 ====================

	/**对公账户名称*/
	@Excel(name = "对公账户名称", width = 15)
	@ApiModelProperty(value = "对公账户名称")
	private java.lang.String corporateAccountName;

	/**对公开户行*/
	@Excel(name = "对公开户行", width = 15)
	@ApiModelProperty(value = "对公开户行")
	private java.lang.String corporateBank;

	/**对公银行账号*/
	@Excel(name = "对公银行账号", width = 15)
	@ApiModelProperty(value = "对公银行账号")
	private java.lang.String corporateBankAccount;

	/**个人账户名称*/
	@Excel(name = "个人账户名称", width = 15)
	@ApiModelProperty(value = "个人账户名称")
	private java.lang.String personalAccountName;

	/**个人开户行*/
	@Excel(name = "个人开户行", width = 15)
	@ApiModelProperty(value = "个人开户行")
	private java.lang.String personalBank;

	/**个人银行账号*/
	@Excel(name = "个人银行账号", width = 15)
	@ApiModelProperty(value = "个人银行账号")
	private java.lang.String personalBankAccount;
}
