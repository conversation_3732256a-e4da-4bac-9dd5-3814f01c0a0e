package org.jeecg.modules.inz_orders_logistics.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_orders_logistics.entity.InzOrdersLogistics;
import org.jeecg.modules.inz_orders_logistics.mapper.InzOrdersLogisticsMapper;
import org.jeecg.modules.inz_orders_logistics.service.IInzOrdersLogisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Date;
import java.util.Map;

/**
 * @Description: 订单物流信息表
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Service
public class InzOrdersLogisticsServiceImpl extends ServiceImpl<InzOrdersLogisticsMapper, InzOrdersLogistics> implements IInzOrdersLogisticsService {

    @Autowired
    private InzOrdersLogisticsMapper logisticsMapper;

    @Override
    public IPage<InzOrdersLogistics> queryLogisticsByStoreId(Page<InzOrdersLogistics> page,
                                                           InzOrdersLogistics logistics,
                                                           String storeId,
                                                           Map<String, String[]> parameterMap) {
        // 构建查询条件
        QueryWrapper<InzOrdersLogistics> queryWrapper = QueryGenerator.initQueryWrapper(logistics, parameterMap);
        
        if (StringUtils.isNotBlank(storeId)) {
            // 添加连表查询和店铺ID过滤条件，使用子查询方式
            queryWrapper.inSql("order_id", "SELECT id FROM inz_order WHERE store_id = '" + storeId + "'");
        }
        
        // 执行分页查询
        return logisticsMapper.selectPage(page, queryWrapper);
    }

    @Override
    public Result<String> updateLogistics(InzOrdersLogistics logistics) {
        // 验证必填参数
        if (logistics == null) {
            return Result.error("物流信息不能为空");
        }
        
        if (StringUtils.isBlank(logistics.getId()) && StringUtils.isBlank(logistics.getOrderId())) {
            return Result.error("物流ID或订单ID不能为空");
        }
        
        // 根据ID或订单ID查找当前物流信息
        InzOrdersLogistics currentLogistics = null;
        if (StringUtils.isNotBlank(logistics.getId())) {
            currentLogistics = this.getById(logistics.getId());
        } else {
            currentLogistics = this.getLogisticsByOrderId(logistics.getOrderId());
        }
        
        // 如果物流信息不存在，返回错误
        if (currentLogistics == null) {
            return Result.error("物流信息不存在");
        }
        
        // 更新物流信息
        boolean hasChanges = false;
        
        // 更新物流公司信息
        if (StringUtils.isNotBlank(logistics.getExpressCompany())) {
            currentLogistics.setExpressCompany(logistics.getExpressCompany());
            hasChanges = true;
        }
        
        // 更新物流公司编码
        if (StringUtils.isNotBlank(logistics.getExpressCode())) {
            currentLogistics.setExpressCode(logistics.getExpressCode());
            hasChanges = true;
        }
        
        // 更新物流单号
        if (StringUtils.isNotBlank(logistics.getTrackingNo())) {
            currentLogistics.setTrackingNo(logistics.getTrackingNo());
            hasChanges = true;
        }
        
        // 更新物流状态
        if (logistics.getLogisticsStatus() != null) {
            currentLogistics.setLogisticsStatus(logistics.getLogisticsStatus());
            hasChanges = true;
            
            // 如果状态为已签收(3)，更新完成时间
            if (logistics.getLogisticsStatus() == 3 && currentLogistics.getFinishTime() == null) {
                currentLogistics.setFinishTime(new Date());
            }
        }
        
        // 更新最新物流信息
        if (StringUtils.isNotBlank(logistics.getLatestInfo())) {
            currentLogistics.setLatestInfo(logistics.getLatestInfo());
            hasChanges = true;
        }
        
        // 如果没有任何实际修改，则告知用户
        if (!hasChanges) {
            return Result.OK("没有检测到需要更新的物流信息");
        }
        
        // 更新修改时间
        currentLogistics.setUpdateTime(new Date());
        
        // 保存到数据库
        boolean success = this.updateById(currentLogistics);
        if (success) {
            return Result.OK("物流信息更新成功");
        } else {
            return Result.error("物流信息更新失败");
        }
    }

    @Override
    public InzOrdersLogistics getLogisticsByOrderId(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return null;
        }
        
        QueryWrapper<InzOrdersLogistics> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_id", orderId);
        return logisticsMapper.selectOne(queryWrapper);
    }
}
