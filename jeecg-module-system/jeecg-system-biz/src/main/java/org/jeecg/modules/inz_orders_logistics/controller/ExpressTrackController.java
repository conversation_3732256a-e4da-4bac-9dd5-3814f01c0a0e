package org.jeecg.modules.inz_orders_logistics.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.inz_orders_logistics.model.ExpressTrackDTO;
import org.jeecg.modules.inz_orders_logistics.service.IExpressTrackingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 物流轨迹查询控制器
 */
@Api(tags = "物流轨迹查询")
@RestController
@RequestMapping("/inz_orders_logistics/track")
@Slf4j
public class ExpressTrackController {
    
    @Autowired
    private IExpressTrackingService expressTrackingService;
    
    /**
     * 根据物流单号和快递公司编码查询物流轨迹
     *
     * @param expressNo 物流单号
     * @param expressCompany 快递公司编码
     * @return 物流轨迹信息
     */
    @ApiOperation(value = "根据物流单号查询物流轨迹", notes = "根据物流单号查询物流轨迹")
    @GetMapping("/query")
    public Result<ExpressTrackDTO> queryTrack(
            @RequestParam(name = "expressNo") String expressNo,
            @RequestParam(name = "expressCompany") String expressCompany) {
        
        log.info("查询物流轨迹, 物流单号: {}, 快递公司编码: {}", expressNo, expressCompany);
        
        ExpressTrackDTO trackDTO = expressTrackingService.queryTrack(expressNo, expressCompany);
        if (trackDTO == null) {
            return Result.error("查询物流信息失败，请稍后重试");
        }
        
        return Result.OK(trackDTO);
    }
    
    /**
     * 根据物流订单ID查询物流轨迹
     *
     * @param logisticsId 物流订单ID
     * @return 物流轨迹信息
     */
    @ApiOperation(value = "根据物流订单ID查询物流轨迹", notes = "根据物流订单ID查询物流轨迹")
    @GetMapping("/queryByLogisticsId")
    public Result<ExpressTrackDTO> queryTrackByLogisticsId(
            @RequestParam(name = "logisticsId") String logisticsId) {
        
        log.info("根据物流订单ID查询物流轨迹, ID: {}", logisticsId);
        
        ExpressTrackDTO trackDTO = expressTrackingService.queryTrackByLogisticsId(logisticsId);
        if (trackDTO == null) {
            return Result.error("查询物流信息失败，请稍后重试");
        }
        
        return Result.OK(trackDTO);
    }
} 