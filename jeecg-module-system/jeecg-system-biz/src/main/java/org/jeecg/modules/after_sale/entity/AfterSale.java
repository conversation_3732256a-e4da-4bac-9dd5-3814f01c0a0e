package org.jeecg.modules.after_sale.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * @Description: 售后表
 * @Author: jeecg-boot
 * @Date:   2025-06-20
 * @Version: V1.0
 */
@Data
@TableName("inz_after_sale")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_after_sale对象", description="售后表")
public class AfterSale implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**订单ID*/
	@Excel(name = "订单ID", width = 15)
    @ApiModelProperty(value = "订单ID")
    private java.lang.String orderId;
	/**订单编号*/
	@Excel(name = "订单编号", width = 15)
    @ApiModelProperty(value = "订单编号")
    private java.lang.String orderNo;
	/**用户ID*/
	@Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private java.lang.String userId;
	/**店铺ID*/
	@Excel(name = "店铺ID", width = 15)
    @ApiModelProperty(value = "店铺ID")
    private java.lang.String storeId;
	/**售后类型(1:仅退款,2:退货退款,3:换货)*/
	@Excel(name = "售后类型(1:仅退款,2:退货退款,3:换货)", width = 15)
    @ApiModelProperty(value = "售后类型(1:仅退款,2:退货退款,3:换货)")
    private java.lang.Integer type;
	/**售后原因编码*/
	@Excel(name = "售后原因编码", width = 15)
    @ApiModelProperty(value = "售后原因编码")
    private java.lang.Integer reasonCode;
	/**售后原因文本*/
	@Excel(name = "售后原因文本", width = 15)
    @ApiModelProperty(value = "售后原因文本")
    private java.lang.String reasonText;
	/**自定义原因说明*/
	@Excel(name = "自定义原因说明", width = 15)
    @ApiModelProperty(value = "自定义原因说明")
    private java.lang.String customReason;
	/**旧售后原因(待删除)*/
	@Excel(name = "旧售后原因", width = 15)
    @ApiModelProperty(value = "旧售后原因(待删除)")
    private java.lang.String oldReason;
	/**补充说明*/
	@Excel(name = "补充说明", width = 15)
    @ApiModelProperty(value = "补充说明")
    private java.lang.String additionalNote;
	/**详细描述*/
	@Excel(name = "详细描述", width = 15)
    @ApiModelProperty(value = "详细描述")
    private java.lang.String detailDescription;
	/**用户上传图片(问题凭证)*/
	@Excel(name = "用户上传图片", width = 15)
    @ApiModelProperty(value = "用户上传图片(问题凭证)")
    private java.lang.String userImages;
	/**商品图片(商品状态)*/
	@Excel(name = "商品图片", width = 15)
    @ApiModelProperty(value = "商品图片(商品状态)")
    private java.lang.String productImages;
	/**包装图片(包装状态)*/
	@Excel(name = "包装图片", width = 15)
    @ApiModelProperty(value = "包装图片(包装状态)")
    private java.lang.String packageImages;
	/**其他图片*/
	@Excel(name = "其他图片", width = 15)
    @ApiModelProperty(value = "其他图片")
    private java.lang.String otherImages;
	/**退款金额*/
	@Excel(name = "退款金额", width = 15)
    @ApiModelProperty(value = "退款金额")
    private java.math.BigDecimal refundAmount;
	/**售后状态(1:待审核,2:审核通过,3:审核拒绝,4:退款中,5:已完成,6:已取消)*/
	@Excel(name = "售后状态(1:待审核,2:审核通过,3:审核拒绝,4:退款中,5:已完成,6:已取消)", width = 15)
    @ApiModelProperty(value = "售后状态(1:待审核,2:审核通过,3:审核拒绝,4:退款中,5:已完成,6:已取消)")
    private java.lang.Integer status;
	/**处理备注*/
	@Excel(name = "处理备注", width = 15)
    @ApiModelProperty(value = "处理备注")
    private java.lang.String handleNote;
	/**退款流水号*/
	@Excel(name = "退款流水号", width = 15)
    @ApiModelProperty(value = "退款流水号")
    private java.lang.String refundNo;
	/**审核时间*/
	@Excel(name = "审核时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private java.util.Date auditTime;
	/**退款时间*/
	@Excel(name = "退款时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "退款时间")
    private java.util.Date refundTime;
	/**完成时间*/
	@Excel(name = "完成时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "完成时间")
    private java.util.Date finishTime;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;

    @TableField(exist = false)
    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @TableField(exist = false)
    @ApiModelProperty(value = "店铺头像")
    private String storeAvatar;

    @TableField(exist = false)
    @ApiModelProperty(value = "订单信息")
    private Object orderInfo;

    @TableField(exist = false)
    @ApiModelProperty(value = "处理时间")
    private java.util.Date handleTime;

    /**兼容字段：问题描述*/
    @TableField(exist = false)
    @ApiModelProperty(value = "问题描述(兼容字段)")
    private java.lang.String description;
    
    /**兼容字段：凭证图片*/
    @TableField(exist = false)
    @ApiModelProperty(value = "凭证图片(兼容字段)")
    private java.lang.String images;
    
    /**
     * 获取问题描述(兼容旧代码)
     */
    public java.lang.String getDescription() {
        return this.additionalNote;
    }
    
    /**
     * 设置问题描述(兼容旧代码)
     */
    public void setDescription(java.lang.String description) {
        this.additionalNote = description;
    }
    
    /**
     * 获取凭证图片(兼容旧代码)
     */
    public java.lang.String getImages() {
        return this.userImages;
    }
    
    /**
     * 设置凭证图片(兼容旧代码)
     */
    public void setImages(java.lang.String images) {
        this.userImages = images;
    }
    
    /**
     * 获取售后原因(兼容旧代码)
     */
    public java.lang.String getReason() {
        return this.reasonText;
    }
    
    /**
     * 设置售后原因(兼容旧代码)
     */
    public void setReason(java.lang.String reason) {
        this.reasonText = reason;
        this.oldReason = reason;
    }
}
