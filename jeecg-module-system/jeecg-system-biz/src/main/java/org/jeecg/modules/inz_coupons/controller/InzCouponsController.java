package org.jeecg.modules.inz_coupons.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_coupons.entity.InzCoupons;
import org.jeecg.modules.inz_coupons.service.IInzCouponsService;
import org.jeecg.modules.inz_coupons.service.impl.InzCouponsServiceImpl;
import org.jeecg.modules.inz_store.service.IInzStoreService;
import org.jeecg.modules.inz_users_fronts.service.IInzUsersFrontsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 优惠券表
 * @Author: jeecg-boot
 * @Date: 2025-06-16
 * @Version: V1.0
 */
/*@Api(tags="优惠券表")*/
@RestController
@RequestMapping("/inz_coupons/inzCoupons")
@Slf4j
@Validated
public class InzCouponsController extends JeecgController<InzCoupons, IInzCouponsService> {
    @Autowired
    private IInzCouponsService inzCouponsService;

    @Autowired
    private IInzStoreService inzStoreService;

    @Autowired
    private IInzUsersFrontsService inzUsersFrontsService;

    /**
     * 分页列表查询
     *
     * @param inzCoupons 查询条件
     * @param pageNo     页码
     * @param pageSize   每页记录数
     * @param storeId    店铺ID
     * @param storeName  店铺名称
     * @param req        HTTP请求
     * @return 分页结果
     */
    //@AutoLog(value = "优惠券表-分页列表查询")
    /*@ApiOperation(value="优惠券表-分页列表查询", notes="优惠券表-分页列表查询")*/
    @GetMapping(value = "/list")
    public Result<IPage<InzCoupons>> queryPageList(InzCoupons inzCoupons,
                                                   @RequestParam(name = "pageNo", defaultValue = "1") @Min(value = 1, message = "页码不能小于1") Integer pageNo,
                                                   @RequestParam(name = "pageSize", defaultValue = "10") @Min(value = 1, message = "每页记录数不能小于1") @Max(value = 100, message = "每页记录数不能超过100") Integer pageSize,
                                                   @RequestParam(name = "storeId", required = false) String storeId,
                                                   @RequestParam(name = "storeName", required = false) String storeName,
                                                   @RequestParam(name = "userName", required = false) String username,
                                                   HttpServletRequest req) {
        try {
            // 创建查询条件
            QueryWrapper<InzCoupons> queryWrapper = QueryGenerator.initQueryWrapper(inzCoupons, req.getParameterMap());
            Page<InzCoupons> page = new Page<>(pageNo, pageSize);
            
            // 处理店铺ID筛选
            if (StringUtils.isNotBlank(storeId)) {
                queryWrapper.eq("store_id", storeId);
            }
            
            // 处理店铺名称筛选
            if (StringUtils.isNotBlank(storeName)) {
                // 通过店铺名称获取店铺ID
                String storeIdFromName = inzStoreService.getStoreIdByName(storeName);
                if (StringUtils.isNotBlank(storeIdFromName)) {
                    queryWrapper.eq("store_id", storeIdFromName);
                } else {
                    // 如果找不到店铺，返回空结果
                    return Result.OK(new Page<>(pageNo, pageSize, 0));
                }
            }
            
            // 处理用户名称筛选
            if (StringUtils.isNotBlank(username)) {
                // 通过用户名称获取用户ID
                String userId = inzUsersFrontsService.getUserIdByUsername(username);
                if (StringUtils.isNotBlank(userId)) {
                    // 假设优惠券表中有user_id字段，表示创建者或使用者
                    queryWrapper.eq("user_id", userId);
                } else {
                    // 如果找不到用户，返回空结果
                    return Result.OK(new Page<>(pageNo, pageSize, 0));
                }
            }
            
            // ====== 数据权限控制逻辑开始 ======
            org.jeecg.common.system.vo.LoginUser sysUser = (org.jeecg.common.system.vo.LoginUser) org.apache.shiro.SecurityUtils.getSubject().getPrincipal();
            if (sysUser != null) {
                if (!isAdmin(sysUser)) {
                    log.info("非管理员用户访问优惠券列表，进行数据权限过滤");
                    String loginUsername = sysUser.getUsername();
                    String userId = null;
                    try {
                        userId = inzUsersFrontsService.getUserIdByUsername(loginUsername);
                        log.info("通过用户名[{}]查询到前端用户ID: {}", loginUsername, userId);
                    } catch (Exception e) {
                        log.warn("通过用户名查询前端用户失败: {}", e.getMessage());
                    }
                    if (StringUtils.isBlank(userId)) {
                        try {
                            userId = inzUsersFrontsService.getUserIdByNickname(loginUsername);
                            log.info("通过昵称[{}]查询到前端用户ID: {}", loginUsername, userId);
                        } catch (Exception e) {
                            log.warn("通过昵称查询前端用户失败: {}", e.getMessage());
                        }
                    }
                    if (StringUtils.isNotBlank(userId)) {
                        QueryWrapper<org.jeecg.modules.inz_store.entity.InzStore> storeQueryWrapper = new QueryWrapper<>();
                        storeQueryWrapper.eq("user_id", userId);
                        org.jeecg.modules.inz_store.entity.InzStore store = inzStoreService.getOne(storeQueryWrapper);
                        if (store != null) {
                            log.info("用户[{}]关联店铺[{}]，只能查看该店铺优惠券", userId, store.getId());
                            queryWrapper.eq("store_id", store.getId());
                        } else {
                            log.warn("用户[{}]没有关联店铺，返回空结果", userId);
                            return Result.OK(new Page<>(pageNo, pageSize, 0));
                        }
                    } else {
                        log.warn("未找到后台用户[{}]对应的前端用户，返回空结果", loginUsername);
                        return Result.OK(new Page<>(pageNo, pageSize, 0));
                    }
                } else {
                    log.info("管理员用户访问优惠券列表，不进行数据权限过滤");
                }
            }
            // ====== 数据权限控制逻辑结束 ======
            
            // 执行查询
            IPage<InzCoupons> pageList = inzCouponsService.page(page, queryWrapper);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error("查询优惠券列表失败", e);
            return Result.error("查询优惠券列表失败: " + e.getMessage());
        }
    }

    /**
     * 判断用户是否为管理员
     * @param user 登录用户
     * @return 是否为管理员
     */
    private boolean isAdmin(org.jeecg.common.system.vo.LoginUser user) {
        if (user == null) {
            return false;
        }
        String roleCode = user.getRoleCode();
        return StringUtils.isNotBlank(roleCode) && (roleCode.contains("admin") || roleCode.contains("ADMIN"));
    }

    /**
     * 添加
     *
     * @param inzCoupons 优惠券信息
     * @return 操作结果
     */
	/*@AutoLog(value = "优惠券表-添加")
	@ApiOperation(value="优惠券表-添加", notes="优惠券表-添加")*/
    @RequiresPermissions("inz_coupons:inz_coupon:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody @Validated InzCoupons inzCoupons) {
        try {
            // 基本验证
            if (inzCoupons == null) {
                return Result.error("优惠券信息不能为空");
            }
            
            // 必填字段验证
            if (StringUtils.isBlank(inzCoupons.getName())) {
                return Result.error("优惠券名称不能为空");
            }
            
            // ====== 权限控制逻辑开始 ======
            org.jeecg.common.system.vo.LoginUser sysUser = (org.jeecg.common.system.vo.LoginUser) org.apache.shiro.SecurityUtils.getSubject().getPrincipal();
            if (sysUser != null) {
                if (!isAdmin(sysUser)) {
                    log.info("非管理员用户创建优惠券，进行权限控制");
                    String loginUsername = sysUser.getUsername();
                    String userId = null;
                    try {
                        userId = inzUsersFrontsService.getUserIdByUsername(loginUsername);
                        log.info("通过用户名[{}]查询到前端用户ID: {}", loginUsername, userId);
                    } catch (Exception e) {
                        log.warn("通过用户名查询前端用户失败: {}", e.getMessage());
                    }
                    if (StringUtils.isBlank(userId)) {
                        try {
                            userId = inzUsersFrontsService.getUserIdByNickname(loginUsername);
                            log.info("通过昵称[{}]查询到前端用户ID: {}", loginUsername, userId);
                        } catch (Exception e) {
                            log.warn("通过昵称查询前端用户失败: {}", e.getMessage());
                        }
                    }
                    if (StringUtils.isNotBlank(userId)) {
                        QueryWrapper<org.jeecg.modules.inz_store.entity.InzStore> storeQueryWrapper = new QueryWrapper<>();
                        storeQueryWrapper.eq("user_id", userId);
                        org.jeecg.modules.inz_store.entity.InzStore store = inzStoreService.getOne(storeQueryWrapper);
                        if (store != null) {
                            log.info("用户[{}]关联店铺[{}]，只能为该店铺创建优惠券", userId, store.getId());
                            // 如果用户提交的店铺ID与其关联的店铺不一致，则强制修改为其关联的店铺ID
                            if (StringUtils.isNotBlank(inzCoupons.getStoreId()) && !store.getId().equals(inzCoupons.getStoreId())) {
                                log.warn("用户尝试为其他店铺[{}]创建优惠券，已强制修改为其关联店铺[{}]", inzCoupons.getStoreId(), store.getId());
                                inzCoupons.setStoreId(store.getId());
                            } else if (StringUtils.isBlank(inzCoupons.getStoreId())) {
                                // 如果没有提供店铺ID，则自动设置为用户关联的店铺ID
                                log.info("未提供店铺ID，自动设置为用户关联店铺[{}]", store.getId());
                                inzCoupons.setStoreId(store.getId());
                            }
                        } else {
                            log.warn("用户[{}]没有关联店铺，无法创建优惠券", userId);
                            return Result.error("您没有关联的店铺，无法创建优惠券");
                        }
                    } else {
                        log.warn("未找到后台用户[{}]对应的前端用户，无法创建优惠券", loginUsername);
                        return Result.error("未找到您的用户信息，无法创建优惠券");
                    }
                } else {
                    log.info("管理员用户创建优惠券，不进行店铺权限控制");
                    // 管理员可以为任何店铺创建优惠券，但如果没有指定店铺ID，则提示必须选择店铺
                    if (StringUtils.isBlank(inzCoupons.getStoreId())) {
                        log.warn("管理员未指定店铺ID，无法创建优惠券");
                        return Result.error("请选择要创建优惠券的店铺");
                    }
                }
            }
            // ====== 权限控制逻辑结束 ======
            
            if (inzCoupons.getType() == null) {
                return Result.error("优惠券类型不能为空");
            }
            
            if (inzCoupons.getType() != 1 && inzCoupons.getType() != 2) {
                return Result.error("优惠券类型必须为1(固定金额)或2(折扣)");
            }
            
            if (inzCoupons.getAmount() == null) {
                return Result.error("优惠金额/折扣率不能为空");
            }
            
            if (inzCoupons.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                return Result.error("优惠金额/折扣率必须大于0");
            }
            
            // 折扣类型时，折扣率不能超过10 (假设是十折制)
            if (inzCoupons.getType() == 2 && inzCoupons.getAmount().compareTo(new BigDecimal("10")) > 0) {
                return Result.error("折扣率不能超过10");
            }
            
            if (inzCoupons.getMinAmount() == null) {
                return Result.error("最低使用金额不能为空");
            }
            
            if (inzCoupons.getMinAmount().compareTo(BigDecimal.ZERO) < 0) {
                return Result.error("最低使用金额不能为负数");
            }
            
            // 商品范围验证
            if (inzCoupons.getProductScope() == null) {
                inzCoupons.setProductScope(1); // 默认为通用
            }
            
            if (inzCoupons.getProductScope() != 1 && inzCoupons.getProductScope() != 2) {
                return Result.error("商品范围必须为1(通用)或2(指定商品)");
            }
            
            // 如果是指定商品，验证商品ID列表
            if (inzCoupons.getProductScope() == 2 && StringUtils.isBlank(inzCoupons.getProductIds())) {
                return Result.error("指定商品时，商品ID列表不能为空");
            }
            
            // 有效期验证
            if (inzCoupons.getValidityType() == null) {
                inzCoupons.setValidityType(1); // 默认为固定日期
            }
            
            if (inzCoupons.getValidityType() != 1 && inzCoupons.getValidityType() != 2) {
                return Result.error("有效期类型必须为1(固定日期)或2(领取后N天)");
            }
            
            // 固定日期类型，验证开始和结束时间
            if (inzCoupons.getValidityType() == 1) {
                if (inzCoupons.getStartTime() == null) {
                    return Result.error("生效时间不能为空");
                }
                
                if (inzCoupons.getEndTime() == null) {
                    return Result.error("失效时间不能为空");
                }
                
                if (inzCoupons.getStartTime().after(inzCoupons.getEndTime())) {
                    return Result.error("生效时间不能晚于失效时间");
                }
            }
            
            // 领取后N天类型，验证有效天数
            if (inzCoupons.getValidityType() == 2) {
                if (inzCoupons.getValidityDays() == null) {
                    return Result.error("有效天数不能为空");
                }
                
                if (inzCoupons.getValidityDays() <= 0) {
                    return Result.error("有效天数必须大于0");
                }
                
                // 设置一个默认的开始和结束时间范围（例如一年），实际有效期将在用户领取时计算
                Date now = new Date();
                inzCoupons.setStartTime(now);
                
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(now);
                calendar.add(Calendar.YEAR, 1);
                inzCoupons.setEndTime(calendar.getTime());
            }
            
            // 发放方式验证
            if (inzCoupons.getDistributionType() == null) {
                inzCoupons.setDistributionType(1); // 默认为一码一用
            }
            
            if (inzCoupons.getDistributionType() != 1 && inzCoupons.getDistributionType() != 2) {
                return Result.error("发放方式必须为1(一码一用)或2(一码多用)");
            }
            
            // 发行总量验证
            if (inzCoupons.getTotalQuantity() == null) {
                return Result.error("发行总量不能为空");
            }
            
            if (inzCoupons.getTotalQuantity() <= 0) {
                return Result.error("发行总量必须大于0");
            }
            
            // 剩余数量初始化为发行总量
            inzCoupons.setRemainingQuantity(inzCoupons.getTotalQuantity());
            
            // 状态初始化为启用
            inzCoupons.setStatus(1);
            
            // 保存优惠券
            inzCouponsService.save(inzCoupons);
            return Result.OK("添加成功！");
        } catch (Exception e) {
            log.error("添加优惠券失败", e);
            return Result.error("添加优惠券失败: " + e.getMessage());
        }
    }

    /**
     * 编辑
     *
     * @param inzCoupons 优惠券信息
     * @return 操作结果
     */
	/*@AutoLog(value = "优惠券表-编辑")
	@ApiOperation(value="优惠券表-编辑", notes="优惠券表-编辑")*/
    @RequiresPermissions("inz_coupons:inz_coupon:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody @Validated InzCoupons inzCoupons) {
        try {
            // 基本验证
            if (inzCoupons == null) {
                return Result.error("优惠券信息不能为空");
            }
            
            if (StringUtils.isBlank(inzCoupons.getId())) {
                return Result.error("优惠券ID不能为空");
            }
            
            // 检查优惠券是否存在
            InzCoupons existingCoupon = inzCouponsService.getById(inzCoupons.getId());
            if (existingCoupon == null) {
                return Result.error("未找到对应的优惠券，ID: " + inzCoupons.getId());
            }
            
            // ====== 权限控制逻辑开始 ======
            org.jeecg.common.system.vo.LoginUser sysUser = (org.jeecg.common.system.vo.LoginUser) org.apache.shiro.SecurityUtils.getSubject().getPrincipal();
            if (sysUser != null) {
                if (!isAdmin(sysUser)) {
                    log.info("非管理员用户编辑优惠券，进行权限控制");
                    String loginUsername = sysUser.getUsername();
                    String userId = null;
                    try {
                        userId = inzUsersFrontsService.getUserIdByUsername(loginUsername);
                        log.info("通过用户名[{}]查询到前端用户ID: {}", loginUsername, userId);
                    } catch (Exception e) {
                        log.warn("通过用户名查询前端用户失败: {}", e.getMessage());
                    }
                    if (StringUtils.isBlank(userId)) {
                        try {
                            userId = inzUsersFrontsService.getUserIdByNickname(loginUsername);
                            log.info("通过昵称[{}]查询到前端用户ID: {}", loginUsername, userId);
                        } catch (Exception e) {
                            log.warn("通过昵称查询前端用户失败: {}", e.getMessage());
                        }
                    }
                    if (StringUtils.isNotBlank(userId)) {
                        QueryWrapper<org.jeecg.modules.inz_store.entity.InzStore> storeQueryWrapper = new QueryWrapper<>();
                        storeQueryWrapper.eq("user_id", userId);
                        org.jeecg.modules.inz_store.entity.InzStore store = inzStoreService.getOne(storeQueryWrapper);
                        if (store != null) {
                            log.info("用户[{}]关联店铺[{}]，只能编辑该店铺优惠券", userId, store.getId());
                            // 检查要编辑的优惠券是否属于该用户的店铺
                            if (!store.getId().equals(existingCoupon.getStoreId())) {
                                log.warn("用户尝试编辑其他店铺[{}]的优惠券，拒绝操作", existingCoupon.getStoreId());
                                return Result.error("您无权编辑其他店铺的优惠券");
                            }
                            
                            // 确保用户不能修改优惠券所属的店铺
                            if (StringUtils.isNotBlank(inzCoupons.getStoreId()) && !store.getId().equals(inzCoupons.getStoreId())) {
                                log.warn("用户尝试将优惠券店铺ID从[{}]修改为[{}]，已强制保持原店铺ID", existingCoupon.getStoreId(), inzCoupons.getStoreId());
                                inzCoupons.setStoreId(existingCoupon.getStoreId());
                            }
                        } else {
                            log.warn("用户[{}]没有关联店铺，无法编辑优惠券", userId);
                            return Result.error("您没有关联的店铺，无法编辑优惠券");
                        }
                    } else {
                        log.warn("未找到后台用户[{}]对应的前端用户，无法编辑优惠券", loginUsername);
                        return Result.error("未找到您的用户信息，无法编辑优惠券");
                    }
                } else {
                    log.info("管理员用户编辑优惠券，不进行店铺权限控制");
                    // 管理员可以编辑任何店铺的优惠券，但不允许修改优惠券所属的店铺
                    if (StringUtils.isNotBlank(inzCoupons.getStoreId()) && !existingCoupon.getStoreId().equals(inzCoupons.getStoreId())) {
                        log.warn("管理员尝试将优惠券店铺ID从[{}]修改为[{}]，已强制保持原店铺ID", existingCoupon.getStoreId(), inzCoupons.getStoreId());
                        inzCoupons.setStoreId(existingCoupon.getStoreId());
                    }
                }
            }
            // ====== 权限控制逻辑结束 ======
            
            // 必填字段验证
            if (StringUtils.isBlank(inzCoupons.getName())) {
                return Result.error("优惠券名称不能为空");
            }
            
            if (inzCoupons.getType() == null) {
                return Result.error("优惠券类型不能为空");
            }
            
            if (inzCoupons.getType() != 1 && inzCoupons.getType() != 2) {
                return Result.error("优惠券类型必须为1(固定金额)或2(折扣)");
            }
            
            if (inzCoupons.getAmount() == null) {
                return Result.error("优惠金额/折扣率不能为空");
            }
            
            if (inzCoupons.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                return Result.error("优惠金额/折扣率必须大于0");
            }
            
            // 折扣类型时，折扣率不能超过10 (假设是十折制)
            if (inzCoupons.getType() == 2 && inzCoupons.getAmount().compareTo(new BigDecimal("10")) > 0) {
                return Result.error("折扣率不能超过10");
            }
            
            if (inzCoupons.getMinAmount() == null) {
                return Result.error("最低使用金额不能为空");
            }
            
            if (inzCoupons.getMinAmount().compareTo(BigDecimal.ZERO) < 0) {
                return Result.error("最低使用金额不能为负数");
            }
            
            // 商品范围验证
            if (inzCoupons.getProductScope() == null) {
                inzCoupons.setProductScope(1); // 默认为通用
            }
            
            if (inzCoupons.getProductScope() != 1 && inzCoupons.getProductScope() != 2) {
                return Result.error("商品范围必须为1(通用)或2(指定商品)");
            }
            
            // 如果是指定商品，验证商品ID列表
            if (inzCoupons.getProductScope() == 2 && StringUtils.isBlank(inzCoupons.getProductIds())) {
                return Result.error("指定商品时，商品ID列表不能为空");
            }
            
            // 有效期验证
            if (inzCoupons.getValidityType() == null) {
                inzCoupons.setValidityType(1); // 默认为固定日期
            }
            
            if (inzCoupons.getValidityType() != 1 && inzCoupons.getValidityType() != 2) {
                return Result.error("有效期类型必须为1(固定日期)或2(领取后N天)");
            }
            
            // 固定日期类型，验证开始和结束时间
            if (inzCoupons.getValidityType() == 1) {
                if (inzCoupons.getStartTime() == null) {
                    return Result.error("生效时间不能为空");
                }
                
                if (inzCoupons.getEndTime() == null) {
                    return Result.error("失效时间不能为空");
                }
                
                if (inzCoupons.getStartTime().after(inzCoupons.getEndTime())) {
                    return Result.error("生效时间不能晚于失效时间");
                }
            }
            
            // 领取后N天类型，验证有效天数
            if (inzCoupons.getValidityType() == 2) {
                if (inzCoupons.getValidityDays() == null) {
                    return Result.error("有效天数不能为空");
                }
                
                if (inzCoupons.getValidityDays() <= 0) {
                    return Result.error("有效天数必须大于0");
                }
            }
            
            // 发放方式验证
            if (inzCoupons.getDistributionType() == null) {
                inzCoupons.setDistributionType(1); // 默认为一码一用
            }
            
            if (inzCoupons.getDistributionType() != 1 && inzCoupons.getDistributionType() != 2) {
                return Result.error("发放方式必须为1(一码一用)或2(一码多用)");
            }
            
            // 保留原始的发行总量和剩余数量
            inzCoupons.setTotalQuantity(existingCoupon.getTotalQuantity());
            inzCoupons.setRemainingQuantity(existingCoupon.getRemainingQuantity());
            
            // 更新优惠券
            inzCouponsService.updateById(inzCoupons);
            return Result.OK("编辑成功!");
        } catch (Exception e) {
            log.error("编辑优惠券失败", e);
            return Result.error("编辑优惠券失败: " + e.getMessage());
        }
    }

    /**
     * 通过id删除
     *
     * @param id 优惠券ID
     * @return 操作结果
     */
	/*@AutoLog(value = "优惠券表-通过id删除")
	@ApiOperation(value="优惠券表-通过id删除", notes="优惠券表-通过id删除")*/
    @RequiresPermissions("inz_coupons:inz_coupon:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) @NotBlank(message = "优惠券ID不能为空") String id) {
        try {
            if (StringUtils.isBlank(id)) {
                return Result.error("优惠券ID不能为空");
            }
            
            // 检查优惠券是否存在
            InzCoupons existingCoupon = inzCouponsService.getById(id);
            if (existingCoupon == null) {
                return Result.error("未找到对应的优惠券，ID: " + id);
            }
            
            // 检查优惠券是否已被领取
            int usedQuantity = existingCoupon.getTotalQuantity() - existingCoupon.getRemainingQuantity();
            if (usedQuantity > 0) {
                return Result.error("该优惠券已被领取使用，不能删除。请考虑禁用该优惠券。");
            }
            
            // ====== 权限控制逻辑开始 ======
            org.jeecg.common.system.vo.LoginUser sysUser = (org.jeecg.common.system.vo.LoginUser) org.apache.shiro.SecurityUtils.getSubject().getPrincipal();
            if (sysUser != null) {
                if (!isAdmin(sysUser)) {
                    log.info("非管理员用户删除优惠券，进行权限控制");
                    String loginUsername = sysUser.getUsername();
                    String userId = null;
                    try {
                        userId = inzUsersFrontsService.getUserIdByUsername(loginUsername);
                        log.info("通过用户名[{}]查询到前端用户ID: {}", loginUsername, userId);
                    } catch (Exception e) {
                        log.warn("通过用户名查询前端用户失败: {}", e.getMessage());
                    }
                    if (StringUtils.isBlank(userId)) {
                        try {
                            userId = inzUsersFrontsService.getUserIdByNickname(loginUsername);
                            log.info("通过昵称[{}]查询到前端用户ID: {}", loginUsername, userId);
                        } catch (Exception e) {
                            log.warn("通过昵称查询前端用户失败: {}", e.getMessage());
                        }
                    }
                    if (StringUtils.isNotBlank(userId)) {
                        QueryWrapper<org.jeecg.modules.inz_store.entity.InzStore> storeQueryWrapper = new QueryWrapper<>();
                        storeQueryWrapper.eq("user_id", userId);
                        org.jeecg.modules.inz_store.entity.InzStore store = inzStoreService.getOne(storeQueryWrapper);
                        if (store != null) {
                            log.info("用户[{}]关联店铺[{}]，只能删除该店铺优惠券", userId, store.getId());
                            // 检查要删除的优惠券是否属于该用户的店铺
                            if (!store.getId().equals(existingCoupon.getStoreId())) {
                                log.warn("用户尝试删除其他店铺[{}]的优惠券，拒绝操作", existingCoupon.getStoreId());
                                return Result.error("您无权删除其他店铺的优惠券");
                            }
                        } else {
                            log.warn("用户[{}]没有关联店铺，无法删除优惠券", userId);
                            return Result.error("您没有关联的店铺，无法删除优惠券");
                        }
                    } else {
                        log.warn("未找到后台用户[{}]对应的前端用户，无法删除优惠券", loginUsername);
                        return Result.error("未找到您的用户信息，无法删除优惠券");
                    }
                } else {
                    log.info("管理员用户删除优惠券，不进行店铺权限控制");
                }
            }
            // ====== 权限控制逻辑结束 ======
            
            inzCouponsService.removeById(id);
            return Result.OK("删除成功!");
        } catch (Exception e) {
            log.error("删除优惠券失败", e);
            return Result.error("删除优惠券失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除
     *
     * @param ids 优惠券ID列表，逗号分隔
     * @return 操作结果
     */
	/*@AutoLog(value = "优惠券表-批量删除")
	@ApiOperation(value="优惠券表-批量删除", notes="优惠券表-批量删除")*/
    @RequiresPermissions("inz_coupons:inz_coupon:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) @NotBlank(message = "优惠券ID列表不能为空") String ids) {
        try {
            if (StringUtils.isBlank(ids)) {
                return Result.error("优惠券ID列表不能为空");
            }
            
            // 分割ID字符串
            String[] idArray = ids.split(",");
            if (idArray.length == 0) {
                return Result.error("优惠券ID列表格式不正确");
            }
            
            // ====== 权限控制逻辑开始 ======
            org.jeecg.common.system.vo.LoginUser sysUser = (org.jeecg.common.system.vo.LoginUser) org.apache.shiro.SecurityUtils.getSubject().getPrincipal();
            boolean isAdminUser = false;
            String userStoreId = null;
            
            if (sysUser != null) {
                isAdminUser = isAdmin(sysUser);
                if (!isAdminUser) {
                    log.info("非管理员用户批量删除优惠券，进行权限控制");
                    String loginUsername = sysUser.getUsername();
                    String userId = null;
                    try {
                        userId = inzUsersFrontsService.getUserIdByUsername(loginUsername);
                        log.info("通过用户名[{}]查询到前端用户ID: {}", loginUsername, userId);
                    } catch (Exception e) {
                        log.warn("通过用户名查询前端用户失败: {}", e.getMessage());
                    }
                    if (StringUtils.isBlank(userId)) {
                        try {
                            userId = inzUsersFrontsService.getUserIdByNickname(loginUsername);
                            log.info("通过昵称[{}]查询到前端用户ID: {}", loginUsername, userId);
                        } catch (Exception e) {
                            log.warn("通过昵称查询前端用户失败: {}", e.getMessage());
                        }
                    }
                    if (StringUtils.isNotBlank(userId)) {
                        QueryWrapper<org.jeecg.modules.inz_store.entity.InzStore> storeQueryWrapper = new QueryWrapper<>();
                        storeQueryWrapper.eq("user_id", userId);
                        org.jeecg.modules.inz_store.entity.InzStore store = inzStoreService.getOne(storeQueryWrapper);
                        if (store != null) {
                            userStoreId = store.getId();
                            log.info("用户[{}]关联店铺[{}]，只能删除该店铺优惠券", userId, userStoreId);
                        } else {
                            log.warn("用户[{}]没有关联店铺，无法删除优惠券", userId);
                            return Result.error("您没有关联的店铺，无法删除优惠券");
                        }
                    } else {
                        log.warn("未找到后台用户[{}]对应的前端用户，无法删除优惠券", loginUsername);
                        return Result.error("未找到您的用户信息，无法删除优惠券");
                    }
                } else {
                    log.info("管理员用户批量删除优惠券，不进行店铺权限控制");
                }
            }
            // ====== 权限控制逻辑结束 ======
            
            // 检查每个优惠券的权限和使用状态
            List<String> validIds = new ArrayList<>();
            List<String> invalidIds = new ArrayList<>();
            List<String> noPermissionIds = new ArrayList<>();
            List<String> usedIds = new ArrayList<>();
            
            for (String id : idArray) {
                if (StringUtils.isBlank(id)) {
                    continue;
                }
                
                // 检查优惠券是否存在
                InzCoupons coupon = inzCouponsService.getById(id);
                if (coupon == null) {
                    invalidIds.add(id);
                    continue;
                }
                
                // 检查权限
                if (!isAdminUser && userStoreId != null && !userStoreId.equals(coupon.getStoreId())) {
                    noPermissionIds.add(id);
                    continue;
                }
                
                // 检查是否已被使用
                int usedQuantity = coupon.getTotalQuantity() - coupon.getRemainingQuantity();
                if (usedQuantity > 0) {
                    usedIds.add(id);
                    continue;
                }
                
                validIds.add(id);
            }
            
            // 处理结果
            if (validIds.isEmpty()) {
                StringBuilder errorMsg = new StringBuilder("没有可删除的优惠券：");
                if (!invalidIds.isEmpty()) {
                    errorMsg.append(invalidIds.size()).append("个不存在，");
                }
                if (!noPermissionIds.isEmpty()) {
                    errorMsg.append(noPermissionIds.size()).append("个无权限删除，");
                }
                if (!usedIds.isEmpty()) {
                    errorMsg.append(usedIds.size()).append("个已被使用");
                }
                return Result.error(errorMsg.toString());
            }
            
            // 执行删除操作
            inzCouponsService.removeByIds(validIds);
            
            // 构建返回消息
            StringBuilder resultMsg = new StringBuilder("成功删除").append(validIds.size()).append("个优惠券");
            if (!invalidIds.isEmpty() || !noPermissionIds.isEmpty() || !usedIds.isEmpty()) {
                resultMsg.append("，但有");
                if (!invalidIds.isEmpty()) {
                    resultMsg.append(invalidIds.size()).append("个不存在，");
                }
                if (!noPermissionIds.isEmpty()) {
                    resultMsg.append(noPermissionIds.size()).append("个无权限删除，");
                }
                if (!usedIds.isEmpty()) {
                    resultMsg.append(usedIds.size()).append("个已被使用");
                }
            }
            
            return Result.OK(resultMsg.toString());
        } catch (Exception e) {
            log.error("批量删除优惠券失败", e);
            return Result.error("批量删除优惠券失败: " + e.getMessage());
        }
    }

    /**
     * 通过id查询
     *
     * @param id 优惠券ID
     * @return 优惠券信息
     */
    //@AutoLog(value = "优惠券表-通过id查询")
    /*@ApiOperation(value="优惠券表-通过id查询", notes="优惠券表-通过id查询")*/
    @GetMapping(value = "/queryById")
    public Result<InzCoupons> queryById(@RequestParam(name = "id", required = true) @NotBlank(message = "优惠券ID不能为空") String id) {
        try {
            if (StringUtils.isBlank(id)) {
                return Result.error("优惠券ID不能为空");
            }
            
        InzCoupons inzCoupons = inzCouponsService.getById(id);
        if (inzCoupons == null) {
                return Result.error("未找到对应数据，ID: " + id);
        }
        return Result.OK(inzCoupons);
        } catch (Exception e) {
            log.error("查询优惠券失败", e);
            return Result.error("查询优惠券失败: " + e.getMessage());
        }
    }

    /**
     * 导出excel
     *
     * @param request HTTP请求
     * @param inzCoupons 查询条件
     * @return ModelAndView
     */
    @RequiresPermissions("inz_coupons:inz_coupon:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzCoupons inzCoupons) {
        return super.exportXls(request, inzCoupons, InzCoupons.class, "优惠券表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 导入结果
     */
    @RequiresPermissions("inz_coupons:inz_coupon:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzCoupons.class);
    }

    /**
     * 禁用优惠券
     *
     * @return 操作结果
     */
    @ApiOperation(value = "禁用优惠券", notes = "禁用指定的优惠券")
    @PostMapping(value = "/disableCoupon")
    public Result<String> disableCoupon(@RequestBody InzCoupons  coupon) {
        try {
            if (StringUtils.isBlank(coupon.getId())) {
                return Result.error("优惠券ID不能为空");
            }
            
            // 检查优惠券是否存在
            InzCoupons coupon1 = inzCouponsService.getById(coupon.getId());
            if (coupon1 == null) {
                return Result.error("优惠券不存在，ID: " + coupon.getId());
            }
            
            // 检查优惠券是否已禁用
            if (coupon.getStatus() != null && coupon.getStatus() == 0) {
                return Result.error("优惠券已经是禁用状态");
            }
            
            inzCouponsService.disableCoupon(coupon.getId());
            return Result.OK();
        } catch (JeecgBootException e) {
            log.error("禁用优惠券失败", e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("禁用优惠券失败", e);
            return Result.error("禁用优惠券失败：" + e.getMessage());
        }
    }

    /**
     * 禁用兑换码
     *
     * @param code 兑换码
     * @return 操作结果
     */
    @ApiOperation(value = "禁用兑换码", notes = "禁用指定的兑换码")
    @RequiresPermissions("inz_coupons:inz_coupon:disableCode")
    @PostMapping(value = "/disableCouponCode")
    public Result<String> disableCouponCode(@RequestParam(name = "code", required = true) @NotBlank(message = "兑换码不能为空") String code) {
        try {
            if (StringUtils.isBlank(code)) {
                return Result.error("兑换码不能为空");
            }
            
            // 验证兑换码格式
            if (!code.matches("^[A-Z0-9]{16}$")) {
                return Result.error("兑换码格式不正确，应为16位大写字母和数字的组合");
            }
            
            // 检查兑换码是否已被禁用
            if (((InzCouponsServiceImpl)inzCouponsService).isCouponCodeDisabled(code)) {
                return Result.error("该兑换码已被禁用，无需重复操作");
            }
            
            inzCouponsService.disableCouponCode(code);
            return Result.OK("兑换码已禁用");
        } catch (JeecgBootException e) {
            log.error("禁用兑换码失败", e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("禁用兑换码失败", e);
            return Result.error("禁用兑换码失败：" + e.getMessage());
        }
    }

    /**
     * 生成兑换码
     *
     * @param type 优惠券类型（1：固定金额，2：折扣）
     * @param amount 优惠金额/折扣率
     * @param minAmount 最低使用金额
     * @param count 生成数量
     * @param storeId 店铺ID（可选）
     * @return 生成的兑换码列表
     */
    @ApiOperation(value = "生成兑换码", notes = "根据参数直接生成兑换码")
    @RequiresPermissions("inz_coupons:inz_coupon:generateCode")
    @PostMapping(value = "/generateCouponCode")
    public Result<Map<String, Object>> generateCouponCode(
            @RequestParam(name = "type", required = true) @NotNull(message = "优惠券类型不能为空") @ApiParam(value = "优惠券类型（1：固定金额，2：折扣）", required = true) Integer type,
            @RequestParam(name = "amount", required = true) @NotNull(message = "优惠金额/折扣率不能为空") @ApiParam(value = "优惠金额/折扣率", required = true) BigDecimal amount,
            @RequestParam(name = "minAmount", required = true) @NotNull(message = "最低使用金额不能为空") @ApiParam(value = "最低使用金额", required = true) BigDecimal minAmount,
            @RequestParam(name = "count", required = true) @NotNull(message = "生成数量不能为空") @Min(value = 1, message = "生成数量必须大于0") @Max(value = 1000, message = "单次生成数量不能超过1000") @ApiParam(value = "生成数量", required = true) Integer count,
            @RequestParam(name = "storeId", required = false) @ApiParam(value = "店铺ID", required = false) String storeId) {
        try {
            // 输入验证
            if (type == null) {
                return Result.error("优惠券类型不能为空");
            }
            
            if (type != 1 && type != 2) {
                return Result.error("优惠券类型必须为1(固定金额)或2(折扣)");
            }
            
            if (amount == null) {
                return Result.error("优惠金额/折扣率不能为空");
            }
            
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                return Result.error("优惠金额/折扣率必须大于0");
            }
            
            // 折扣类型时，折扣率不能超过10 (假设是十折制)
            if (type == 2 && amount.compareTo(new BigDecimal("10")) > 0) {
                return Result.error("折扣率不能超过10");
            }
            
            if (minAmount == null) {
                return Result.error("最低使用金额不能为空");
            }
            
            if (minAmount.compareTo(BigDecimal.ZERO) < 0) {
                return Result.error("最低使用金额不能为负数");
            }
            
            if (count == null) {
                return Result.error("生成数量不能为空");
            }
            
            if (count <= 0) {
                return Result.error("生成数量必须大于0");
            }
            
            if (count > 1000) {
                return Result.error("单次生成数量不能超过1000");
            }
            
            return inzCouponsService.generateCouponCode(type, amount, minAmount, count, storeId);
        } catch (JeecgBootException e) {
            log.error("生成兑换码失败", e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("生成兑换码失败", e);
            return Result.error("生成兑换码失败：" + e.getMessage());
        }
    }

    @GetMapping(value = "/couponsByStoreName")
    public Result<IPage<InzCoupons>> queryCouponsByStoreName(
            @RequestParam(name = "storeName") String storeName,
            @RequestParam(name = "pageNo", defaultValue = "1") @Min(value = 1, message = "页码不能小于1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") @Min(value = 1, message = "每页记录数不能小于1") @Max(value = 100, message = "每页记录数不能超过100") Integer pageSize) {
        try {
            Page<InzCoupons> page = new Page<>(pageNo, pageSize);
            IPage<InzCoupons> pageList = inzCouponsService.queryCouponsByStoreName(page, storeName);
        return Result.OK(pageList);
        } catch (Exception e) {
            log.error("查询优惠券列表失败", e);
            return Result.error("查询优惠券列表失败: " + e.getMessage());
        }
    }
}