package org.jeecg.modules.inz_product_settlement.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_product_settlement.entity.InzProductSettlement;
import org.jeecg.modules.inz_product_settlement.service.IInzProductSettlementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @Description: 商品结算
 * @Author: jeecg-boot
 * @Date: 2025-06-26
 * @Version: V1.0
 */
@Api(tags = "商品结算")
@RestController
@RequestMapping("/inz_product_settlement/inzProductSettlement")
@Slf4j
public class InzProductSettlementController extends JeecgController<InzProductSettlement, IInzProductSettlementService> {
    @Autowired
    private IInzProductSettlementService inzProductSettlementService;

    /**
     * 分页列表查询
     *
     * @param inzProductSettlement
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "商品结算-分页列表查询")
    @ApiOperation(value = "商品结算-分页列表查询", notes = "商品结算-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(InzProductSettlement inzProductSettlement,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   @RequestParam(name = "settlementStatus", required = false) Integer settlementStatus,
                                   HttpServletRequest req) {
        QueryWrapper<InzProductSettlement> queryWrapper = QueryGenerator.initQueryWrapper(inzProductSettlement, req.getParameterMap());

        if(settlementStatus != null){
            queryWrapper.eq("settlement_status", settlementStatus);
        }

        Page<InzProductSettlement> page = new Page<InzProductSettlement>(pageNo, pageSize);
        IPage<InzProductSettlement> pageList = inzProductSettlementService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param inzProductSettlement
     * @return
     */
    @AutoLog(value = "商品结算-添加")
    @ApiOperation(value = "商品结算-添加", notes = "商品结算-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody InzProductSettlement inzProductSettlement) {
        // 自动计算结算金额
        inzProductSettlement.calculateAllAmounts();
        inzProductSettlementService.save(inzProductSettlement);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param inzProductSettlement
     * @return
     */
    @AutoLog(value = "商品结算-编辑")
    @ApiOperation(value = "商品结算-编辑", notes = "商品结算-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody InzProductSettlement inzProductSettlement) {
        // 重新计算结算金额
        inzProductSettlement.calculateAllAmounts();
        inzProductSettlementService.updateById(inzProductSettlement);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "商品结算-通过id删除")
    @ApiOperation(value = "商品结算-通过id删除", notes = "商品结算-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        inzProductSettlementService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "商品结算-批量删除")
    @ApiOperation(value = "商品结算-批量删除", notes = "商品结算-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzProductSettlementService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "商品结算-通过id查询")
    @ApiOperation(value = "商品结算-通过id查询", notes = "商品结算-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        InzProductSettlement inzProductSettlement = inzProductSettlementService.getById(id);
        if (inzProductSettlement == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzProductSettlement);
    }


    /**
     * 创建结算记录
     *
     * @param orderId
     * @return
     */
    @AutoLog(value = "商品结算-创建结算记录")
    @ApiOperation(value = "商品结算-创建结算记录", notes = "根据订单ID创建结算记录")
    @PostMapping(value = "/createSettlement")
    public Result<?> createSettlement(@RequestParam(name = "orderId", required = true) String orderId) {
        try {
            boolean success = inzProductSettlementService.createSettlementRecord(orderId);
            if (success) {
                return Result.OK("创建结算记录成功");
            } else {
                return Result.error("创建结算记录失败");
            }
        } catch (Exception e) {
            log.error("创建结算记录异常", e);
            return Result.error("创建结算记录异常：" + e.getMessage());
        }
    }

    /**
     * 批量结算
     *
     * @param settlementIds
     * @param batchName
     * @param remark
     * @return
     */
    @AutoLog(value = "商品结算-批量结算")
    @ApiOperation(value = "商品结算-批量结算", notes = "批量处理结算记录")
    @PostMapping(value = "/batchSettle")
    public Result<?> batchSettle(@RequestParam(name = "settlementIds", required = true) String settlementIds,
                                 @RequestParam(name = "batchName", required = false) String batchName,
                                 @RequestParam(name = "remark", required = false) String remark) {
        try {
            List<String> idList = Arrays.asList(settlementIds.split(","));
            boolean success = inzProductSettlementService.batchSettle(idList, batchName, remark);
            if (success) {
                return Result.OK("批量结算成功");
            } else {
                return Result.error("批量结算失败");
            }
        } catch (Exception e) {
            log.error("批量结算异常", e);
            return Result.error("批量结算异常：" + e.getMessage());
        }
    }

    /**
     * 计算结算金额
     *
     * @param orderId
     * @return
     */
    @AutoLog(value = "商品结算-计算结算金额")
    @ApiOperation(value = "商品结算-计算结算金额", notes = "计算订单的结算金额")
    @GetMapping(value = "/calculateSettlement")
    public Result<?> calculateSettlement(@RequestParam(name = "orderId", required = true) String orderId) {
        try {
            Map<String, Object> result = inzProductSettlementService.calculateSettlement(orderId);
            return Result.OK(result);
        } catch (Exception e) {
            log.error("计算结算金额异常", e);
            return Result.error("计算结算金额异常：" + e.getMessage());
        }
    }

    /**
     * 获取店铺结算统计
     *
     * @param storeId
     * @param startTime
     * @param endTime
     * @return
     */
    @AutoLog(value = "商品结算-店铺结算统计")
    @ApiOperation(value = "商品结算-店铺结算统计", notes = "获取店铺结算统计数据")
    @GetMapping(value = "/storeStatistics")
    public Result<?> getStoreStatistics(@RequestParam(name = "storeId", required = true) String storeId,
                                        @RequestParam(name = "startTime", required = false) Date startTime,
                                        @RequestParam(name = "endTime", required = false) Date endTime) {
        try {
            if (startTime == null) {
                Calendar cal = Calendar.getInstance();
                cal.add(Calendar.MONTH, -1);
                startTime = cal.getTime();
            }
            if (endTime == null) {
                endTime = new Date();
            }

            Map<String, Object> result = inzProductSettlementService.getStoreSettlementStatistics(storeId, startTime, endTime);
            return Result.OK(result);
        } catch (Exception e) {
            log.error("获取店铺结算统计异常", e);
            return Result.error("获取统计数据异常：" + e.getMessage());
        }
    }

    /**
     * 获取平台结算统计
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @AutoLog(value = "商品结算-平台结算统计")
    @ApiOperation(value = "商品结算-平台结算统计", notes = "获取平台整体结算统计数据")
    @GetMapping(value = "/platformStatistics")
    public Result<?> getPlatformStatistics(@RequestParam(name = "startTime", required = false) Date startTime,
                                           @RequestParam(name = "endTime", required = false) Date endTime) {
        try {
            if (startTime == null) {
                Calendar cal = Calendar.getInstance();
                cal.add(Calendar.MONTH, -1);
                startTime = cal.getTime();
            }
            if (endTime == null) {
                endTime = new Date();
            }

            Map<String, Object> result = inzProductSettlementService.getPlatformSettlementStatistics(startTime, endTime);
            return Result.OK(result);
        } catch (Exception e) {
            log.error("获取平台结算统计异常", e);
            return Result.error("获取统计数据异常：" + e.getMessage());
        }
    }

    /**
     * 获取店铺佣金排行榜
     *
     * @param startTime
     * @param endTime
     * @param limit
     * @return
     */
    @AutoLog(value = "商品结算-店铺佣金排行榜")
    @ApiOperation(value = "商品结算-店铺佣金排行榜", notes = "获取店铺佣金贡献排行榜")
    @GetMapping(value = "/commissionRanking")
    public Result<?> getCommissionRanking(@RequestParam(name = "startTime", required = false) Date startTime,
                                          @RequestParam(name = "endTime", required = false) Date endTime,
                                          @RequestParam(name = "limit", defaultValue = "10") Integer limit) {
        try {
            if (startTime == null) {
                Calendar cal = Calendar.getInstance();
                cal.add(Calendar.MONTH, -1);
                startTime = cal.getTime();
            }
            if (endTime == null) {
                endTime = new Date();
            }

            List<Map<String, Object>> result = inzProductSettlementService.getStoreCommissionRanking(startTime, endTime, limit);
            return Result.OK(result);
        } catch (Exception e) {
            log.error("获取佣金排行榜异常", e);
            return Result.error("获取排行榜异常：" + e.getMessage());
        }
    }

    /**
     * 取消结算
     *
     * @param settlementId
     * @param reason
     * @return
     */
    @AutoLog(value = "商品结算-取消结算")
    @ApiOperation(value = "商品结算-取消结算", notes = "取消指定的结算记录")
    @PostMapping(value = "/cancelSettlement")
    public Result<?> cancelSettlement(@RequestParam(name = "settlementId", required = true) String settlementId,
                                      @RequestParam(name = "reason", required = false) String reason) {
        try {
            boolean success = inzProductSettlementService.cancelSettlement(settlementId, reason);
            if (success) {
                return Result.OK("取消结算成功");
            } else {
                return Result.error("取消结算失败");
            }
        } catch (Exception e) {
            log.error("取消结算异常", e);
            return Result.error("取消结算异常：" + e.getMessage());
        }
    }

    /**
     * 重新计算结算金额
     *
     * @param settlementId
     * @return
     */
    @AutoLog(value = "商品结算-重新计算")
    @ApiOperation(value = "商品结算-重新计算", notes = "重新计算结算记录的金额")
    @PostMapping(value = "/recalculate")
    public Result<?> recalculateSettlement(@RequestParam(name = "settlementId", required = true) String settlementId) {
        try {
            boolean success = inzProductSettlementService.recalculateSettlement(settlementId);
            if (success) {
                return Result.OK("重新计算成功");
            } else {
                return Result.error("重新计算失败");
            }
        } catch (Exception e) {
            log.error("重新计算异常", e);
            return Result.error("重新计算异常：" + e.getMessage());
        }
    }

    /**
     * 导出excel
     *
     * @param request
     * @param inzProductSettlement
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzProductSettlement inzProductSettlement) {
        return super.exportXls(request, inzProductSettlement, InzProductSettlement.class, "商品结算");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzProductSettlement.class);
    }
}
