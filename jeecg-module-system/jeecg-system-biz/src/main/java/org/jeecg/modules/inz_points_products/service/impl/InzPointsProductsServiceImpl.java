package org.jeecg.modules.inz_points_products.service.impl;

import org.jeecg.modules.inz_points_products.entity.InzPointsProducts;
import org.jeecg.modules.inz_points_products.mapper.InzPointsProductsMapper;
import org.jeecg.modules.inz_points_products.service.IInzPointsProductsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 积分商品表
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Service
public class InzPointsProductsServiceImpl extends ServiceImpl<InzPointsProductsMapper, InzPointsProducts> implements IInzPointsProductsService {

}
