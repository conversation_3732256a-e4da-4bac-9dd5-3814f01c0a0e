package org.jeecg.modules.system.util;

import org.jeecg.common.util.encryption.AesEncryptUtil;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * @Description: 传输安全工具类
 * @Author: jeecg-boot
 * @Date: 2025-01-24
 * @Version: V1.0
 */
public class TransmissionSecurityUtil {

    // AES密钥（生产环境应从配置文件读取）
    private static final String AES_KEY = "CardVerse2025Key"; // 16位密钥
    
    /**
     * 验证请求是否使用HTTPS
     * @param request HTTP请求
     * @return 是否为HTTPS
     */
    public static boolean isHttpsRequest(javax.servlet.http.HttpServletRequest request) {
        return "https".equalsIgnoreCase(request.getScheme()) || 
               "https".equalsIgnoreCase(request.getHeader("X-Forwarded-Proto"));
    }
    
    /**
     * 强制HTTPS检查
     * @param request HTTP请求
     * @throws SecurityException 如果不是HTTPS请求
     */
    public static void enforceHttps(javax.servlet.http.HttpServletRequest request) {
        if (!isHttpsRequest(request)) {
            throw new SecurityException("此接口仅支持HTTPS访问，请使用安全连接");
        }
    }
    
    /**
     * AES加密密码
     * @param password 明文密码
     * @return 加密后的密码
     */
    public static String encryptPassword(String password) {
        if (StringUtils.isEmpty(password)) {
            return password;
        }
        
        try {
            SecretKeySpec secretKey = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            
            byte[] encryptedBytes = cipher.doFinal(password.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            throw new RuntimeException("密码加密失败", e);
        }
    }
    
    /**
     * AES解密密码
     * @param encryptedPassword 加密的密码
     * @return 明文密码
     */
    public static String decryptPassword(String encryptedPassword) {
        if (StringUtils.isEmpty(encryptedPassword)) {
            return encryptedPassword;
        }
        
        try {
            SecretKeySpec secretKey = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedPassword));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("密码解密失败", e);
        }
    }
    
    /**
     * 生成随机AES密钥
     * @return Base64编码的密钥
     */
    public static String generateAESKey() {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
            keyGenerator.init(128, new SecureRandom());
            SecretKey secretKey = keyGenerator.generateKey();
            return Base64.getEncoder().encodeToString(secretKey.getEncoded());
        } catch (Exception e) {
            throw new RuntimeException("生成AES密钥失败", e);
        }
    }
    
    /**
     * 验证请求头中的安全标识
     * @param request HTTP请求
     * @return 是否包含安全标识
     */
    public static boolean hasSecurityHeaders(javax.servlet.http.HttpServletRequest request) {
        // 检查是否包含安全相关的请求头
        String userAgent = request.getHeader("User-Agent");
        String referer = request.getHeader("Referer");
        String origin = request.getHeader("Origin");
        
        // 基本的安全检查
        if (StringUtils.isEmpty(userAgent)) {
            return false; // 缺少User-Agent可能是恶意请求
        }
        
        // 检查是否来自可信域名（生产环境需要配置）
        if (!StringUtils.isEmpty(origin)) {
            // 这里应该检查origin是否在白名单中
            // 示例：return TRUSTED_ORIGINS.contains(origin);
        }
        
        return true;
    }
    
    /**
     * 生成请求签名（防篡改）
     * @param data 要签名的数据
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @return 签名
     */
    public static String generateSignature(String data, long timestamp, String nonce) {
        String signData = data + timestamp + nonce + AES_KEY;
        return org.jeecg.common.util.Md5Util.md5Encode(signData, "UTF-8");
    }
    
    /**
     * 验证请求签名
     * @param data 数据
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param signature 签名
     * @return 是否验证通过
     */
    public static boolean verifySignature(String data, long timestamp, String nonce, String signature) {
        // 检查时间戳（防重放攻击）
        long currentTime = System.currentTimeMillis();
        if (Math.abs(currentTime - timestamp) > 5 * 60 * 1000) { // 5分钟有效期
            return false;
        }
        
        String expectedSignature = generateSignature(data, timestamp, nonce);
        return expectedSignature.equals(signature);
    }
    
    /**
     * 敏感信息脱敏
     * @param sensitive 敏感信息
     * @param type 类型：phone, email, idcard
     * @return 脱敏后的信息
     */
    public static String maskSensitiveInfo(String sensitive, String type) {
        if (StringUtils.isEmpty(sensitive)) {
            return sensitive;
        }
        
        switch (type.toLowerCase()) {
            case "phone":
                if (sensitive.length() >= 11) {
                    return sensitive.substring(0, 3) + "****" + sensitive.substring(7);
                }
                break;
            case "email":
                int atIndex = sensitive.indexOf("@");
                if (atIndex > 2) {
                    return sensitive.substring(0, 2) + "***" + sensitive.substring(atIndex);
                }
                break;
            case "idcard":
                if (sensitive.length() >= 18) {
                    return sensitive.substring(0, 6) + "********" + sensitive.substring(14);
                }
                break;
            case "password":
                return "******"; // 密码完全隐藏
        }
        
        return sensitive;
    }
    
    /**
     * 检查请求是否来自可信IP
     * @param clientIp 客户端IP
     * @return 是否可信
     */
    public static boolean isTrustedIp(String clientIp) {
        // 这里应该实现IP白名单检查
        // 示例实现：检查是否为内网IP或白名单IP
        
        if (StringUtils.isEmpty(clientIp)) {
            return false;
        }
        
        // 检查是否为内网IP
        if (clientIp.startsWith("192.168.") || 
            clientIp.startsWith("10.") || 
            clientIp.startsWith("172.")) {
            return true;
        }
        
        // 检查是否为本地IP
        if ("127.0.0.1".equals(clientIp) || "localhost".equals(clientIp)) {
            return true;
        }
        
        // 这里可以添加更多的IP白名单检查逻辑
        
        return true; // 默认信任（生产环境应该更严格）
    }
}
