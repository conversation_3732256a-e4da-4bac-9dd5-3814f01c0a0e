import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '订单ID',
    align:"center",
    dataIndex: 'orderId'
   },
   {
    title: '订单编号',
    align:"center",
    dataIndex: 'orderNo'
   },
   {
    title: '业务类型(1:普通订单,2:积分订单)',
    align:"center",
    dataIndex: 'bizType'
   },
   {
    title: '物流单号',
    align:"center",
    dataIndex: 'trackingNo'
   },
   {
    title: '物流公司名称',
    align:"center",
    dataIndex: 'expressCompany'
   },
   {
    title: '物流公司编码',
    align:"center",
    dataIndex: 'expressCode'
   },
   {
    title: '物流状态(1:已发货,2:运输中,3:已签收,4:异常)',
    align:"center",
    dataIndex: 'logisticsStatus'
   },
   {
    title: '最新物流信息',
    align:"center",
    dataIndex: 'latestInfo'
   },
   {
    title: '发货时间',
    align:"center",
    dataIndex: 'shipTime'
   },
   {
    title: '完成时间',
    align:"center",
    dataIndex: 'finishTime'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '订单ID',
    field: 'orderId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入订单ID!'},
          ];
     },
  },
  {
    label: '订单编号',
    field: 'orderNo',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入订单编号!'},
          ];
     },
  },
  {
    label: '业务类型(1:普通订单,2:积分订单)',
    field: 'bizType',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入业务类型(1:普通订单,2:积分订单)!'},
          ];
     },
  },
  {
    label: '物流单号',
    field: 'trackingNo',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入物流单号!'},
          ];
     },
  },
  {
    label: '物流公司名称',
    field: 'expressCompany',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入物流公司名称!'},
          ];
     },
  },
  {
    label: '物流公司编码',
    field: 'expressCode',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入物流公司编码!'},
          ];
     },
  },
  {
    label: '物流状态(1:已发货,2:运输中,3:已签收,4:异常)',
    field: 'logisticsStatus',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入物流状态(1:已发货,2:运输中,3:已签收,4:异常)!'},
          ];
     },
  },
  {
    label: '最新物流信息',
    field: 'latestInfo',
    component: 'Input',
  },
  {
    label: '发货时间',
    field: 'shipTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入发货时间!'},
          ];
     },
  },
  {
    label: '完成时间',
    field: 'finishTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  orderId: {title: '订单ID',order: 0,view: 'text', type: 'string',},
  orderNo: {title: '订单编号',order: 1,view: 'text', type: 'string',},
  bizType: {title: '业务类型(1:普通订单,2:积分订单)',order: 2,view: 'number', type: 'number',},
  trackingNo: {title: '物流单号',order: 3,view: 'text', type: 'string',},
  expressCompany: {title: '物流公司名称',order: 4,view: 'text', type: 'string',},
  expressCode: {title: '物流公司编码',order: 5,view: 'text', type: 'string',},
  logisticsStatus: {title: '物流状态(1:已发货,2:运输中,3:已签收,4:异常)',order: 6,view: 'number', type: 'number',},
  latestInfo: {title: '最新物流信息',order: 7,view: 'text', type: 'string',},
  shipTime: {title: '发货时间',order: 8,view: 'datetime', type: 'string',},
  finishTime: {title: '完成时间',order: 9,view: 'datetime', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}