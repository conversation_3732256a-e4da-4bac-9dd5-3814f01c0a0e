package org.jeecg.modules.system.util;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 短信验证码安全工具类
 * @Author: jeecg-boot
 * @Date: 2025-01-24
 * @Version: V1.0
 */
@Component
@Slf4j
public class SmsSecurityUtil {

    @Autowired
    private RedisUtil redisUtil;

    // 验证码发送频率限制（秒）
    private static final int SEND_INTERVAL = 60;
    
    // 验证码有效期（秒）
    private static final int CODE_EXPIRE_TIME = 300; // 5分钟
    
    // 每日发送次数限制
    private static final int DAILY_SEND_LIMIT = 10;
    
    // 验证失败次数限制
    private static final int MAX_VERIFY_ATTEMPTS = 5;

    /**
     * 发送验证码前的安全检查
     * @param phone 手机号
     * @return 检查结果，null表示通过
     */
    public String checkSendSecurity(String phone) {
        String phoneKey = "CardVerse:sms_security:" + phone;
        
        // 1. 检查发送频率
        String lastSendKey = phoneKey + ":last_send";
        if (redisUtil.hasKey(lastSendKey)) {
            long lastSendTime = (Long) redisUtil.get(lastSendKey);
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastSendTime < SEND_INTERVAL * 1000) {
                long remainingTime = SEND_INTERVAL - (currentTime - lastSendTime) / 1000;
                return "发送过于频繁，请" + remainingTime + "秒后再试";
            }
        }
        
        // 2. 检查每日发送次数
        String dailyCountKey = phoneKey + ":daily_count:" + getCurrentDateStr();
        Object dailyCountObj = redisUtil.get(dailyCountKey);
        int dailyCount = dailyCountObj != null ? (Integer) dailyCountObj : 0;
        if (dailyCount >= DAILY_SEND_LIMIT) {
            return "今日发送次数已达上限，请明天再试";
        }
        
        return null; // 检查通过
    }
    
    /**
     * 记录验证码发送
     * @param phone 手机号
     * @param code 验证码
     */
    public void recordSendCode(String phone, String code) {
        String phoneKey = "CardVerse:sms_security:" + phone;
        long currentTime = System.currentTimeMillis();
        
        // 1. 记录最后发送时间
        String lastSendKey = phoneKey + ":last_send";
        redisUtil.set(lastSendKey, currentTime, SEND_INTERVAL);
        
        // 2. 增加每日发送次数
        String dailyCountKey = phoneKey + ":daily_count:" + getCurrentDateStr();
        redisUtil.incr(dailyCountKey, 1);
        redisUtil.expire(dailyCountKey, 24 * 60 * 60); // 24小时过期
        
        // 3. 存储验证码（带使用状态）
        String codeKey = "CardVerse:phone_code:" + phone + ":code";
        SmsCodeInfo codeInfo = new SmsCodeInfo();
        codeInfo.setCode(code);
        codeInfo.setCreateTime(currentTime);
        codeInfo.setUsed(false);
        codeInfo.setVerifyAttempts(0);
        
        redisUtil.set(codeKey, codeInfo, CODE_EXPIRE_TIME);
        
        log.info("验证码发送记录：手机号={}, 验证码={}, 时间={}", phone, code, currentTime);
    }
    
    /**
     * 验证验证码（防重放）
     * @param phone 手机号
     * @param inputCode 输入的验证码
     * @return 验证结果，null表示验证通过
     */
    public String verifyCodeSecure(String phone, String inputCode) {
        String codeKey = "CardVerse:phone_code:" + phone + ":code";
        
        // 1. 检查验证码是否存在
        if (!redisUtil.hasKey(codeKey)) {
            return "验证码已过期或不存在";
        }
        
        // 2. 获取验证码信息
        Object codeInfoObj = redisUtil.get(codeKey);
        if (!(codeInfoObj instanceof SmsCodeInfo)) {
            // 兼容旧版本，如果是字符串类型
            if (codeInfoObj instanceof String) {
                String storedCode = (String) codeInfoObj;
                if (!storedCode.equals(inputCode)) {
                    return "验证码错误";
                }
                // 验证成功后立即删除
                redisUtil.del(codeKey);
                return null;
            }
            return "验证码格式错误";
        }
        
        SmsCodeInfo codeInfo = (SmsCodeInfo) codeInfoObj;
        
        // 3. 检查是否已使用
        if (codeInfo.isUsed()) {
            return "验证码已使用，请重新获取";
        }
        
        // 4. 检查验证失败次数
        if (codeInfo.getVerifyAttempts() >= MAX_VERIFY_ATTEMPTS) {
            redisUtil.del(codeKey); // 删除验证码
            return "验证失败次数过多，请重新获取验证码";
        }
        
        // 5. 验证验证码
        if (!codeInfo.getCode().equals(inputCode)) {
            // 增加验证失败次数
            codeInfo.setVerifyAttempts(codeInfo.getVerifyAttempts() + 1);
            redisUtil.set(codeKey, codeInfo, CODE_EXPIRE_TIME);
            
            int remainingAttempts = MAX_VERIFY_ATTEMPTS - codeInfo.getVerifyAttempts();
            return "验证码错误，还可尝试" + remainingAttempts + "次";
        }
        
        // 6. 验证成功，标记为已使用并立即删除
        codeInfo.setUsed(true);
        redisUtil.del(codeKey); // 立即删除，防止重放
        
        log.info("验证码验证成功：手机号={}, 验证码={}", phone, inputCode);
        return null; // 验证通过
    }
    
    /**
     * 清理验证码（强制删除）
     * @param phone 手机号
     */
    public void clearCode(String phone) {
        String codeKey = "CardVerse:phone_code:" + phone + ":code";
        redisUtil.del(codeKey);
        log.info("验证码已清理：手机号={}", phone);
    }
    
    /**
     * 检查IP发送频率
     * @param ip IP地址
     * @return 检查结果，null表示通过
     */
    public String checkIpSendFrequency(String ip) {
        String ipKey = "CardVerse:sms_ip:" + ip + ":count:" + getCurrentHourStr();
        Object countObj = redisUtil.get(ipKey);
        int count = countObj != null ? (Integer) countObj : 0;
        
        // 每小时每IP最多发送20条
        if (count >= 20) {
            return "该IP发送过于频繁，请稍后再试";
        }
        
        return null;
    }
    
    /**
     * 记录IP发送次数
     * @param ip IP地址
     */
    public void recordIpSend(String ip) {
        String ipKey = "CardVerse:sms_ip:" + ip + ":count:" + getCurrentHourStr();
        redisUtil.incr(ipKey, 1);
        redisUtil.expire(ipKey, 60 * 60); // 1小时过期
    }
    
    /**
     * 获取当前日期字符串
     */
    private String getCurrentDateStr() {
        return String.valueOf(System.currentTimeMillis() / (24 * 60 * 60 * 1000));
    }
    
    /**
     * 获取当前小时字符串
     */
    private String getCurrentHourStr() {
        return String.valueOf(System.currentTimeMillis() / (60 * 60 * 1000));
    }
    
    /**
     * 短信验证码信息类
     */
    public static class SmsCodeInfo {
        private String code;
        private long createTime;
        private boolean used;
        private int verifyAttempts;
        
        // Getters and Setters
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        
        public long getCreateTime() { return createTime; }
        public void setCreateTime(long createTime) { this.createTime = createTime; }
        
        public boolean isUsed() { return used; }
        public void setUsed(boolean used) { this.used = used; }
        
        public int getVerifyAttempts() { return verifyAttempts; }
        public void setVerifyAttempts(int verifyAttempts) { this.verifyAttempts = verifyAttempts; }
    }
}
