package org.jeecg.modules.inz_product_settlement.service;

import org.jeecg.modules.inz_product_settlement.entity.InzSettlementBatch;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 结算批次
 * @Author: jeecg-boot
 * @Date: 2025-06-26
 * @Version: V1.0
 */
public interface IInzSettlementBatchService extends IService<InzSettlementBatch> {

    /**
     * 创建结算批次
     * @param batchName 批次名称
     * @param storeIds 店铺ID列表（可选）
     * @param startTime 结算开始时间
     * @param endTime 结算结束时间
     * @param remark 备注
     * @return 批次ID
     */
    String createSettlementBatch(String batchName, List<String> storeIds, Date startTime, Date endTime, String remark);

    /**
     * 执行批次结算
     * @param batchId 批次ID
     * @return 是否执行成功
     */
    boolean executeBatchSettlement(String batchId);

    /**
     * 取消结算批次
     * @param batchId 批次ID
     * @param reason 取消原因
     * @return 是否取消成功
     */
    boolean cancelSettlementBatch(String batchId, String reason);

    /**
     * 获取批次详情
     * @param batchId 批次ID
     * @return 批次详情
     */
    Map<String, Object> getBatchDetail(String batchId);

    /**
     * 获取批次统计信息
     * @param batchId 批次ID
     * @return 统计信息
     */
    Map<String, Object> getBatchStatistics(String batchId);

    /**
     * 更新批次统计信息
     * @param batchId 批次ID
     * @return 是否更新成功
     */
    boolean updateBatchStatistics(String batchId);
}
