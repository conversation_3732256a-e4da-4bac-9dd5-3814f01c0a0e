package org.jeecg.modules.inz_product_activity.service;

import org.jeecg.modules.api.product.entity.ProductActivity;
import org.jeecg.modules.inz_product_activity.entity.InzProductActivity;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description: 商品活动表
 * @Author: jeecg-boot
 * @Date:   2025-06-21
 * @Version: V1.0
 */
public interface IInzProductActivityService extends IService<InzProductActivity> {
    /**
     * 创建商品活动
     *
     * @param inzProductActivity 活动信息
     * @return 是否成功
     */
    boolean createActivity(InzProductActivity inzProductActivity);
}
