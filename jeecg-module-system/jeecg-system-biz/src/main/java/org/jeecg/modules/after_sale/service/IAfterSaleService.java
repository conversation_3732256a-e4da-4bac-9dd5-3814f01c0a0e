package org.jeecg.modules.after_sale.service;

import org.jeecg.modules.after_sale.entity.AfterSale;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 售后表
 * @Author: jeecg-boot
 * @Date:   2025-06-20
 * @Version: V1.0
 */
public interface IAfterSaleService extends IService<AfterSale> {

    /**
     * 通过用户名称查询售后订单
     * @param username 用户名称
     * @return 售后订单列表
     */
    List<AfterSale> getAfterSalesByUsername(String username);

    /**
     * 通过店铺名称查询售后订单
     * @param storeName 店铺名称
     * @return 售后订单列表
     */
    List<AfterSale> getAfterSalesByStoreName(String storeName);

    /**
     * 通过店铺ID查询售后订单
     * @param storeId 店铺ID
     * @return 售后订单列表
     */
    List<AfterSale> getAfterSalesByStoreId(String storeId);
    
    /**
     * 根据订单编号获取售后申请
     * @param orderNo 订单编号
     * @return 售后申请
     */
    AfterSale getAfterSaleByOrderNo(String orderNo);
}
