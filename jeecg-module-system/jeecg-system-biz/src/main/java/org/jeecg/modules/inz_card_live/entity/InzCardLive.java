package org.jeecg.modules.inz_card_live.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 直播表
 * @Author: jeecg-boot
 * @Date:   2025-06-20
 * @Version: V1.0
 */
@Data
@TableName("card_live")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="card_live对象", description="直播表")
public class InzCardLive implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**卡片ID*/
	@Excel(name = "卡片ID", width = 15)
    @ApiModelProperty(value = "卡片ID")
    private java.lang.String cardId;
	/**店铺ID*/
	@Excel(name = "店铺ID", width = 15)
    @ApiModelProperty(value = "店铺ID")
    private java.lang.String storeId;
	/**直播标题*/
    @Excel(name = "直播标题", width = 30)
    @ApiModelProperty(value = "直播标题")
    private java.lang.String title;
	/**直播状态(0:待直播 1:直播中 2:已结束)*/
	@Excel(name = "直播状态(0:待直播 1:直播中 2:已结束)", width = 15)
    @ApiModelProperty(value = "直播状态(0:待直播 1:直播中 2:已结束)")
    private java.lang.Integer status;
	/**直播开始时间*/
	@Excel(name = "直播开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "直播开始时间")
    private java.util.Date startTime;
	/**直播结束时间*/
	@Excel(name = "直播结束时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "直播结束时间")
    private java.util.Date endTime;
	/**直播回放地址*/
	@Excel(name = "直播回放地址", width = 15)
    @ApiModelProperty(value = "直播回放地址")
    private java.lang.String replayUrl;
	/**观看人数*/
	@Excel(name = "观看人数", width = 15)
    @ApiModelProperty(value = "观看人数")
    private java.lang.Integer viewCount;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**删除标记*/
	@Excel(name = "删除标记", width = 15)
    @ApiModelProperty(value = "删除标记")
    @TableLogic
    private java.lang.Integer delFlag;
}
