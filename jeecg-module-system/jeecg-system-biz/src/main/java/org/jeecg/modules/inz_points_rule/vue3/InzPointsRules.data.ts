import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '规则编码',
    align:"center",
    dataIndex: 'ruleCode'
   },
   {
    title: '规则名称',
    align:"center",
    dataIndex: 'ruleName'
   },
   {
    title: '规则描述',
    align:"center",
    dataIndex: 'ruleDesc'
   },
   {
    title: '积分值',
    align:"center",
    dataIndex: 'points'
   },
   {
    title: '规则类型(1:获取规则,2:使用规则)',
    align:"center",
    dataIndex: 'ruleType'
   },
   {
    title: '任务类型(1:签到,2:分享,3:评价,4:关注,5:购物)',
    align:"center",
    dataIndex: 'taskType'
   },
   {
    title: '每日限制次数(0表示不限制)',
    align:"center",
    dataIndex: 'dailyLimit'
   },
   {
    title: '状态(0:禁用,1:启用)',
    align:"center",
    dataIndex: 'status'
   },
   {
    title: '排序',
    align:"center",
    dataIndex: 'sort'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '规则编码',
    field: 'ruleCode',
    component: 'Input',
  },
  {
    label: '规则名称',
    field: 'ruleName',
    component: 'Input',
  },
  {
    label: '规则描述',
    field: 'ruleDesc',
    component: 'Input',
  },
  {
    label: '积分值',
    field: 'points',
    component: 'InputNumber',
  },
  {
    label: '规则类型(1:获取规则,2:使用规则)',
    field: 'ruleType',
    component: 'InputNumber',
  },
  {
    label: '任务类型(1:签到,2:分享,3:评价,4:关注,5:购物)',
    field: 'taskType',
    component: 'InputNumber',
  },
  {
    label: '每日限制次数(0表示不限制)',
    field: 'dailyLimit',
    component: 'InputNumber',
  },
  {
    label: '状态(0:禁用,1:启用)',
    field: 'status',
    component: 'InputNumber',
  },
  {
    label: '排序',
    field: 'sort',
    component: 'InputNumber',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  ruleCode: {title: '规则编码',order: 0,view: 'text', type: 'string',},
  ruleName: {title: '规则名称',order: 1,view: 'text', type: 'string',},
  ruleDesc: {title: '规则描述',order: 2,view: 'text', type: 'string',},
  points: {title: '积分值',order: 3,view: 'number', type: 'number',},
  ruleType: {title: '规则类型(1:获取规则,2:使用规则)',order: 4,view: 'number', type: 'number',},
  taskType: {title: '任务类型(1:签到,2:分享,3:评价,4:关注,5:购物)',order: 5,view: 'number', type: 'number',},
  dailyLimit: {title: '每日限制次数(0表示不限制)',order: 6,view: 'number', type: 'number',},
  status: {title: '状态(0:禁用,1:启用)',order: 7,view: 'number', type: 'number',},
  sort: {title: '排序',order: 8,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}