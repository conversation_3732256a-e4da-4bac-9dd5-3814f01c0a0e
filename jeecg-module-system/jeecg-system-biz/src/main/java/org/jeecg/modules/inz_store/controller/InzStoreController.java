package org.jeecg.modules.inz_store.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.aspect.annotation.PermissionData;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.inz_product.entity.InzProduct;
import org.jeecg.modules.inz_product.service.IInzProductService;
import org.jeecg.modules.inz_store.entity.InzStore;
import org.jeecg.modules.inz_store.service.IInzStoreService;
import org.jeecg.modules.inz_users_fronts.service.IInzUsersFrontsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.Arrays;

/**
 * @Description: 店铺表
 * @Author: jeecg-boot
 * @Date: 2025-06-15
 * @Version: V1.0
 */
/*@Api(tags="店铺表")*/
@RestController
@RequestMapping("/inz_store/inzStore")
@Slf4j
@Validated
public class InzStoreController extends JeecgController<InzStore, IInzStoreService> {
    @Autowired
    private IInzStoreService inzStoreService;

    @Autowired
    private IInzProductService inzProductService;

    @Autowired
    private IInzUsersFrontsService inzUsersFrontsService;

    /**
     * 分页列表查询
     *
     * @param inzStore
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "店铺表-分页列表查询")
    @ApiOperation(value = "店铺表-分页列表查询", notes = "店铺表-分页列表查询")
    @GetMapping(value = "/list")
    @PermissionData(pageComponent = "inz_store/InzStoreList")
    public Result<IPage<InzStore>> queryPageList(InzStore inzStore,
                                                 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                 @RequestParam(name = "storeName", required = false) String storeName,
                                                 @RequestParam(name = "userName", required = false) String username,
                                                 @RequestParam(name = "userId", required = false) String userId,
                                                 HttpServletRequest req) {
        // 创建分页对象
        Page<InzStore> page = new Page<InzStore>(pageNo, pageSize);
        IPage<InzStore> pageList;

        // 创建查询条件
        QueryWrapper<InzStore> queryWrapper = QueryGenerator.initQueryWrapper(inzStore, req.getParameterMap());

        // 处理店铺名称筛选
        if (StringUtils.isNotBlank(storeName)) {
            queryWrapper.like("name", storeName);
        }

        // 处理用户名称筛选
        if (StringUtils.isNotBlank(username)) {
            // 通过用户名称获取用户ID
            String userIdFromName = inzUsersFrontsService.getUserIdByUsername(username);
            if (StringUtils.isNotBlank(userIdFromName)) {
                queryWrapper.eq("user_id", userIdFromName);
                log.info("根据用户名[{}]查询到用户ID[{}]过滤店铺列表", username, userIdFromName);
            } else {
                // 如果找不到用户，返回空结果
                log.warn("找不到用户名为[{}]的用户，返回空结果", username);
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }
        }

        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null && StringUtils.isBlank(userId) && StringUtils.isBlank(username)) {
            // 判断是否为管理员角色
            if (!isAdmin(sysUser)) {
                log.info("非管理员用户访问店铺列表，进行数据权限过滤");

                // 获取用户名，用于查询前端用户
                String username1 = sysUser.getUsername();

                // 1. 先尝试通过sys_user的用户名查找对应的user_front记录
                String currentUserId = null;
                try {
                    currentUserId = inzUsersFrontsService.getUserIdByUsername(username1);
                    log.info("通过用户名[{}]查询到前端用户ID: {}", username1, currentUserId);
                } catch (Exception e) {
                    log.warn("通过用户名查询前端用户失败: {}", e.getMessage());
                }

                if (StringUtils.isNotBlank(currentUserId)) {
                    queryWrapper.eq("user_id", currentUserId);
                    log.info("对用户[{}]应用数据权限过滤", currentUserId);
                } else {
                    log.warn("未找到后台用户[{}]对应的前端用户，返回空结果", username1);
                    return Result.OK(new Page<>(pageNo, pageSize, 0));
                }
            } else {
                log.info("管理员用户访问店铺列表，不进行数据权限过滤");
                // 管理员用户不添加用户ID过滤条件，可以查看所有店铺
            }
        }

        // 执行查询
        pageList = inzStoreService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 判断用户是否为管理员
     *
     * @param user 登录用户
     * @return 是否为管理员
     */
    private boolean isAdmin(LoginUser user) {
        if (user == null) {
            return false;
        }

        log.info("判断用户是否为管理员 - 用户名: {}, 角色编码: {}", user.getUsername(), user.getRoleCode());

        // 检查用户名是否为admin
        if ("admin".equalsIgnoreCase(user.getUsername())) {
            log.info("用户{}是admin用户，判定为管理员", user.getUsername());
            return true;
        }

        // 根据角色编码判断
        String roleCode = user.getRoleCode();
        if (StringUtils.isNotBlank(roleCode)) {
            // 转换为小写进行比较，提高准确性
            String lowerRoleCode = roleCode.toLowerCase();
            boolean isAdminRole = lowerRoleCode.contains("admin") || lowerRoleCode.contains("administrator");
            log.info("用户{}角色编码检查结果: {}", user.getUsername(), isAdminRole);
            return isAdminRole;
        }

        log.info("用户{}没有角色编码，判定为非管理员", user.getUsername());
        return false;
    }

    /**
     * 添加
     *
     * @param inzStore
     * @return
     */
    @AutoLog(value = "店铺表-添加")
    @ApiOperation(value = "店铺表-添加", notes = "店铺表-添加")
    @RequiresPermissions("inz_store:inz_store:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InzStore inzStore) {
        inzStoreService.save(inzStore);
        return Result.OK("添加成功！");
    }

    /**
     * 管理员获取所有店铺列表（无权限过滤）
     *
     * @param inzStore
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "店铺表-管理员查看所有店铺")
    @ApiOperation(value = "店铺表-管理员查看所有店铺", notes = "管理员专用接口，可查看所有店铺")
    @GetMapping(value = "/adminList")
    @RequiresPermissions("inz_store:inz_store:list")
    public Result<IPage<InzStore>> adminQueryPageList(InzStore inzStore,
                                                      @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                      @RequestParam(name = "storeName", required = false) String storeName,
                                                      @RequestParam(name = "userName", required = false) String username,
                                                      @RequestParam(name = "userId", required = false) String userId,
                                                      HttpServletRequest req) {
        // 创建分页对象
        Page<InzStore> page = new Page<>(pageNo, pageSize);
        IPage<InzStore> pageList;

        // 创建查询条件
        QueryWrapper<InzStore> queryWrapper = QueryGenerator.initQueryWrapper(inzStore, req.getParameterMap());

        // 处理店铺名称筛选
        if (StringUtils.isNotBlank(storeName)) {
            queryWrapper.like("name", storeName);
        }

        // 处理用户名筛选
        if (StringUtils.isNotBlank(username)) {
            // 通过用户名查询前端用户ID
            try {
                String frontUserId = inzUsersFrontsService.getUserIdByUsername(username);
                if (StringUtils.isNotBlank(frontUserId)) {
                    queryWrapper.eq("user_id", frontUserId);
                } else {
                    // 如果找不到用户，返回空结果
                    return Result.OK(new Page<>(pageNo, pageSize, 0));
                }
            } catch (Exception e) {
                log.warn("通过用户名查询前端用户失败: {}", e.getMessage());
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }
        }

        // 处理用户ID筛选
        if (StringUtils.isNotBlank(userId)) {
            queryWrapper.eq("user_id", userId);
        }

        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        log.info("管理员{}访问所有店铺列表，不进行数据权限过滤", sysUser != null ? sysUser.getUsername() : "unknown");

        // 管理员接口：不进行任何数据权限过滤，可以查看所有店铺
        // 执行查询
        pageList = inzStoreService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 编辑
     *
     * @param inzStore
     * @return
     */
    @AutoLog(value = "店铺表-编辑")
    @ApiOperation(value = "店铺表-编辑", notes = "店铺表-编辑")
    @RequiresPermissions("inz_store:inz_store:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InzStore inzStore) {
        inzStoreService.updateById(inzStore);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "店铺表-通过id删除")
    @ApiOperation(value = "店铺表-通过id删除", notes = "店铺表-通过id删除")
    @RequiresPermissions("inz_store:inz_store:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        inzStoreService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "店铺表-批量删除")
    @ApiOperation(value = "店铺表-批量删除", notes = "店铺表-批量删除")
    @RequiresPermissions("inz_store:inz_store:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzStoreService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @return 店铺信息
     */
    @AutoLog(value = "店铺表-通过id查询")
    @ApiOperation(value = "店铺表-通过id查询", notes = "店铺表-通过id查询")
    @GetMapping(value = {"/queryById", "/queryById/{id}"})
    public Result<InzStore> queryById(@RequestParam(name = "id", required = false) String requestId, 
                                      @PathVariable(name = "id", required = false) String pathId) {
        // 优先使用路径变量，如果为空则使用请求参数
        String id = pathId != null ? pathId : requestId;
        
        if (id == null) {
            return Result.error("店铺ID不能为空");
        }
        
        InzStore inzStore = inzStoreService.getById(id);
        if (inzStore == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzStore);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param inzStore
     */
    @RequiresPermissions("inz_store:inz_store:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzStore inzStore) {
        return super.exportXls(request, inzStore, InzStore.class, "店铺表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("inz_store:inz_store:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzStore.class);
    }

    /**
     * 获取店铺中的热销商品（销量占库存的70%以上）
     *
     * @param storeId  店铺ID
     * @param pageNo   页码
     * @param pageSize 每页记录数
     * @return 热销商品分页结果
     */
    @ApiOperation(value = "获取店铺热销商品", notes = "获取店铺中销量占库存70%以上的热销商品")
    @GetMapping(value = "/hotSellingProducts")
    public Result<IPage<InzProduct>> getHotSellingProducts(
            @RequestParam(name = "storeId") String storeId,
            @RequestParam(name = "pageNo", defaultValue = "1") @Min(value = 1, message = "页码不能小于1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") @Min(value = 1, message = "每页记录数不能小于1") @Max(value = 100, message = "每页记录数不能超过100") Integer pageSize) {
        try {
            if (StringUtils.isBlank(storeId)) {
                return Result.error("店铺ID不能为空");
            }

            // 检查店铺是否存在
            InzStore store = inzStoreService.getById(storeId);
            if (store == null) {
                return Result.error("店铺不存在，ID: " + storeId);
            }

            Page<InzProduct> page = new Page<>(pageNo, pageSize);
            IPage<InzProduct> pageList = inzProductService.getHotSellingProducts(page, storeId);

            return Result.OK(pageList);
        } catch (Exception e) {
            log.error("获取热销商品失败", e);
            return Result.error("获取热销商品失败: " + e.getMessage());
        }
    }

    /**
     * 通过店铺名称获取热销商品（销量占库存的70%以上）
     *
     * @param storeName 店铺名称
     * @param pageNo    页码
     * @param pageSize  每页记录数
     * @return 热销商品分页结果
     */
    @ApiOperation(value = "通过店铺名称获取热销商品", notes = "通过店铺名称获取销量占库存70%以上的热销商品")
    @GetMapping(value = "/hotSellingProductsByName")
    public Result<IPage<InzProduct>> getHotSellingProductsByName(
            @RequestParam(name = "storeName") String storeName,
            @RequestParam(name = "pageNo", defaultValue = "1") @Min(value = 1, message = "页码不能小于1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") @Min(value = 1, message = "每页记录数不能小于1") @Max(value = 100, message = "每页记录数不能超过100") Integer pageSize) {
        try {
            if (StringUtils.isBlank(storeName)) {
                return Result.error("店铺名称不能为空");
            }

            // 通过店铺名称获取店铺ID
            String storeId = inzStoreService.getStoreIdByName(storeName);
            if (StringUtils.isBlank(storeId)) {
                return Result.error("未找到名为 '" + storeName + "' 的店铺");
            }

            Page<InzProduct> page = new Page<>(pageNo, pageSize);
            IPage<InzProduct> pageList = inzProductService.getHotSellingProducts(page, storeId);

            return Result.OK(pageList);
        } catch (Exception e) {
            log.error("获取热销商品失败", e);
            return Result.error("获取热销商品失败: " + e.getMessage());
        }
    }

    /**
     * 提交店铺审核 (集成auditRecord模块)
     *
     * @param id 店铺ID
     * @return
     */
    @AutoLog(value = "店铺管理-提交审核")
    @ApiOperation(value = "提交店铺审核", notes = "提交店铺审核到统一审核系统")
    @RequiresPermissions("inz_store:submit_audit")
    @PostMapping(value = "/submitAudit/{id}")
    public Result<String> submitStoreAudit(@PathVariable String id) {
        try {
            // 调用统一审核服务，而不是重复开发
            boolean success = inzStoreService.submitStoreForAudit(id);
            if (success) {
                return Result.OK("店铺审核提交成功！请到审核管理模块查看审核进度。");
            } else {
                return Result.error("提交审核失败！");
            }
        } catch (Exception e) {
            log.error("提交店铺审核失败", e);
            return Result.error("提交审核失败: " + e.getMessage());
        }
    }
}
