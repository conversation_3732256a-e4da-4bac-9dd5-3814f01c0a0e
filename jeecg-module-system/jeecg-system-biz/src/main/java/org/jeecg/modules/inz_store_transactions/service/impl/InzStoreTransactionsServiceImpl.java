package org.jeecg.modules.inz_store_transactions.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.inz_store_transactions.entity.InzStoreTransactions;
import org.jeecg.modules.inz_store_transactions.mapper.InzStoreTransactionsMapper;
import org.jeecg.modules.inz_store_transactions.service.IInzStoreTransactionsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Calendar;

/**
 * @Description: inz_store_transaction
 * @Author: jeecg-boot
 * @Date:   2025-06-26
 * @Version: V1.0
 */
@Service
@Slf4j
public class InzStoreTransactionsServiceImpl extends ServiceImpl<InzStoreTransactionsMapper, InzStoreTransactions> implements IInzStoreTransactionsService {

    @Override
    public Map<String, Object> getTransactionSummary() {
        Map<String, Object> summary = new HashMap<>();
        
        try {
            // 获取总交易数量
            long totalTransactions = this.count();
            summary.put("totalTransactions", totalTransactions);
            
            // 获取总交易金额 (只计算成功的交易)
            QueryWrapper<InzStoreTransactions> amountWrapper = new QueryWrapper<>();
            amountWrapper.eq("status", "SUCCESS");
            List<InzStoreTransactions> transactions = this.list(amountWrapper);
            
            BigDecimal totalAmount = transactions.stream()
                .map(InzStoreTransactions::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            summary.put("totalAmount", totalAmount);
            
            // 获取合作门店数量 (通过去重storeId)
            Set<String> uniqueStores = transactions.stream()
                .map(InzStoreTransactions::getStoreId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
            summary.put("storeCount", uniqueStores.size());
            
            // 计算月增长率
            BigDecimal growthRate = calculateMonthlyGrowthRate();
            summary.put("growthRate", growthRate);
            
            log.info("获取交易概览数据成功: {}", summary);
        } catch (Exception e) {
            log.error("获取交易概览数据失败", e);
            // 设置默认值
            summary.put("totalTransactions", 0);
            summary.put("totalAmount", BigDecimal.ZERO);
            summary.put("storeCount", 0);
            summary.put("growthRate", BigDecimal.ZERO);
        }
        
        return summary;
    }
    
    /**
     * 计算月增长率
     * @return 月增长率百分比
     */
    private BigDecimal calculateMonthlyGrowthRate() {
        try {
            // 获取当前月和上个月的时间范围
            Calendar calendar = Calendar.getInstance();
            
            // 当前月开始时间
            Calendar currentMonthStart = Calendar.getInstance();
            currentMonthStart.set(Calendar.DAY_OF_MONTH, 1);
            currentMonthStart.set(Calendar.HOUR_OF_DAY, 0);
            currentMonthStart.set(Calendar.MINUTE, 0);
            currentMonthStart.set(Calendar.SECOND, 0);
            
            // 当前月结束时间（当前时间）
            Calendar currentMonthEnd = Calendar.getInstance();
            
            // 上个月开始时间
            Calendar lastMonthStart = Calendar.getInstance();
            lastMonthStart.add(Calendar.MONTH, -1);
            lastMonthStart.set(Calendar.DAY_OF_MONTH, 1);
            lastMonthStart.set(Calendar.HOUR_OF_DAY, 0);
            lastMonthStart.set(Calendar.MINUTE, 0);
            lastMonthStart.set(Calendar.SECOND, 0);
            
            // 上个月结束时间
            Calendar lastMonthEnd = Calendar.getInstance();
            lastMonthEnd.set(Calendar.DAY_OF_MONTH, 1);
            lastMonthEnd.add(Calendar.SECOND, -1);
            
            // 查询当前月交易总额
            QueryWrapper<InzStoreTransactions> currentMonthWrapper = new QueryWrapper<>();
            currentMonthWrapper.eq("status", "SUCCESS")
                              .ge("transaction_time", currentMonthStart.getTime())
                              .le("transaction_time", currentMonthEnd.getTime());
            
            List<InzStoreTransactions> currentMonthTransactions = this.list(currentMonthWrapper);
            BigDecimal currentMonthAmount = currentMonthTransactions.stream()
                .map(InzStoreTransactions::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 查询上个月交易总额
            QueryWrapper<InzStoreTransactions> lastMonthWrapper = new QueryWrapper<>();
            lastMonthWrapper.eq("status", "SUCCESS")
                           .ge("transaction_time", lastMonthStart.getTime())
                           .le("transaction_time", lastMonthEnd.getTime());
            
            List<InzStoreTransactions> lastMonthTransactions = this.list(lastMonthWrapper);
            BigDecimal lastMonthAmount = lastMonthTransactions.stream()
                .map(InzStoreTransactions::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 计算增长率
            if (lastMonthAmount.compareTo(BigDecimal.ZERO) == 0) {
                // 如果上个月没有交易，则增长率为100%（或者根据业务需求设置其他值）
                return new BigDecimal(100);
            }
            
            BigDecimal growthRate = currentMonthAmount.subtract(lastMonthAmount)
                .divide(lastMonthAmount, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(100))
                .setScale(2, RoundingMode.HALF_UP);
            
            return growthRate;
        } catch (Exception e) {
            log.error("计算月增长率失败", e);
            return BigDecimal.ZERO;
        }
    }
}
