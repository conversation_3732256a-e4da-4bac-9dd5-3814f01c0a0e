package org.jeecg.modules.inz_product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_product.entity.InzProduct;
import org.jeecg.modules.inz_product.mapper.InzProductMapper;
import org.jeecg.modules.inz_product.service.IInzProductService;
import org.jeecg.modules.inz_store.service.IInzStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Date;
import java.util.HashMap;

/**
 * @Description: 商品表
 * @Author: jeecg-boot
 * @Date:   2025-06-15
 * @Version: V1.0
 */
@Service
public class InzProductServiceImpl extends ServiceImpl<InzProductMapper, InzProduct> implements IInzProductService {

    @Autowired
    private IInzStoreService inzStoreService;

    @Override
    public IPage<InzProduct> queryProductsByStoreId(Page<InzProduct> page,
                                                    InzProduct inzProduct,
                                                    String storeId,
                                                    Map<String, String[]> parameterMap) {
        // 构建查询条件
        QueryWrapper<InzProduct> queryWrapper = QueryGenerator.initQueryWrapper(inzProduct, parameterMap);

        if (StringUtils.isNotBlank(storeId)) {
            // 直接添加店铺ID过滤条件
            queryWrapper.eq("store_id", storeId);
        }

        // 执行分页查询
        return this.page(page, queryWrapper);
    }
    
    @Override
    public IPage<InzProduct> getHotSellingProducts(Page<InzProduct> page, String storeId) {
        if (StringUtils.isBlank(storeId)) {
            return new Page<>(page.getCurrent(), page.getSize());
        }
        
        // 构建查询条件：销量占库存的70%以上
        QueryWrapper<InzProduct> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("store_id", storeId);
        
        // 商品状态为上架(1)
        queryWrapper.eq("status", 1);
        
        // 确保库存和销量都有值
        queryWrapper.isNotNull("stock");
        queryWrapper.gt("stock", 0);
        queryWrapper.isNotNull("sales");
        
        // 使用SQL表达式计算销量占库存的百分比，并筛选大于70%的商品
        // sales / (sales + stock) > 0.7
        // 等价于 sales > 0.7 * (sales + stock)
        // 等价于 sales > 0.7 * sales + 0.7 * stock
        // 等价于 0.3 * sales > 0.7 * stock
        // 等价于 sales > (7/3) * stock
        queryWrapper.apply("sales > (7/3) * stock");
        
        // 按销量降序排序
        queryWrapper.orderByDesc("sales");
        
        // 执行分页查询
        return this.page(page, queryWrapper);
    }
    
    @Override
    public IPage<InzProduct> getHotSellingProductsByStoreName(Page<InzProduct> page, String storeName) {
        if (StringUtils.isBlank(storeName)) {
            return new Page<>(page.getCurrent(), page.getSize());
        }
        
        // 通过店铺名称获取店铺ID
        String storeId = inzStoreService.getStoreIdByName(storeName);
        
        if (StringUtils.isBlank(storeId)) {
            return new Page<>(page.getCurrent(), page.getSize());
        }
        
        // 调用通过店铺ID查询的方法
        return getHotSellingProducts(page, storeId);
    }
    
    @Override
    public List<InzProduct> getProductsByStoreName(String storeName) {
        if (StringUtils.isBlank(storeName)) {
            return new ArrayList<>();
        }
        
        // 通过店铺名称获取店铺ID
        String storeId = inzStoreService.getStoreIdByName(storeName);
        
        if (StringUtils.isBlank(storeId)) {
            return new ArrayList<>();
        }
        
        // 通过店铺ID查询商品
        LambdaQueryWrapper<InzProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InzProduct::getStoreId, storeId);
        queryWrapper.eq(InzProduct::getStatus, 1); // 只查询上架商品
        queryWrapper.orderByDesc(InzProduct::getCreateTime); // 按创建时间倒序排序
        
        return this.list(queryWrapper);
    }
    
    @Override
    public IPage<InzProduct> queryProductsByStoreName(Page<InzProduct> page,
                                                     InzProduct inzProduct,
                                                     String storeName,
                                                     Map<String, String[]> parameterMap) {
        if (StringUtils.isBlank(storeName)) {
            return new Page<>(page.getCurrent(), page.getSize());
        }
        
        // 通过店铺名称获取店铺ID
        String storeId = inzStoreService.getStoreIdByName(storeName);
        
        if (StringUtils.isBlank(storeId)) {
            return new Page<>(page.getCurrent(), page.getSize());
        }
        
        // 创建查询条件
        QueryWrapper<InzProduct> queryWrapper = QueryGenerator.initQueryWrapper(inzProduct, parameterMap);
        queryWrapper.eq("store_id", storeId);
        
        // 处理商品名称查询参数
        if (parameterMap != null && parameterMap.containsKey("name")) {
            String[] nameValues = parameterMap.get("name");
            if (nameValues != null && nameValues.length > 0 && StringUtils.isNotBlank(nameValues[0])) {
                queryWrapper.like("name", nameValues[0]);
            }
        }
        
        // 执行分页查询
        return this.page(page, queryWrapper);
    }
    
    @Override
    public void putOnSale(String id) {
        if (StringUtils.isNotBlank(id)) {
            InzProduct product = this.getById(id);
            if (product != null) {
                product.setStatus(InzProduct.STATUS_ONLINE);
                this.updateById(product);
            }
        }
    }
    
    @Override
    public void putOffSale(String id) {
        if (StringUtils.isNotBlank(id)) {
            InzProduct product = this.getById(id);
            if (product != null) {
                product.setStatus(InzProduct.STATUS_OFFLINE);
                this.updateById(product);
            }
        }
    }
    
    @Override
    public IPage<InzProduct> getFeaturedProducts(Page<InzProduct> page, String storeId) {
        if (StringUtils.isBlank(storeId)) {
            return new Page<>(page.getCurrent(), page.getSize());
        }
        
        // 构建查询条件：获取店铺特色商品
        QueryWrapper<InzProduct> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("store_id", storeId);
        queryWrapper.eq("is_featured", 1); // 特色商品标记为1
        queryWrapper.eq("status", InzProduct.STATUS_ONLINE); // 上架状态
        
        // 按排序值降序，创建时间降序
        queryWrapper.orderByDesc("sort");
        queryWrapper.orderByDesc("create_time");
        
        // 执行分页查询
        return this.page(page, queryWrapper);
    }
    
    @Override
    public Map<String, Object> getProductDetail(String id) {
        Map<String, Object> result = new HashMap<>();
        
        if (StringUtils.isBlank(id)) {
            return result;
        }
        
        // 获取商品基本信息
        InzProduct product = this.getById(id);
        if (product == null) {
            return result;
        }
        
        // 添加商品基本信息
        result.put("product", product);
        
        // 计算助力信息
        if (product.getCurrentSupport() != null && product.getTargetSupport() != null) {
            int remainingSupport = Math.max(0, product.getTargetSupport() - product.getCurrentSupport());
            int supportPercentage = 0;
            
            if (product.getTargetSupport() > 0) {
                supportPercentage = (int) (product.getCurrentSupport() * 100.0 / product.getTargetSupport());
            }
            
            Map<String, Object> supportInfo = new HashMap<>();
            supportInfo.put("current", product.getCurrentSupport());
            supportInfo.put("target", product.getTargetSupport());
            supportInfo.put("remaining", remainingSupport);
            supportInfo.put("percentage", supportPercentage);
            
            result.put("supportInfo", supportInfo);
        }
        
        // 计算助力倒计时
        if (product.getSupportEndTime() != null) {
            Date now = new Date();
            long remainingTime = product.getSupportEndTime().getTime() - now.getTime();
            
            if (remainingTime > 0) {
                long days = remainingTime / (24 * 60 * 60 * 1000);
                long hours = (remainingTime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000);
                long minutes = (remainingTime % (60 * 60 * 1000)) / (60 * 1000);
                long seconds = (remainingTime % (60 * 1000)) / 1000;
                
                Map<String, Object> countdown = new HashMap<>();
                countdown.put("days", days);
                countdown.put("hours", hours);
                countdown.put("minutes", minutes);
                countdown.put("seconds", seconds);
                countdown.put("totalSeconds", remainingTime / 1000);
                
                result.put("countdown", countdown);
            }
        }
        
        // 获取店铺信息
        if (StringUtils.isNotBlank(product.getStoreId())) {
            // 使用店铺服务获取店铺信息
            try {
                Map<String, Object> storeInfo = inzStoreService.getStoreInfo(product.getStoreId());
                result.put("store", storeInfo);
            } catch (Exception e) {
                // 如果获取店铺信息失败，不影响其他数据返回
                log.warn("获取店铺信息失败: " + e.getMessage());
            }
        }
        
        return result;
    }
}
