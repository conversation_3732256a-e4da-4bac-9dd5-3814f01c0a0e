-- 安全配置SQL文件
-- 用于配置系统安全相关的参数和规则

-- 创建安全配置表
CREATE TABLE IF NOT EXISTS `sys_security_config` (
    `id` varchar(32) NOT NULL COMMENT '主键',
    `config_key` varchar(100) NOT NULL COMMENT '配置键',
    `config_value` varchar(500) NOT NULL COMMENT '配置值',
    `config_desc` varchar(200) DEFAULT NULL COMMENT '配置描述',
    `config_type` varchar(20) DEFAULT 'STRING' COMMENT '配置类型：STRING, NUMBER, BOOLEAN, JSON',
    `is_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统安全配置表';

-- 插入安全配置数据
INSERT INTO `sys_security_config` (`id`, `config_key`, `config_value`, `config_desc`, `config_type`, `is_enabled`, `create_time`, `create_by`) VALUES
-- 密码安全配置
('sec_001', 'PASSWORD_MIN_LENGTH', '8', '密码最小长度', 'NUMBER', 1, NOW(), 'system'),
('sec_002', 'PASSWORD_MAX_LENGTH', '20', '密码最大长度', 'NUMBER', 1, NOW(), 'system'),
('sec_003', 'PASSWORD_REQUIRE_DIGIT', 'true', '密码必须包含数字', 'BOOLEAN', 1, NOW(), 'system'),
('sec_004', 'PASSWORD_REQUIRE_LOWERCASE', 'true', '密码必须包含小写字母', 'BOOLEAN', 1, NOW(), 'system'),
('sec_005', 'PASSWORD_REQUIRE_UPPERCASE', 'true', '密码必须包含大写字母', 'BOOLEAN', 1, NOW(), 'system'),
('sec_006', 'PASSWORD_REQUIRE_SPECIAL', 'true', '密码必须包含特殊字符', 'BOOLEAN', 1, NOW(), 'system'),
('sec_007', 'PASSWORD_COMPLEXITY_MIN', '3', '密码复杂度最低要求（满足几种类型）', 'NUMBER', 1, NOW(), 'system'),

-- 短信验证码安全配置
('sec_008', 'SMS_SEND_INTERVAL', '60', '短信发送间隔（秒）', 'NUMBER', 1, NOW(), 'system'),
('sec_009', 'SMS_CODE_EXPIRE_TIME', '300', '验证码有效期（秒）', 'NUMBER', 1, NOW(), 'system'),
('sec_010', 'SMS_DAILY_LIMIT', '10', '每日发送次数限制', 'NUMBER', 1, NOW(), 'system'),
('sec_011', 'SMS_VERIFY_MAX_ATTEMPTS', '5', '验证码最大尝试次数', 'NUMBER', 1, NOW(), 'system'),
('sec_012', 'SMS_IP_HOURLY_LIMIT', '20', '每小时每IP发送限制', 'NUMBER', 1, NOW(), 'system'),

-- 文件上传安全配置
('sec_013', 'UPLOAD_MAX_FILE_SIZE', '10485760', '最大文件上传大小（字节）', 'NUMBER', 1, NOW(), 'system'),
('sec_014', 'UPLOAD_ALLOWED_TYPES', 'jpg,jpeg,png,gif,bmp,pdf,doc,docx,xls,xlsx,zip,rar', '允许上传的文件类型', 'STRING', 1, NOW(), 'system'),
('sec_015', 'UPLOAD_FORBIDDEN_TYPES', 'jsp,php,asp,aspx,exe,bat,cmd,sh,py,rb,class,jar', '禁止上传的文件类型', 'STRING', 1, NOW(), 'system'),
('sec_016', 'UPLOAD_SCAN_CONTENT', 'true', '是否扫描文件内容', 'BOOLEAN', 1, NOW(), 'system'),

-- 支付安全配置
('sec_017', 'PAYMENT_MIN_AMOUNT', '0.01', '最小支付金额', 'NUMBER', 1, NOW(), 'system'),
('sec_018', 'PAYMENT_MAX_AMOUNT', '999999.99', '最大支付金额', 'NUMBER', 1, NOW(), 'system'),
('sec_019', 'PAYMENT_VERIFY_AMOUNT', 'true', '是否验证支付金额', 'BOOLEAN', 1, NOW(), 'system'),
('sec_020', 'PAYMENT_LOG_ENABLED', 'true', '是否记录支付日志', 'BOOLEAN', 1, NOW(), 'system'),

-- 文件下载安全配置
('sec_021', 'DOWNLOAD_AUTH_REQUIRED', 'true', '文件下载是否需要认证', 'BOOLEAN', 1, NOW(), 'system'),
('sec_022', 'DOWNLOAD_PUBLIC_TYPES', 'jpg,jpeg,png,gif,bmp,ico,svg', '公开下载的文件类型', 'STRING', 1, NOW(), 'system'),
('sec_023', 'DOWNLOAD_SENSITIVE_TYPES', 'pdf,doc,docx,xls,xlsx,txt,csv,zip,rar', '敏感文件类型（需要权限）', 'STRING', 1, NOW(), 'system'),
('sec_024', 'DOWNLOAD_MAX_FILE_SIZE', '104857600', '最大下载文件大小（字节）', 'NUMBER', 1, NOW(), 'system'),

-- 传输安全配置
('sec_025', 'HTTPS_REQUIRED', 'true', '是否强制HTTPS', 'BOOLEAN', 1, NOW(), 'system'),
('sec_026', 'ENCRYPT_SENSITIVE_DATA', 'true', '是否加密敏感数据传输', 'BOOLEAN', 1, NOW(), 'system'),
('sec_027', 'REQUEST_SIGNATURE_REQUIRED', 'false', '是否需要请求签名', 'BOOLEAN', 0, NOW(), 'system'),

-- 登录安全配置
('sec_028', 'LOGIN_MAX_ATTEMPTS', '5', '登录最大尝试次数', 'NUMBER', 1, NOW(), 'system'),
('sec_029', 'LOGIN_LOCK_TIME', '1800', '登录锁定时间（秒）', 'NUMBER', 1, NOW(), 'system'),
('sec_030', 'LOGIN_SESSION_TIMEOUT', '7200', '登录会话超时时间（秒）', 'NUMBER', 1, NOW(), 'system'),

-- IP安全配置
('sec_031', 'IP_WHITELIST_ENABLED', 'false', '是否启用IP白名单', 'BOOLEAN', 0, NOW(), 'system'),
('sec_032', 'IP_BLACKLIST_ENABLED', 'true', '是否启用IP黑名单', 'BOOLEAN', 1, NOW(), 'system'),
('sec_033', 'IP_RATE_LIMIT', '100', '每分钟IP请求限制', 'NUMBER', 1, NOW(), 'system');

-- 创建安全日志表
CREATE TABLE IF NOT EXISTS `sys_security_log` (
    `id` varchar(32) NOT NULL COMMENT '主键',
    `log_type` varchar(50) NOT NULL COMMENT '日志类型：LOGIN, UPLOAD, DOWNLOAD, PAYMENT, SMS',
    `event_type` varchar(50) NOT NULL COMMENT '事件类型：SUCCESS, FAILURE, WARNING, BLOCKED',
    `user_id` varchar(32) DEFAULT NULL COMMENT '用户ID',
    `username` varchar(100) DEFAULT NULL COMMENT '用户名',
    `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
    `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
    `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
    `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
    `event_desc` varchar(1000) DEFAULT NULL COMMENT '事件描述',
    `risk_level` varchar(20) DEFAULT 'LOW' COMMENT '风险级别：LOW, MEDIUM, HIGH, CRITICAL',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_log_type` (`log_type`),
    KEY `idx_event_type` (`event_type`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_client_ip` (`client_ip`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_risk_level` (`risk_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统安全日志表';

-- 创建IP黑名单表
CREATE TABLE IF NOT EXISTS `sys_ip_blacklist` (
    `id` varchar(32) NOT NULL COMMENT '主键',
    `ip_address` varchar(50) NOT NULL COMMENT 'IP地址',
    `ip_range` varchar(100) DEFAULT NULL COMMENT 'IP范围（CIDR格式）',
    `block_reason` varchar(200) DEFAULT NULL COMMENT '封禁原因',
    `block_type` varchar(20) DEFAULT 'MANUAL' COMMENT '封禁类型：MANUAL, AUTO',
    `expire_time` datetime DEFAULT NULL COMMENT '过期时间（NULL表示永久）',
    `is_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_ip_address` (`ip_address`),
    KEY `idx_is_enabled` (`is_enabled`),
    KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP黑名单表';

-- 创建安全事件表
CREATE TABLE IF NOT EXISTS `sys_security_event` (
    `id` varchar(32) NOT NULL COMMENT '主键',
    `event_code` varchar(50) NOT NULL COMMENT '事件代码',
    `event_name` varchar(100) NOT NULL COMMENT '事件名称',
    `event_desc` varchar(500) DEFAULT NULL COMMENT '事件描述',
    `risk_level` varchar(20) DEFAULT 'LOW' COMMENT '风险级别',
    `auto_block` tinyint(1) DEFAULT '0' COMMENT '是否自动封禁',
    `block_duration` int(11) DEFAULT '3600' COMMENT '封禁时长（秒）',
    `notification_enabled` tinyint(1) DEFAULT '0' COMMENT '是否启用通知',
    `is_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_event_code` (`event_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全事件配置表';

-- 插入安全事件配置
INSERT INTO `sys_security_event` (`id`, `event_code`, `event_name`, `event_desc`, `risk_level`, `auto_block`, `block_duration`, `notification_enabled`, `is_enabled`, `create_time`, `create_by`) VALUES
('evt_001', 'LOGIN_FAILURE', '登录失败', '用户登录失败', 'LOW', 0, 0, 0, 1, NOW(), 'system'),
('evt_002', 'LOGIN_BRUTE_FORCE', '暴力破解', '短时间内多次登录失败', 'HIGH', 1, 3600, 1, 1, NOW(), 'system'),
('evt_003', 'UPLOAD_MALICIOUS_FILE', '恶意文件上传', '尝试上传恶意文件', 'CRITICAL', 1, 7200, 1, 1, NOW(), 'system'),
('evt_004', 'DOWNLOAD_UNAUTHORIZED', '未授权下载', '尝试下载未授权文件', 'MEDIUM', 0, 1800, 1, 1, NOW(), 'system'),
('evt_005', 'PAYMENT_AMOUNT_ZERO', '零元支付', '尝试进行零元支付', 'HIGH', 1, 3600, 1, 1, NOW(), 'system'),
('evt_006', 'SMS_FREQUENCY_ABUSE', '短信频率滥用', '短时间内频繁发送短信', 'MEDIUM', 1, 1800, 0, 1, NOW(), 'system'),
('evt_007', 'PATH_TRAVERSAL', '路径遍历攻击', '尝试进行路径遍历攻击', 'CRITICAL', 1, 7200, 1, 1, NOW(), 'system'),
('evt_008', 'SQL_INJECTION', 'SQL注入攻击', '检测到SQL注入尝试', 'CRITICAL', 1, 7200, 1, 1, NOW(), 'system'),
('evt_009', 'XSS_ATTACK', 'XSS攻击', '检测到XSS攻击尝试', 'HIGH', 1, 3600, 1, 1, NOW(), 'system'),
('evt_010', 'CSRF_ATTACK', 'CSRF攻击', '检测到CSRF攻击尝试', 'HIGH', 1, 3600, 1, 1, NOW(), 'system');

-- 创建安全规则表
CREATE TABLE IF NOT EXISTS `sys_security_rule` (
    `id` varchar(32) NOT NULL COMMENT '主键',
    `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
    `rule_type` varchar(50) NOT NULL COMMENT '规则类型：RATE_LIMIT, PATTERN_MATCH, THRESHOLD',
    `rule_config` text COMMENT '规则配置（JSON格式）',
    `target_type` varchar(50) DEFAULT NULL COMMENT '目标类型：IP, USER, GLOBAL',
    `action_type` varchar(50) DEFAULT 'LOG' COMMENT '动作类型：LOG, BLOCK, ALERT',
    `is_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
    `priority` int(11) DEFAULT '0' COMMENT '优先级',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
    PRIMARY KEY (`id`),
    KEY `idx_rule_type` (`rule_type`),
    KEY `idx_is_enabled` (`is_enabled`),
    KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全规则表';

-- 插入默认安全规则
INSERT INTO `sys_security_rule` (`id`, `rule_name`, `rule_type`, `rule_config`, `target_type`, `action_type`, `is_enabled`, `priority`, `create_time`, `create_by`) VALUES
('rule_001', '登录频率限制', 'RATE_LIMIT', '{"window": 300, "limit": 5, "action": "block"}', 'IP', 'BLOCK', 1, 100, NOW(), 'system'),
('rule_002', '文件上传频率限制', 'RATE_LIMIT', '{"window": 60, "limit": 10, "action": "block"}', 'USER', 'BLOCK', 1, 90, NOW(), 'system'),
('rule_003', 'SQL注入检测', 'PATTERN_MATCH', '{"patterns": ["union.*select", "drop.*table", "exec.*sp_"], "action": "block"}', 'GLOBAL', 'BLOCK', 1, 95, NOW(), 'system'),
('rule_004', 'XSS攻击检测', 'PATTERN_MATCH', '{"patterns": ["<script", "javascript:", "onerror="], "action": "block"}', 'GLOBAL', 'BLOCK', 1, 95, NOW(), 'system'),
('rule_005', '大额支付监控', 'THRESHOLD', '{"threshold": 10000, "action": "alert"}', 'USER', 'ALERT', 1, 80, NOW(), 'system');

-- 更新现有配置表，添加安全相关配置
INSERT IGNORE INTO `inz_config` (`id`, `name`, `code`, `value`, `create_time`, `create_by`) VALUES
('config_sec_001', '密码强度验证开关', 'PASSWORD_STRENGTH_CHECK', '1', NOW(), 'system'),
('config_sec_002', '短信验证码防重放开关', 'SMS_ANTI_REPLAY', '1', NOW(), 'system'),
('config_sec_003', '文件上传安全检查开关', 'FILE_UPLOAD_SECURITY_CHECK', '1', NOW(), 'system'),
('config_sec_004', '支付金额验证开关', 'PAYMENT_AMOUNT_VALIDATION', '1', NOW(), 'system'),
('config_sec_005', '文件下载权限验证开关', 'FILE_DOWNLOAD_AUTH_CHECK', '1', NOW(), 'system'),
('config_sec_006', 'HTTPS强制开关', 'FORCE_HTTPS', '0', NOW(), 'system'),
('config_sec_007', '安全日志记录开关', 'SECURITY_LOG_ENABLED', '1', NOW(), 'system'),
('config_sec_008', 'IP黑名单检查开关', 'IP_BLACKLIST_CHECK', '1', NOW(), 'system'),
('config_sec_009', '安全事件自动处理开关', 'AUTO_SECURITY_HANDLING', '1', NOW(), 'system'),
('config_sec_010', '安全通知开关', 'SECURITY_NOTIFICATION', '1', NOW(), 'system');
