package org.jeecg.modules.inz_coupons.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 优惠券表
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Data
@TableName("inz_coupon")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_coupon对象", description="优惠券表")
public class InzCoupons implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**优惠券名称*/
	@Excel(name = "优惠券名称", width = 15)
    @ApiModelProperty(value = "优惠券名称")
    private java.lang.String name;
	/**优惠券类型（1：固定金额，2：折扣）*/
	@Excel(name = "优惠券类型（1：固定金额，2：折扣）", width = 15)
    @ApiModelProperty(value = "优惠券类型（1：固定金额，2：折扣）")
    private java.lang.Integer type;
	/**商品范围（1：通用，2：指定商品）*/
	@Excel(name = "商品范围", width = 15)
    @ApiModelProperty(value = "商品范围（1：通用，2：指定商品）")
    private java.lang.Integer productScope;
	/**指定商品ID列表*/
    @ApiModelProperty(value = "指定商品ID列表（JSON数组格式，仅当product_scope=2时有效）")
    private java.lang.String productIds;
	/**优惠金额/折扣率*/
	@Excel(name = "优惠金额/折扣率", width = 15)
    @ApiModelProperty(value = "优惠金额/折扣率")
    private java.math.BigDecimal amount;
	/**最低使用金额*/
	@Excel(name = "最低使用金额", width = 15)
    @ApiModelProperty(value = "最低使用金额")
    private java.math.BigDecimal minAmount;
	/**店铺ID*/
	@Excel(name = "店铺ID", width = 15)
    @ApiModelProperty(value = "店铺ID")
    private java.lang.String storeId;
	/**发行总量*/
	@Excel(name = "发行总量", width = 15)
    @ApiModelProperty(value = "发行总量")
    private java.lang.Integer totalQuantity;
	/**剩余数量*/
	@Excel(name = "剩余数量", width = 15)
    @ApiModelProperty(value = "剩余数量")
    private java.lang.Integer remainingQuantity;
	/**生效时间*/
	@Excel(name = "生效时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "生效时间")
    private java.util.Date startTime;
	/**失效时间*/
	@Excel(name = "失效时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "失效时间")
    private java.util.Date endTime;
	/**使用说明*/
	@Excel(name = "使用说明", width = 15)
    @ApiModelProperty(value = "使用说明")
    private java.lang.String description;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
    /**状态（0：禁用，1：启用）*/
    @Excel(name = "状态（0：禁用，1：启用）", width = 15)
    @ApiModelProperty(value = "状态（0：禁用，1：启用）")
    private java.lang.Integer status;
    /**获取来源*/
    @Excel(name = "获取来源", width = 15)
    @ApiModelProperty(value = "获取来源（领券中心、店铺活动、白名单等）")
    private java.lang.String source;
    /**发放方式*/
    @Excel(name = "发放方式", width = 15)
    @ApiModelProperty(value = "发放方式（1：一码一用，2：一码多用）")
    private java.lang.Integer distributionType;
    /**指定用户*/
    @ApiModelProperty(value = "指定用户（JSON数组格式，存储用户ID列表）")
    private java.lang.String specifiedUsers;
    /**有效期类型（1：固定日期，2：领取后N天）*/
	@Excel(name = "有效期类型", width = 15)
    @ApiModelProperty(value = "有效期类型（1：固定日期，2：领取后N天）")
    private java.lang.Integer validityType;
    /**有效天数*/
	@Excel(name = "有效天数", width = 15)
    @ApiModelProperty(value = "有效天数（仅当validity_type=2时有效）")
    private java.lang.Integer validityDays;
}
