package org.jeecg.modules.inz_product_activity.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 商品活动表
 * @Author: jeecg-boot
 * @Date:   2025-06-21
 * @Version: V1.0
 */
@Data
@TableName("inz_product_activity")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_product_activity对象", description="商品活动表")
public class InzProductActivity implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**活动名称*/
	@Excel(name = "活动名称", width = 15)
    @ApiModelProperty(value = "活动名称")
    private java.lang.String name;
	/**商品ID*/
	@Excel(name = "商品ID", width = 15)
    @ApiModelProperty(value = "商品ID")
    private java.lang.String productId;
	/**活动标签（如限时购、爆款推荐等）*/
	@Excel(name = "活动标签（如限时购、爆款推荐等）", width = 15)
    @ApiModelProperty(value = "活动标签（如限时购、爆款推荐等）")
    private java.lang.String tag;
	/**活动开始时间*/
	@Excel(name = "活动开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "活动开始时间")
    private java.util.Date startTime;
	/**活动结束时间*/
	@Excel(name = "活动结束时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "活动结束时间")
    private java.util.Date endTime;
	/**活动价格（用于覆盖原价）*/
	@Excel(name = "活动价格（用于覆盖原价）", width = 15)
    @ApiModelProperty(value = "活动价格（用于覆盖原价）")
    private java.math.BigDecimal activityPrice;
	/**活动库存（为空则取商品原始库存）*/
	@Excel(name = "活动库存（为空则取商品原始库存）", width = 15)
    @ApiModelProperty(value = "活动库存（为空则取商品原始库存）")
    private java.lang.Integer activityStock;
	/**活动说明*/
	@Excel(name = "活动说明", width = 15)
    @ApiModelProperty(value = "活动说明")
    private java.lang.String description;
	/**是否启用（0-关闭，1-启用）*/
	@Excel(name = "是否启用（0-关闭，1-启用）", width = 15)
    @ApiModelProperty(value = "是否启用（0-关闭，1-启用）")
    private java.lang.Integer status;
	/**是否全平台可见*/
	@Excel(name = "是否全平台可见", width = 15)
    @ApiModelProperty(value = "是否全平台可见")
    private java.lang.Integer globalVisible;
	/**排序值*/
	@Excel(name = "排序值", width = 15)
    @ApiModelProperty(value = "排序值")
    private java.lang.Integer sort;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
}
