package org.jeecg.modules.user_front.service.impl;

import org.jeecg.modules.user_front.entity.InzUserFront;
import org.jeecg.modules.user_front.entity.InzUserDevice;
import org.jeecg.modules.user_front.mapper.InzUserDeviceMapper;
import org.jeecg.modules.user_front.mapper.InzUserFrontMapper;
import org.jeecg.modules.user_front.service.IInzUserFrontService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 用户表
 * @Author: jeecg-boot
 * @Date:   2025-03-28
 * @Version: V1.0
 */
@Service
public class InzUserFrontServiceImpl extends ServiceImpl<InzUserFrontMapper, InzUserFront> implements IInzUserFrontService {

	@Autowired
	private InzUserFrontMapper inzUserFrontMapper;
	@Autowired
	private InzUserDeviceMapper inzUserDeviceMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(InzUserFront inzUserFront, List<InzUserDevice> inzUserDeviceList) {
		inzUserFrontMapper.insert(inzUserFront);
		if(inzUserDeviceList!=null && inzUserDeviceList.size()>0) {
			for(InzUserDevice entity:inzUserDeviceList) {
				//外键设置
				entity.setUserId(inzUserFront.getId());
				inzUserDeviceMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(InzUserFront inzUserFront,List<InzUserDevice> inzUserDeviceList) {
		inzUserFrontMapper.updateById(inzUserFront);
		
		//1.先删除子表数据
		inzUserDeviceMapper.deleteByMainId(inzUserFront.getId());
		
		//2.子表数据重新插入
		if(inzUserDeviceList!=null && inzUserDeviceList.size()>0) {
			for(InzUserDevice entity:inzUserDeviceList) {
				//外键设置
				entity.setUserId(inzUserFront.getId());
				inzUserDeviceMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		inzUserDeviceMapper.deleteByMainId(id);
		inzUserFrontMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			inzUserDeviceMapper.deleteByMainId(id.toString());
			inzUserFrontMapper.deleteById(id);
		}
	}
	
}
