package org.jeecg.modules.inz_orders_logistics.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.inz_orders_logistics.entity.ShippingRecord;
import org.jeecg.modules.inz_orders_logistics.model.ExpressTrackDTO;

import java.util.List;
import java.util.Map;

/**
 * @Description: 发货记录服务接口
 * @Author: jeecg-boot
 * @Date: 2024-07-28
 * @Version: V1.0
 */
public interface IShippingRecordService extends IService<ShippingRecord> {
    
    /**
     * 分页查询发货记录
     *
     * @param page 分页参数
     * @param record 查询条件
     * @return 分页结果
     */
    IPage<ShippingRecord> pageList(Page<ShippingRecord> page, ShippingRecord record);
    
    /**
     * 发货
     *
     * @param record 发货记录
     * @return 是否成功
     */
    boolean ship(ShippingRecord record);
    
    /**
     * 更新物流信息
     *
     * @param id 记录ID
     * @param expressCompany 快递公司
     * @param trackingNo 快递单号
     * @return 是否成功
     */
    boolean updateLogistics(String id, String expressCompany, String trackingNo);
    
    /**
     * 查询物流轨迹
     *
     * @param id 记录ID
     * @return 物流轨迹
     */
    org.jeecg.modules.inz_orders_logistics.model.ExpressTrackDTO queryLogisticsTrack(String id);
    
    /**
     * 批量查询物流轨迹
     *
     * @param ids 记录ID列表
     * @return 物流轨迹映射，key为记录ID，value为轨迹数据
     */
    Map<String, ExpressTrackDTO> batchQueryLogisticsTrack(List<String> ids);
    
    /**
     * 批量订阅物流推送
     *
     * @param ids 记录ID列表
     * @return 是否全部成功
     */
    boolean batchSubscribe(List<String> ids);
} 