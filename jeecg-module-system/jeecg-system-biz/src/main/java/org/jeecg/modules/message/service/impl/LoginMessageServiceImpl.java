package org.jeecg.modules.message.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.message.service.ILoginMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @Description: 登录后消息发送服务实现
 * @Author: jeecg-boot
 * @Date: 2025-01-24
 * @Version: V1.0
 */
@Service
@Slf4j
public class LoginMessageServiceImpl implements ILoginMessageService {

    @Autowired
    private RedisUtil redisUtil;

    // Redis缓存键前缀
    private static final String LOGIN_MESSAGE_CACHE_PREFIX = "CardVerse:login_message:";
    private static final String FIRST_LOGIN_CACHE_PREFIX = "CardVerse:first_login:";

    @Override
    public void sendWelcomeMessage(String userId, String username, boolean isFirstLogin) {
        try {
            String cacheKey = LOGIN_MESSAGE_CACHE_PREFIX + "welcome:" + userId;
            
            // 检查是否已经发送过欢迎消息（24小时内不重复发送）
            if (redisUtil.hasKey(cacheKey)) {
                log.info("用户 {} 24小时内已发送过欢迎消息，跳过发送", username);
                return;
            }

            String title = isFirstLogin ? "欢迎加入卡片交易平台！" : "欢迎回来！";
            String content = isFirstLogin ? 
                "🎉 欢迎加入我们的卡片交易平台！\n\n" +
                "在这里您可以：\n" +
                "• 买卖各种珍稀卡片\n" +
                "• 参与卡片拆包直播\n" +
                "• 与其他收藏爱好者交流\n" +
                "• 享受专业的卡片评级服务\n\n" +
                "祝您交易愉快！" :
                "🎉 欢迎回来！\n\n" +
                "感谢您再次使用我们的平台，" +
                "希望您能在这里找到心仪的卡片！\n\n" +
                "祝您交易愉快！";

            // 发送消息到inz_message表
            sendMessageToUser(userId, 1, title, content, null);

            // 设置缓存，24小时过期
            redisUtil.set(cacheKey, "sent", 24 * 60 * 60);
            
            log.info("成功发送欢迎消息给用户: {}", username);
        } catch (Exception e) {
            log.error("发送欢迎消息失败，用户: {}, 错误: {}", username, e.getMessage(), e);
        }
    }

    @Override
    public void sendNewUserWelcomeMessage(String userId, String username) {
        try {
            String cacheKey = FIRST_LOGIN_CACHE_PREFIX + userId;
            
            // 标记为首次登录用户
            redisUtil.set(cacheKey, "first_login", 30 * 24 * 60 * 60); // 30天过期

            String title = "新手指南";
            String content = "📖 新手指南\n\n" +
                "作为新用户，建议您：\n" +
                "1. 完善个人资料，提升账户安全性\n" +
                "2. 浏览热门卡片，了解市场行情\n" +
                "3. 关注感兴趣的店铺和卡片\n" +
                "4. 参与社区讨论，学习收藏知识\n" +
                "5. 阅读平台规则，确保交易安全\n\n" +
                "如有疑问，请随时联系客服！";

            sendMessageToUser(userId, 1, title, content, null);
            
            log.info("成功发送新手指南给用户: {}", username);
        } catch (Exception e) {
            log.error("发送新手指南失败，用户: {}, 错误: {}", username, e.getMessage(), e);
        }
    }

    @Override
    public void sendAnnouncementMessage(String userId, String username) {
        try {
            String cacheKey = LOGIN_MESSAGE_CACHE_PREFIX + "announcement:" + userId;
            
            // 检查是否已经发送过公告消息（7天内不重复发送）
            if (redisUtil.hasKey(cacheKey)) {
                return;
            }

            String title = "平台最新公告";
            String content = "📢 平台最新公告\n\n" +
                "• 平台持续优化用户体验\n" +
                "• 新增卡片评级服务\n" +
                "• 加强交易安全保障\n" +
                "• 推出积分商城功能\n\n" +
                "更多详情请查看公告中心！";

            sendMessageToUser(userId, 1, title, content, null);

            // 设置缓存，7天过期
            redisUtil.set(cacheKey, "sent", 7 * 24 * 60 * 60);
            
            log.info("成功发送平台公告给用户: {}", username);
        } catch (Exception e) {
            log.error("发送平台公告失败，用户: {}, 错误: {}", username, e.getMessage(), e);
        }
    }

    @Override
    public void sendReportRewardMessage(String userId, String username) {
        try {
            String cacheKey = LOGIN_MESSAGE_CACHE_PREFIX + "report_reward:" + userId;
            
            // 检查是否已经发送过举报有奖消息（30天内不重复发送）
            if (redisUtil.hasKey(cacheKey)) {
                return;
            }

            String title = "举报有奖活动";
            String content = "🎁 举报有奖活动\n\n" +
                "为维护平台交易环境，我们推出举报有奖活动：\n\n" +
                "举报以下行为可获得奖励：\n" +
                "• 虚假商品信息 - 奖励50积分\n" +
                "• 恶意刷单行为 - 奖励100积分\n" +
                "• 违规交易行为 - 奖励200积分\n" +
                "• 其他违规行为 - 奖励30积分\n\n" +
                "举报方式：联系在线客服\n" +
                "感谢您为平台建设贡献力量！";

            sendMessageToUser(userId, 1, title, content, null);

            // 设置缓存，30天过期
            redisUtil.set(cacheKey, "sent", 30 * 24 * 60 * 60);
            
            log.info("成功发送举报有奖消息给用户: {}", username);
        } catch (Exception e) {
            log.error("发送举报有奖消息失败，用户: {}, 错误: {}", username, e.getMessage(), e);
        }
    }

    @Override
    public void sendPlatformRulesMessage(String userId, String username) {
        try {
            String cacheKey = LOGIN_MESSAGE_CACHE_PREFIX + "rules:" + userId;
            
            // 检查是否已经发送过规则消息（15天内不重复发送）
            if (redisUtil.hasKey(cacheKey)) {
                return;
            }

            String title = "平台交易规则提醒";
            String content = "⚠️ 平台交易规则提醒\n\n" +
                "为保障您的交易安全，请遵守以下规则：\n\n" +
                "1. 禁止发布虚假商品信息\n" +
                "2. 禁止恶意刷单和虚假交易\n" +
                "3. 禁止线下私下交易\n" +
                "4. 禁止发布违法违规内容\n" +
                "5. 尊重其他用户，文明交流\n\n" +
                "违规行为将面临警告、限制或封号处理。\n" +
                "详细规则请查看用户协议！";

            sendMessageToUser(userId, 1, title, content, null);

            // 设置缓存，15天过期
            redisUtil.set(cacheKey, "sent", 15 * 24 * 60 * 60);
            
            log.info("成功发送平台规则消息给用户: {}", username);
        } catch (Exception e) {
            log.error("发送平台规则消息失败，用户: {}, 错误: {}", username, e.getMessage(), e);
        }
    }

    @Override
    public void sendSecurityReminderMessage(String userId, String username) {
        try {
            String cacheKey = LOGIN_MESSAGE_CACHE_PREFIX + "security:" + userId;
            
            // 检查是否已经发送过安全提醒消息（7天内不重复发送）
            if (redisUtil.hasKey(cacheKey)) {
                return;
            }

            String title = "账户安全提醒";
            String content = "🔒 账户安全提醒\n\n" +
                "为保障您的账户安全，请注意：\n\n" +
                "• 定期修改登录密码\n" +
                "• 不要向他人透露账户信息\n" +
                "• 发现异常登录及时联系客服\n" +
                "• 绑定手机号和邮箱\n" +
                "• 开启登录验证功能\n\n" +
                "如发现账户异常，请立即联系客服！";

            sendMessageToUser(userId, 1, title, content, null);

            // 设置缓存，7天过期
            redisUtil.set(cacheKey, "sent", 7 * 24 * 60 * 60);
            
            log.info("成功发送安全提醒消息给用户: {}", username);
        } catch (Exception e) {
            log.error("发送安全提醒消息失败，用户: {}, 错误: {}", username, e.getMessage(), e);
        }
    }

    @Override
    public void sendAllLoginMessages(String userId, String username, boolean isFirstLogin) {
        log.info("开始为用户 {} 发送登录后自动消息，首次登录: {}", username, isFirstLogin);
        
        try {
            // 1. 发送欢迎消息（必发）
            sendWelcomeMessage(userId, username, isFirstLogin);
            
            // 2. 如果是首次登录，发送新手指南
            if (isFirstLogin) {
                sendNewUserWelcomeMessage(userId, username);
            }
            
            // 3. 发送平台公告（根据缓存策略）
            sendAnnouncementMessage(userId, username);
            
            // 4. 发送举报有奖消息（根据缓存策略）
            sendReportRewardMessage(userId, username);
            
            // 5. 发送平台规则提醒（根据缓存策略）
            sendPlatformRulesMessage(userId, username);
            
            // 6. 发送安全提醒（根据缓存策略）
            sendSecurityReminderMessage(userId, username);
            
            log.info("完成为用户 {} 发送登录后自动消息", username);
        } catch (Exception e) {
            log.error("发送登录后自动消息失败，用户: {}, 错误: {}", username, e.getMessage(), e);
        }
    }

    /**
     * 发送消息到用户消息表
     * @param userId 用户ID
     * @param type 消息类型
     * @param title 消息标题
     * @param content 消息内容
     * @param relationId 关联ID
     */
    private void sendMessageToUser(String userId, Integer type, String title, String content, String relationId) {
        try {
            // 通过反射调用Message相关的服务
            Object messageService = SpringContextUtils.getBean("messageServiceImpl");
            if (messageService != null) {
                // 创建Message对象
                Class<?> messageClass = Class.forName("org.jeecg.modules.api.message.entity.Message");
                Object message = messageClass.newInstance();
                
                // 设置消息属性
                messageClass.getMethod("setUserId", String.class).invoke(message, userId);
                messageClass.getMethod("setType", Integer.class).invoke(message, type);
                messageClass.getMethod("setTitle", String.class).invoke(message, title);
                messageClass.getMethod("setContent", String.class).invoke(message, content);
                messageClass.getMethod("setRelationId", String.class).invoke(message, relationId);
                messageClass.getMethod("setStatus", Integer.class).invoke(message, 0); // 未读
                messageClass.getMethod("setCreateTime", Date.class).invoke(message, new Date());
                messageClass.getMethod("setCreateBy", String.class).invoke(message, "system");
                
                // 调用保存方法
                messageService.getClass().getMethod("createMessage", messageClass).invoke(messageService, message);
                
                log.debug("成功发送消息到用户 {}: {}", userId, title);
            } else {
                log.warn("未找到messageService，无法发送消息");
            }
        } catch (Exception e) {
            log.error("发送消息到用户失败，用户ID: {}, 标题: {}, 错误: {}", userId, title, e.getMessage(), e);
        }
    }

    /**
     * 检查是否为首次登录
     * @param userId 用户ID
     * @return 是否首次登录
     */
    public boolean isFirstLogin(String userId) {
        String cacheKey = FIRST_LOGIN_CACHE_PREFIX + userId;
        return !redisUtil.hasKey(cacheKey);
    }
}
