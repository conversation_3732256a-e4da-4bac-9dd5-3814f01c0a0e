package org.jeecg.modules.inz_product_settlement.controller;

import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_product_settlement.entity.InzSettlementBatch;
import org.jeecg.modules.inz_product_settlement.service.IInzSettlementBatchService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 结算批次
 * @Author: jeecg-boot
 * @Date: 2025-06-26
 * @Version: V1.0
 */
@Api(tags="结算批次")
@RestController
@RequestMapping("/inz_settlement_batch")
@Slf4j
public class InzSettlementBatchController extends JeecgController<InzSettlementBatch, IInzSettlementBatchService> {
	@Autowired
	private IInzSettlementBatchService inzSettlementBatchService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzSettlementBatch
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "结算批次-分页列表查询")
	@ApiOperation(value="结算批次-分页列表查询", notes="结算批次-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(InzSettlementBatch inzSettlementBatch,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<InzSettlementBatch> queryWrapper = QueryGenerator.initQueryWrapper(inzSettlementBatch, req.getParameterMap());
		Page<InzSettlementBatch> page = new Page<InzSettlementBatch>(pageNo, pageSize);
		IPage<InzSettlementBatch> pageList = inzSettlementBatchService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param inzSettlementBatch
	 * @return
	 */
	@AutoLog(value = "结算批次-添加")
	@ApiOperation(value="结算批次-添加", notes="结算批次-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody InzSettlementBatch inzSettlementBatch) {
		inzSettlementBatchService.save(inzSettlementBatch);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param inzSettlementBatch
	 * @return
	 */
	@AutoLog(value = "结算批次-编辑")
	@ApiOperation(value="结算批次-编辑", notes="结算批次-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody InzSettlementBatch inzSettlementBatch) {
		inzSettlementBatchService.updateById(inzSettlementBatch);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "结算批次-通过id删除")
	@ApiOperation(value="结算批次-通过id删除", notes="结算批次-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		inzSettlementBatchService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "结算批次-批量删除")
	@ApiOperation(value="结算批次-批量删除", notes="结算批次-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzSettlementBatchService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "结算批次-通过id查询")
	@ApiOperation(value="结算批次-通过id查询", notes="结算批次-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		InzSettlementBatch inzSettlementBatch = inzSettlementBatchService.getById(id);
		if(inzSettlementBatch==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzSettlementBatch);
	}

	/**
	 * 创建结算批次
	 *
	 * @param batchName
	 * @param storeIds
	 * @param startTime
	 * @param endTime
	 * @param remark
	 * @return
	 */
	@AutoLog(value = "结算批次-创建批次")
	@ApiOperation(value="结算批次-创建批次", notes="创建新的结算批次")
	@PostMapping(value = "/createBatch")
	public Result<?> createBatch(@RequestParam(name="batchName",required=true) String batchName,
								 @RequestParam(name="storeIds",required=false) String storeIds,
								 @RequestParam(name="startTime",required=false) Date startTime,
								 @RequestParam(name="endTime",required=false) Date endTime,
								 @RequestParam(name="remark",required=false) String remark) {
		try {
			List<String> storeIdList = null;
			if (storeIds != null && !storeIds.trim().isEmpty()) {
				storeIdList = Arrays.asList(storeIds.split(","));
			}
			
			String batchId = inzSettlementBatchService.createSettlementBatch(batchName, storeIdList, startTime, endTime, remark);
			if (batchId != null) {
				return Result.OK("创建批次成功", batchId);
			} else {
				return Result.error("创建批次失败，没有找到符合条件的待结算记录");
			}
		} catch (Exception e) {
			log.error("创建结算批次异常", e);
			return Result.error("创建批次异常：" + e.getMessage());
		}
	}

	/**
	 * 执行批次结算
	 *
	 * @param batchId
	 * @return
	 */
	@AutoLog(value = "结算批次-执行批次结算")
	@ApiOperation(value="结算批次-执行批次结算", notes="执行指定批次的结算操作")
	@PostMapping(value = "/executeBatch")
	public Result<?> executeBatch(@RequestParam(name="batchId",required=true) String batchId) {
		try {
			boolean success = inzSettlementBatchService.executeBatchSettlement(batchId);
			if (success) {
				return Result.OK("执行批次结算成功");
			} else {
				return Result.error("执行批次结算失败");
			}
		} catch (Exception e) {
			log.error("执行批次结算异常", e);
			return Result.error("执行批次结算异常：" + e.getMessage());
		}
	}

	/**
	 * 取消结算批次
	 *
	 * @param batchId
	 * @param reason
	 * @return
	 */
	@AutoLog(value = "结算批次-取消批次")
	@ApiOperation(value="结算批次-取消批次", notes="取消指定的结算批次")
	@PostMapping(value = "/cancelBatch")
	public Result<?> cancelBatch(@RequestParam(name="batchId",required=true) String batchId,
								 @RequestParam(name="reason",required=false) String reason) {
		try {
			boolean success = inzSettlementBatchService.cancelSettlementBatch(batchId, reason);
			if (success) {
				return Result.OK("取消批次成功");
			} else {
				return Result.error("取消批次失败");
			}
		} catch (Exception e) {
			log.error("取消结算批次异常", e);
			return Result.error("取消批次异常：" + e.getMessage());
		}
	}

	/**
	 * 获取批次详情
	 *
	 * @param batchId
	 * @return
	 */
	@AutoLog(value = "结算批次-获取批次详情")
	@ApiOperation(value="结算批次-获取批次详情", notes="获取批次的详细信息")
	@GetMapping(value = "/batchDetail")
	public Result<?> getBatchDetail(@RequestParam(name="batchId",required=true) String batchId) {
		try {
			Map<String, Object> result = inzSettlementBatchService.getBatchDetail(batchId);
			return Result.OK(result);
		} catch (Exception e) {
			log.error("获取批次详情异常", e);
			return Result.error("获取批次详情异常：" + e.getMessage());
		}
	}

	/**
	 * 获取批次统计信息
	 *
	 * @param batchId
	 * @return
	 */
	@AutoLog(value = "结算批次-获取批次统计")
	@ApiOperation(value="结算批次-获取批次统计", notes="获取批次的统计信息")
	@GetMapping(value = "/batchStatistics")
	public Result<?> getBatchStatistics(@RequestParam(name="batchId",required=true) String batchId) {
		try {
			Map<String, Object> result = inzSettlementBatchService.getBatchStatistics(batchId);
			return Result.OK(result);
		} catch (Exception e) {
			log.error("获取批次统计异常", e);
			return Result.error("获取批次统计异常：" + e.getMessage());
		}
	}

	/**
	 * 更新批次统计信息
	 *
	 * @param batchId
	 * @return
	 */
	@AutoLog(value = "结算批次-更新批次统计")
	@ApiOperation(value="结算批次-更新批次统计", notes="重新计算批次的统计信息")
	@PostMapping(value = "/updateStatistics")
	public Result<?> updateStatistics(@RequestParam(name="batchId",required=true) String batchId) {
		try {
			boolean success = inzSettlementBatchService.updateBatchStatistics(batchId);
			if (success) {
				return Result.OK("更新统计信息成功");
			} else {
				return Result.error("更新统计信息失败");
			}
		} catch (Exception e) {
			log.error("更新批次统计异常", e);
			return Result.error("更新统计异常：" + e.getMessage());
		}
	}

	/**
	 * 导出excel
	 *
	 * @param request
	 * @param inzSettlementBatch
	 */
	@RequestMapping(value = "/exportXls")
	public ModelAndView exportXls(HttpServletRequest request, InzSettlementBatch inzSettlementBatch) {
		return super.exportXls(request, inzSettlementBatch, InzSettlementBatch.class, "结算批次");
	}

	/**
	 * 通过excel导入数据
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		return super.importExcel(request, response, InzSettlementBatch.class);
	}
}
