package org.jeecg.modules.inz_store_blacklist.service.impl;

import org.jeecg.modules.inz_store_blacklist.entity.InzStoreBlacklist;
import org.jeecg.modules.inz_store_blacklist.mapper.InzStoreBlacklistMapper;
import org.jeecg.modules.inz_store_blacklist.service.IInzStoreBlacklistService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 黑名单表
 * @Author: jeecg-boot
 * @Date:   2025-06-22
 * @Version: V1.0
 */
@Service
public class InzStoreBlacklistServiceImpl extends ServiceImpl<InzStoreBlacklistMapper, InzStoreBlacklist> implements IInzStoreBlacklistService {

}
