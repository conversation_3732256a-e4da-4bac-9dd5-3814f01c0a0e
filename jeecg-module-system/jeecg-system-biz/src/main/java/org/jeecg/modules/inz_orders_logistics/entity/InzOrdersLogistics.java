package org.jeecg.modules.inz_orders_logistics.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 订单物流信息表
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Data
@TableName("inz_order_logistics")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_order_logistics对象", description="订单物流信息表")
public class InzOrdersLogistics implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**订单ID*/
	@Excel(name = "订单ID", width = 15)
    @ApiModelProperty(value = "订单ID")
    private java.lang.String orderId;
	/**订单编号*/
	@Excel(name = "订单编号", width = 15)
    @ApiModelProperty(value = "订单编号")
    private java.lang.String orderNo;
	/**业务类型(1:普通订单,2:积分订单)*/
	@Excel(name = "业务类型(1:普通订单,2:积分订单)", width = 15)
    @ApiModelProperty(value = "业务类型(1:普通订单,2:积分订单)")
    private java.lang.Integer bizType;
	/**物流单号*/
	@Excel(name = "物流单号", width = 15)
    @ApiModelProperty(value = "物流单号")
    private java.lang.String trackingNo;
	/**物流公司名称*/
	@Excel(name = "物流公司名称", width = 15)
    @ApiModelProperty(value = "物流公司名称")
    private java.lang.String expressCompany;
	/**物流公司编码*/
	@Excel(name = "物流公司编码", width = 15)
    @ApiModelProperty(value = "物流公司编码")
    private java.lang.String expressCode;
	/**物流状态(1:已发货,2:运输中,3:已签收,4:异常)*/
	@Excel(name = "物流状态(1:已发货,2:运输中,3:已签收,4:异常)", width = 15)
    @ApiModelProperty(value = "物流状态(1:已发货,2:运输中,3:已签收,4:异常)")
    private java.lang.Integer logisticsStatus;
	/**最新物流信息*/
	@Excel(name = "最新物流信息", width = 15)
    @ApiModelProperty(value = "最新物流信息")
    private java.lang.String latestInfo;
	/**发货时间*/
	@Excel(name = "发货时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发货时间")
    private java.util.Date shipTime;
	/**完成时间*/
	@Excel(name = "完成时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "完成时间")
    private java.util.Date finishTime;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
}
