import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '卡片ID',
    align:"center",
    dataIndex: 'cardId'
   },
   {
    title: '店铺ID',
    align:"center",
    dataIndex: 'storeId'
   },
   {
    title: '直播状态(0:待直播 1:直播中 2:已结束)',
    align:"center",
    dataIndex: 'status'
   },
   {
    title: '直播开始时间',
    align:"center",
    dataIndex: 'startTime'
   },
   {
    title: '直播结束时间',
    align:"center",
    dataIndex: 'endTime'
   },
   {
    title: '直播回放地址',
    align:"center",
    dataIndex: 'replayUrl'
   },
   {
    title: '观看人数',
    align:"center",
    dataIndex: 'viewCount'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '卡片ID',
    field: 'cardId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入卡片ID!'},
          ];
     },
  },
  {
    label: '店铺ID',
    field: 'storeId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入店铺ID!'},
          ];
     },
  },
  {
    label: '直播状态(0:待直播 1:直播中 2:已结束)',
    field: 'status',
    component: 'InputNumber',
  },
  {
    label: '直播开始时间',
    field: 'startTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '直播结束时间',
    field: 'endTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '直播回放地址',
    field: 'replayUrl',
    component: 'Input',
  },
  {
    label: '观看人数',
    field: 'viewCount',
    component: 'InputNumber',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  cardId: {title: '卡片ID',order: 0,view: 'text', type: 'string',},
  storeId: {title: '店铺ID',order: 1,view: 'text', type: 'string',},
  status: {title: '直播状态(0:待直播 1:直播中 2:已结束)',order: 2,view: 'number', type: 'number',},
  startTime: {title: '直播开始时间',order: 3,view: 'datetime', type: 'string',},
  endTime: {title: '直播结束时间',order: 4,view: 'datetime', type: 'string',},
  replayUrl: {title: '直播回放地址',order: 5,view: 'text', type: 'string',},
  viewCount: {title: '观看人数',order: 6,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}