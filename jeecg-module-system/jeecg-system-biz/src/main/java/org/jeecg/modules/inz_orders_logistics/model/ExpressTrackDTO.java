package org.jeecg.modules.inz_orders_logistics.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 物流轨迹数据传输对象
 */
@Data
public class ExpressTrackDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息状态
     */
    private String message;
    
    /**
     * 查询结果状态: 0-无轨迹，1-有轨迹，2-异常，3-超时
     */
    private String state;
    
    /**
     * 快递公司编码
     */
    private String com;
    
    /**
     * 快递单号
     */
    private String nu;
    
    /**
     * 快递状态: 
     * 0-在途，1-揽收，2-疑难，3-签收，4-退签，5-派件，6-退回，7-转单，10-待清关，11-清关中，12-已清关，13-清关异常，14-收件人拒签
     */
    private String status;
    
    /**
     * 轨迹数据列表
     */
    private List<ExpressTrackItemDTO> data;
    
    /**
     * 内部包装的轨迹项
     */
    @Data
    public static class ExpressTrackItemDTO implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 轨迹节点时间
         */
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date time;
        
        /**
         * 轨迹节点描述
         */
        private String context;
        
        /**
         * 轨迹节点地点
         */
        private String location;
    }
} 