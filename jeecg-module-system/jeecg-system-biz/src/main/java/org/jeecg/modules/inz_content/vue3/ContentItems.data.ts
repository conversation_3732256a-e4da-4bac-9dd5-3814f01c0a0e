import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '分类ID',
    align:"center",
    dataIndex: 'categoryId'
   },
   {
    title: '标题',
    align:"center",
    dataIndex: 'title'
   },
   {
    title: '内容',
    align:"center",
    dataIndex: 'content'
   },
   {
    title: '内容类型(agreement:协议,faq:常见问题)',
    align:"center",
    dataIndex: 'itemType'
   },
   {
    title: '排序号',
    align:"center",
    dataIndex: 'sortNo'
   },
   {
    title: '状态(0-禁用,1-启用)',
    align:"center",
    dataIndex: 'status'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '分类ID',
    field: 'categoryId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入分类ID!'},
          ];
     },
  },
  {
    label: '标题',
    field: 'title',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入标题!'},
          ];
     },
  },
  {
    label: '内容',
    field: 'content',
    component: 'InputTextArea',
  },
  {
    label: '内容类型(agreement:协议,faq:常见问题)',
    field: 'itemType',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入内容类型(agreement:协议,faq:常见问题)!'},
          ];
     },
  },
  {
    label: '排序号',
    field: 'sortNo',
    component: 'InputNumber',
  },
  {
    label: '状态(0-禁用,1-启用)',
    field: 'status',
    component: 'InputNumber',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  categoryId: {title: '分类ID',order: 0,view: 'text', type: 'string',},
  title: {title: '标题',order: 1,view: 'text', type: 'string',},
  content: {title: '内容',order: 2,view: 'textarea', type: 'string',},
  itemType: {title: '内容类型(agreement:协议,faq:常见问题)',order: 3,view: 'text', type: 'string',},
  sortNo: {title: '排序号',order: 4,view: 'number', type: 'number',},
  status: {title: '状态(0-禁用,1-启用)',order: 5,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}