package org.jeecg.modules.inz_coupons.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.inz_coupons.entity.InzCoupons;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @Description: 优惠券表
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
public interface IInzCouponsService extends IService<InzCoupons> {
    /**
     * 根据店铺ID查询优惠券信息
     * @param page 分页参数
     * @param inzCoupons 查询条件
     * @param storeId 店铺ID
     * @param parameterMap 请求参数
     * @return 分页结果
     */
    IPage<InzCoupons> queryCouponsByStoreId(Page<InzCoupons> page,
                                           InzCoupons inzCoupons,
                                           String storeId,
                                           Map<String, String[]> parameterMap);

    /**
     * 禁用优惠券
     * @param id 优惠券ID
     */
    void disableCoupon(String id);
    
    /**
     * 禁用兑换码
     * @param code 兑换码
     */
    void disableCouponCode(String code);

    /**
     * 生成并发布优惠券
     * @param inzCoupons 优惠券实体
     */
    void generateAndPublishCoupon(InzCoupons inzCoupons);

    /**
     * 生成兑换码
     * @param couponId 优惠券ID
     * @param count 生成数量
     * @return 生成的兑换码信息
     */
    Result<Map<String, Object>> generateCouponCode(String couponId, Integer count);
    
    /**
     * 根据参数直接生成兑换码
     * @param type 优惠券类型（1：固定金额，2：折扣）
     * @param amount 优惠金额/折扣率
     * @param minAmount 最低使用金额
     * @param count 生成数量
     * @param storeId 店铺ID（可选）
     * @return 生成的兑换码信息
     */
    Result<Map<String, Object>> generateCouponCode(Integer type, BigDecimal amount, BigDecimal minAmount, Integer count, String storeId);

    IPage<InzCoupons> queryCouponsByStoreName(Page<InzCoupons> page, String storeName);
}
