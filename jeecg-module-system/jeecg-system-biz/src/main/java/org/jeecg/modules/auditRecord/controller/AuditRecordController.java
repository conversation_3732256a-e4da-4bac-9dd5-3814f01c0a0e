package org.jeecg.modules.auditRecord.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.auditRecord.entity.AuditRecord;
import org.jeecg.modules.auditRecord.service.IAuditRecordService;
import org.jeecg.modules.inz_store.service.IInzStoreService;
import org.jeecg.modules.inz_users_fronts.service.IInzUsersFrontsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.commons.lang3.StringUtils;

 /**
 * @Description: 审核表
 * @Author: jeecg-boot
 * @Date:   2025-06-21
 * @Version: V1.0
 */
@Api(tags="审核表")
@RestController
@RequestMapping("/audit_record/auditRecord")
@Slf4j
public class AuditRecordController extends JeecgController<AuditRecord, IAuditRecordService> {
	@Autowired
	private IAuditRecordService auditRecordService;
	@Autowired
	private IInzStoreService inzStoreService;
	@Autowired
	private IInzUsersFrontsService inzUsersFrontsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param auditRecord
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "审核表-分页列表查询")
	@ApiOperation(value="审核表-分页列表查询", notes="审核表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<AuditRecord>> queryPageList(AuditRecord auditRecord,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   @RequestParam(name="auditType", required=false) Integer auditType,
								   @RequestParam(name="status", required=false) Integer status,
								   @RequestParam(name="storeName", required=false) String storeName,
								   @RequestParam(name="userName", required=false) String username,
								   HttpServletRequest req) {
        QueryWrapper<AuditRecord> queryWrapper = QueryGenerator.initQueryWrapper(auditRecord, req.getParameterMap());
        Page<AuditRecord> page = new Page<>(pageNo, pageSize);
        // 处理审核类型筛选
        if (auditType != null) {
            queryWrapper.eq("audit_type", auditType);
        }
        
        // 处理审核状态筛选
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        
        // 处理店铺名称筛选
        if (StringUtils.isNotBlank(storeName)) {
            // 通过店铺名称获取店铺ID
            String storeId = inzStoreService.getStoreIdByName(storeName);
            if (StringUtils.isNotBlank(storeId)) {
                // 根据审核类型不同，查询方式可能不同
                // 假设审核记录表中有store_id字段
                queryWrapper.eq("store_id", storeId);
            } else {
                // 如果找不到店铺，返回空结果
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }
        }
        
        // 处理用户名称筛选
        if (StringUtils.isNotBlank(username)) {
            // 通过用户名称获取用户ID
            String userId = inzUsersFrontsService.getUserIdByUsername(username);
            if (StringUtils.isNotBlank(userId)) {
                // 假设审核记录表中有user_id字段
                queryWrapper.eq("user_id", userId);
            } else {
                // 如果找不到用户，返回空结果
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }
        }

		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		if(sysUser!=null){
			if(!isAdmin(sysUser)){
				String loginUsername = sysUser.getUsername();
				String userId = null;
				try{
					userId = inzUsersFrontsService.getUserIdByUsername(loginUsername);
					log.info("通过用户名[{}]查询到前端用户ID: {}", loginUsername, userId);
				} catch (Exception e) {
					throw new RuntimeException(e);
				}
				if(StringUtils.isBlank(userId)){
					try{
						userId = inzUsersFrontsService.getUserIdByNickname(loginUsername);
						log.info("通过昵称[{}]查询到前端用户ID: {}", loginUsername, userId);
					} catch (Exception e) {
						throw new RuntimeException(e);
					}
				}
				if(StringUtils.isNotBlank(userId)){
					QueryWrapper<AuditRecord> auditQueryWrapper=new QueryWrapper<>();
					auditQueryWrapper.eq("user_id", userId);
					AuditRecord auditRecord1 = auditRecordService.getOne(auditQueryWrapper);
					if(auditRecord1 != null){
						queryWrapper.eq("auditRecordId", auditRecord1.getId());
					}else{
						return Result.OK(new Page<>(pageNo, pageSize, 0));
					}
				}else{
					return Result.OK(new Page<>(pageNo, pageSize, 0));
				}
			}else{
				log.info("管理员用户访问，不进行数据权限过滤");
			}

		}

		IPage<AuditRecord> pageList = auditRecordService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	 /**
	  * 判断用户是否为管理员
	  * @param user 登录用户
	  * @return 是否为管理员
	  */
	 private boolean isAdmin(org.jeecg.common.system.vo.LoginUser user) {
		 if (user == null) {
			 return false;
		 }
		 String roleCode = user.getRoleCode();
		 return StringUtils.isNotBlank(roleCode) && (roleCode.contains("admin") || roleCode.contains("ADMIN"));
	 }
	
	/**
	 *   添加
	 *
	 * @param auditRecord
	 * @return
	 */
	@AutoLog(value = "审核表-添加")
	@ApiOperation(value="审核表-添加", notes="审核表-添加")
	@RequiresPermissions("auditRecord:audit_record:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody AuditRecord auditRecord) {
		auditRecordService.save(auditRecord);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param auditRecord
	 * @return
	 */
	@AutoLog(value = "审核表-编辑")
	@ApiOperation(value="审核表-编辑", notes="审核表-编辑")
	@RequiresPermissions("auditRecord:audit_record:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody AuditRecord auditRecord) {
		auditRecordService.updateById(auditRecord);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "审核表-通过id删除")
	@ApiOperation(value="审核表-通过id删除", notes="审核表-通过id删除")
	@RequiresPermissions("auditRecord:audit_record:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		auditRecordService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "审核表-批量删除")
	@ApiOperation(value="审核表-批量删除", notes="审核表-批量删除")
	@RequiresPermissions("auditRecord:audit_record:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.auditRecordService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "审核表-通过id查询")
	@ApiOperation(value="审核表-通过id查询", notes="审核表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<AuditRecord> queryById(@RequestParam(name="id",required=true) String id) {
		AuditRecord auditRecord = auditRecordService.getById(id);
		if(auditRecord==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(auditRecord);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param auditRecord
    */
    @RequiresPermissions("auditRecord:audit_record:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AuditRecord auditRecord) {
        return super.exportXls(request, auditRecord, AuditRecord.class, "审核表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("auditRecord:audit_record:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AuditRecord.class);
    }

	/**
	 * 执行审核操作
	 * @return 审核结果
	 */
	@ApiOperation(value = "执行审核操作", notes = "对指定的业务进行审核，通过或驳回")
	@PostMapping(value = "/perform")
	public Result<AuditRecord> performAudit(@RequestBody AuditRecord auditRecord ) {
		return auditRecordService.performAudit(auditRecord.getId(), auditRecord.getStatus(), auditRecord.getComments());
	}

	/**
	 * 提交商品上架审核
	 * @param productId 商品ID
	 * @return 审核结果
	 */
	@ApiOperation(value = "提交商品上架审核", notes = "提交商品上架审核")
	@PostMapping(value = "/submitProductAudit")
	public Result<AuditRecord> submitProductAudit(@RequestParam String productId) {
		return auditRecordService.submitProductForAudit(productId);
	}

	/**
	 * 提交售后申请审核
	 * @param aftersaleId 售后ID
	 * @return 审核结果
	 */
	@ApiOperation(value = "提交售后申请审核", notes = "提交售后申请审核")
	@PostMapping(value = "/submitAftersaleAudit")
	public Result<AuditRecord> submitAftersaleAudit(@RequestParam String aftersaleId) {
		return auditRecordService.submitAftersaleForAudit(aftersaleId);
	}

	/**
	 * 根据商品ID查询审核记录
	 * @param productId 商品ID
	 * @return 审核记录
	 */
	@ApiOperation(value = "根据商品ID查询审核记录", notes = "根据商品ID查询审核记录")
	@GetMapping(value = "/getProductAuditRecord")
	public Result<AuditRecord> getProductAuditRecord(@RequestParam String productId) {
		AuditRecord auditRecord = auditRecordService.getAuditRecordByProductId(productId);
		if (auditRecord == null) {
			return Result.error("未找到对应审核记录");
		}
		return Result.OK(auditRecord);
	}

	/**
	 * 根据售后ID查询审核记录
	 * @param aftersaleId 售后ID
	 * @return 审核记录
	 */
	@ApiOperation(value = "根据售后ID查询审核记录", notes = "根据售后ID查询审核记录")
	@GetMapping(value = "/getAftersaleAuditRecord")
	public Result<AuditRecord> getAftersaleAuditRecord(@RequestParam String aftersaleId) {
		AuditRecord auditRecord = auditRecordService.getAuditRecordByAftersaleId(aftersaleId);
		if (auditRecord == null) {
			return Result.error("未找到对应审核记录");
		}
		return Result.OK(auditRecord);
	}
}
