import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '订单ID',
    align:"center",
    dataIndex: 'orderId'
   },
   {
    title: '订单编号',
    align:"center",
    dataIndex: 'orderNo'
   },
   {
    title: '用户ID',
    align:"center",
    dataIndex: 'userId'
   },
   {
    title: '店铺ID',
    align:"center",
    dataIndex: 'storeId'
   },
   {
    title: '售后类型(1:仅退款,2:退货退款,3:换货)',
    align:"center",
    dataIndex: 'type'
   },
   {
    title: '售后原因',
    align:"center",
    dataIndex: 'reason'
   },
   {
    title: '问题描述',
    align:"center",
    dataIndex: 'description'
   },
   {
    title: '凭证图片(逗号分隔)',
    align:"center",
    dataIndex: 'images'
   },
   {
    title: '退款金额',
    align:"center",
    dataIndex: 'refundAmount'
   },
   {
    title: '售后状态(1:待审核,2:审核通过,3:审核拒绝,4:退款中,5:已完成,6:已取消)',
    align:"center",
    dataIndex: 'status'
   },
   {
    title: '处理备注',
    align:"center",
    dataIndex: 'handleNote'
   },
   {
    title: '退款流水号',
    align:"center",
    dataIndex: 'refundNo'
   },
   {
    title: '审核时间',
    align:"center",
    dataIndex: 'auditTime'
   },
   {
    title: '退款时间',
    align:"center",
    dataIndex: 'refundTime'
   },
   {
    title: '完成时间',
    align:"center",
    dataIndex: 'finishTime'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '订单ID',
    field: 'orderId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入订单ID!'},
          ];
     },
  },
  {
    label: '订单编号',
    field: 'orderNo',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入订单编号!'},
          ];
     },
  },
  {
    label: '用户ID',
    field: 'userId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入用户ID!'},
          ];
     },
  },
  {
    label: '店铺ID',
    field: 'storeId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入店铺ID!'},
          ];
     },
  },
  {
    label: '售后类型(1:仅退款,2:退货退款,3:换货)',
    field: 'type',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入售后类型(1:仅退款,2:退货退款,3:换货)!'},
          ];
     },
  },
  {
    label: '售后原因',
    field: 'reason',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入售后原因!'},
          ];
     },
  },
  {
    label: '问题描述',
    field: 'description',
    component: 'Input',
  },
  {
    label: '凭证图片(逗号分隔)',
    field: 'images',
    component: 'Input',
  },
  {
    label: '退款金额',
    field: 'refundAmount',
    component: 'InputNumber',
  },
  {
    label: '售后状态(1:待审核,2:审核通过,3:审核拒绝,4:退款中,5:已完成,6:已取消)',
    field: 'status',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入售后状态(1:待审核,2:审核通过,3:审核拒绝,4:退款中,5:已完成,6:已取消)!'},
          ];
     },
  },
  {
    label: '处理备注',
    field: 'handleNote',
    component: 'Input',
  },
  {
    label: '退款流水号',
    field: 'refundNo',
    component: 'Input',
  },
  {
    label: '审核时间',
    field: 'auditTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '退款时间',
    field: 'refundTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '完成时间',
    field: 'finishTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  orderId: {title: '订单ID',order: 0,view: 'text', type: 'string',},
  orderNo: {title: '订单编号',order: 1,view: 'text', type: 'string',},
  userId: {title: '用户ID',order: 2,view: 'text', type: 'string',},
  storeId: {title: '店铺ID',order: 3,view: 'text', type: 'string',},
  type: {title: '售后类型(1:仅退款,2:退货退款,3:换货)',order: 4,view: 'number', type: 'number',},
  reason: {title: '售后原因',order: 5,view: 'text', type: 'string',},
  description: {title: '问题描述',order: 6,view: 'text', type: 'string',},
  images: {title: '凭证图片(逗号分隔)',order: 7,view: 'text', type: 'string',},
  refundAmount: {title: '退款金额',order: 8,view: 'number', type: 'number',},
  status: {title: '售后状态(1:待审核,2:审核通过,3:审核拒绝,4:退款中,5:已完成,6:已取消)',order: 9,view: 'number', type: 'number',},
  handleNote: {title: '处理备注',order: 10,view: 'text', type: 'string',},
  refundNo: {title: '退款流水号',order: 11,view: 'text', type: 'string',},
  auditTime: {title: '审核时间',order: 12,view: 'datetime', type: 'string',},
  refundTime: {title: '退款时间',order: 13,view: 'datetime', type: 'string',},
  finishTime: {title: '完成时间',order: 14,view: 'datetime', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}