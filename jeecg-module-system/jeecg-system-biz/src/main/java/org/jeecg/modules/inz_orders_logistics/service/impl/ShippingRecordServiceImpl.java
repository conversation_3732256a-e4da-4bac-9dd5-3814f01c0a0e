package org.jeecg.modules.inz_orders_logistics.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.inz_orders_logistics.entity.ShippingRecord;
import org.jeecg.modules.inz_orders_logistics.mapper.ShippingRecordMapper;
import org.jeecg.modules.inz_orders_logistics.model.ExpressCompanyEnum;
import org.jeecg.modules.inz_orders_logistics.model.ExpressTrackDTO;
import org.jeecg.modules.inz_orders_logistics.service.IExpressTrackingService;
import org.jeecg.modules.inz_orders_logistics.service.IShippingRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 发货记录服务实现类
 * @Author: jeecg-boot
 * @Date: 2024-07-28
 * @Version: V1.0
 */
@Service
@Slf4j
public class ShippingRecordServiceImpl extends ServiceImpl<ShippingRecordMapper, ShippingRecord> implements IShippingRecordService {

    @Autowired
    private IExpressTrackingService expressTrackingService;

    @Override
    public IPage<ShippingRecord> pageList(Page<ShippingRecord> page, ShippingRecord record) {
        QueryWrapper<ShippingRecord> queryWrapper = new QueryWrapper<>();
        
        // 构建查询条件
        if (record != null) {
            // 发货单号
            if (StringUtils.isNotBlank(record.getShippingNo())) {
                queryWrapper.eq("shipping_no", record.getShippingNo());
            }
            
            // 快递单号
            if (StringUtils.isNotBlank(record.getTrackingNo())) {
                queryWrapper.eq("tracking_no", record.getTrackingNo());
            }
            
            // 收件人姓名
            if (StringUtils.isNotBlank(record.getReceiverName())) {
                queryWrapper.like("receiver_name", record.getReceiverName());
            }
            
            // 收件人电话
            if (StringUtils.isNotBlank(record.getReceiverPhone())) {
                queryWrapper.eq("receiver_phone", record.getReceiverPhone());
            }
            
            // 发货状态
            if (record.getStatus() != null) {
                queryWrapper.eq("status", record.getStatus());
            }
        }
        
        // 默认按创建时间倒序排序
        queryWrapper.orderByDesc("create_time");
        
        return this.page(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean ship(ShippingRecord record) {
        // 校验必填项
        if (record == null || StringUtils.isBlank(record.getTrackingNo()) || 
            StringUtils.isBlank(record.getExpressCompany()) || 
            StringUtils.isBlank(record.getReceiverName()) || 
            StringUtils.isBlank(record.getReceiverPhone()) ||
            StringUtils.isBlank(record.getReceiverAddress())) {
            log.error("发货信息不完整");
            return false;
        }
        
        // 设置默认值
        if (record.getStatus() == null) {
            record.setStatus(1); // 已发货
        }
        
        if (record.getShippingType() == null) {
            record.setShippingType(1); // 默认系统发货
        }
        
        if (record.getShippingFee() == null) {
            record.setShippingFee(BigDecimal.ZERO); // 默认0费用
        }
        
        // 生成发货单号
        if (StringUtils.isBlank(record.getShippingNo())) {
            record.setShippingNo("MP" + System.currentTimeMillis());
        }
        
        // 设置时间
        Date now = new Date();
        record.setCreateTime(now);
        record.setUpdateTime(now);
        
        // 保存记录
        boolean saved = this.save(record);
        
        if (saved) {
            // 订阅物流推送
            try {
                expressTrackingService.subscribeExpressTrack(
                    record.getTrackingNo(), 
                    record.getExpressCompany(), 
                    record.getReceiverPhone(),
                    record.getShippingNo()
                );
            } catch (Exception e) {
                log.error("订阅物流推送失败", e);
                // 订阅失败不影响发货流程
            }
        }
        
        return saved;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLogistics(String id, String expressCompany, String trackingNo) {
        if (StringUtils.isBlank(id) || StringUtils.isBlank(expressCompany) || StringUtils.isBlank(trackingNo)) {
            log.error("更新物流信息参数不完整");
            return false;
        }
        
        // 查询记录
        ShippingRecord record = this.getById(id);
        if (record == null) {
            log.error("找不到ID为{}的发货记录", id);
            return false;
        }
        
        // 更新物流信息
        record.setExpressCompany(expressCompany);
        record.setTrackingNo(trackingNo);
        record.setUpdateTime(new Date());
        
        // 尝试获取最新的物流信息
        try {
            ExpressTrackDTO trackDTO = expressTrackingService.queryTrack(trackingNo, 
                ExpressCompanyEnum.getCodeByName(expressCompany));
            
            if (trackDTO != null && trackDTO.getData() != null && !trackDTO.getData().isEmpty()) {
                // 更新物流状态和最新信息
                ExpressTrackDTO.ExpressTrackItemDTO latestItem = trackDTO.getData().get(0);
                record.setLatestLogistics(latestItem.getContext());
                
                // 根据快递100的状态更新我们系统的状态
                if ("3".equals(trackDTO.getStatus())) { // 签收
                    record.setStatus(2); // 已签收
                } else if ("2".equals(trackDTO.getStatus())) { // 疑难
                    record.setStatus(3); // 异常
                } else {
                    record.setStatus(1); // 已发货/运输中
                }
            }
            
            // 订阅物流推送
            expressTrackingService.subscribeExpressTrack(
                trackingNo, 
                expressCompany, 
                record.getReceiverPhone(),
                record.getShippingNo()
            );
        } catch (Exception e) {
            log.error("查询或订阅物流信息失败", e);
            // 即使查询物流失败，也不影响基本信息的更新
        }
        
        return this.updateById(record);
    }

    @Override
    public ExpressTrackDTO queryLogisticsTrack(String id) {
        // 查询记录
        ShippingRecord record = this.getById(id);
        if (record == null || StringUtils.isBlank(record.getTrackingNo()) || StringUtils.isBlank(record.getExpressCompany())) {
            log.error("找不到发货记录或物流信息不完整，ID: {}", id);
            return null;
        }
        
        // 查询物流轨迹
        String expressCode = ExpressCompanyEnum.getCodeByName(record.getExpressCompany());
        ExpressTrackDTO trackDTO = expressTrackingService.queryTrack(record.getTrackingNo(), expressCode);
        
        // 如果成功获取到物流轨迹，更新记录中的状态和最新物流信息
        if (trackDTO != null && trackDTO.getData() != null && !trackDTO.getData().isEmpty()) {
            try {
                ExpressTrackDTO.ExpressTrackItemDTO latestItem = trackDTO.getData().get(0);
                
                ShippingRecord updateRecord = new ShippingRecord();
                updateRecord.setId(record.getId());
                updateRecord.setLatestLogistics(latestItem.getContext());
                updateRecord.setUpdateTime(new Date());
                
                // 根据物流状态更新发货状态
                if ("3".equals(trackDTO.getStatus())) { // 签收
                    updateRecord.setStatus(2); // 已签收
                } else if ("2".equals(trackDTO.getStatus())) { // 疑难
                    updateRecord.setStatus(3); // 异常
                }
                
                this.updateById(updateRecord);
            } catch (Exception e) {
                log.error("更新物流状态失败", e);
                // 更新失败不影响查询结果的返回
            }
        }
        
        return trackDTO;
    }
    
    @Override
    public Map<String, ExpressTrackDTO> batchQueryLogisticsTrack(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return new HashMap<>();
        }
        
        // 查询所有记录
        List<ShippingRecord> records = this.listByIds(ids);
        if (records.isEmpty()) {
            return new HashMap<>();
        }
        
        // 构建批量查询参数
        List<Map<String, String>> expressInfoList = records.stream()
            .filter(record -> StringUtils.isNotBlank(record.getTrackingNo()) && StringUtils.isNotBlank(record.getExpressCompany()))
            .map(record -> {
                Map<String, String> expressInfo = new HashMap<>();
                expressInfo.put("expressNo", record.getTrackingNo());
                expressInfo.put("expressCompany", ExpressCompanyEnum.getCodeByName(record.getExpressCompany()));
                expressInfo.put("recordId", record.getId());
                return expressInfo;
            })
            .collect(Collectors.toList());
        
        // 批量查询物流
        Map<String, ExpressTrackDTO> trackResult = expressTrackingService.batchQueryTrack(expressInfoList);
        
        // 更新记录状态
        if (!trackResult.isEmpty()) {
            List<ShippingRecord> updateRecords = new ArrayList<>();
            
            for (ShippingRecord record : records) {
                ExpressTrackDTO trackDTO = trackResult.get(record.getTrackingNo());
                if (trackDTO != null && trackDTO.getData() != null && !trackDTO.getData().isEmpty()) {
                    ExpressTrackDTO.ExpressTrackItemDTO latestItem = trackDTO.getData().get(0);
                    
                    ShippingRecord updateRecord = new ShippingRecord();
                    updateRecord.setId(record.getId());
                    updateRecord.setLatestLogistics(latestItem.getContext());
                    updateRecord.setUpdateTime(new Date());
                    
                    // 根据物流状态更新发货状态
                    if ("3".equals(trackDTO.getStatus())) { // 签收
                        updateRecord.setStatus(2); // 已签收
                    } else if ("2".equals(trackDTO.getStatus())) { // 疑难
                        updateRecord.setStatus(3); // 异常
                    }
                    
                    updateRecords.add(updateRecord);
                }
            }
            
            // 批量更新
            if (!updateRecords.isEmpty()) {
                this.updateBatchById(updateRecords);
            }
        }
        
        // 返回物流轨迹结果，按记录ID为key
        Map<String, ExpressTrackDTO> result = new HashMap<>();
        for (ShippingRecord record : records) {
            ExpressTrackDTO trackDTO = trackResult.get(record.getTrackingNo());
            if (trackDTO != null) {
                result.put(record.getId(), trackDTO);
            }
        }
        
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSubscribe(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        // 查询所有记录
        List<ShippingRecord> records = this.listByIds(ids);
        if (records.isEmpty()) {
            return false;
        }
        
        boolean allSuccess = true;
        for (ShippingRecord record : records) {
            if (StringUtils.isBlank(record.getTrackingNo()) || StringUtils.isBlank(record.getExpressCompany()) || 
                StringUtils.isBlank(record.getReceiverPhone())) {
                log.warn("记录信息不完整，无法订阅，ID: {}", record.getId());
                allSuccess = false;
                continue;
            }
            
            try {
                boolean success = expressTrackingService.subscribeExpressTrack(
                    record.getTrackingNo(), 
                    record.getExpressCompany(), 
                    record.getReceiverPhone(),
                    record.getShippingNo()
                );
                
                if (!success) {
                    allSuccess = false;
                }
            } catch (Exception e) {
                log.error("订阅物流异常，ID: {}, 异常: {}", record.getId(), e.getMessage());
                allSuccess = false;
            }
        }
        
        return allSuccess;
    }
} 