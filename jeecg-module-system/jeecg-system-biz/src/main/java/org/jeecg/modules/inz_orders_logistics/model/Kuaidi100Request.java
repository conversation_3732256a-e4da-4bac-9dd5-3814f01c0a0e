package org.jeecg.modules.inz_orders_logistics.model;

import lombok.Data;

/**
 * 快递100请求参数
 */
@Data
public class Kuaidi100Request {

    /**
     * 授权key
     */
    private String customer;
    
    /**
     * 加密签名字符串
     */
    private String sign;
    
    /**
     * 查询参数
     */
    private String param;
    
    /**
     * 请求类型，订阅时需要
     */
    private String schema;
    
    /**
     * 查询参数类
     */
    @Data
    public static class Param {
        /**
         * 查询的快递公司编码
         */
        private String com;
        
        /**
         * 查询的快递单号
         */
        private String num;
        
        /**
         * 收件人或寄件人的手机号
         */
        private String phone;
        
        /**
         * 出发地城市
         */
        private String from;
        
        /**
         * 目的地城市
         */
        private String to;
        
        /**
         * 回调接口的地址
         */
        private String callbackurl;
        
        /**
         * 附加参数，订阅时可传递自定义参数
         */
        private String salt;
        
        /**
         * 添加订阅接口的返回结果，true表示添加成功
         */
        private String result;
        
        /**
         * 回调接口出现异常时的重试次数
         */
        private Integer callbackIntercept;
        
        /**
         * 签收状态回调选项：0-签收时不需要回调，1-签收时需要回调
         */
        private Integer finishCallBack;
        
        /**
         * 是否开启智能识别单号，默认false
         */
        private Boolean autoCom;
        
        /**
         * 返回数据格式
         */
        private String resultv2;
        
        /**
         * 订阅授权密钥
         */
        private String key;
    }
    
    /**
     * 订阅回调数据
     */
    @Data
    public static class CallbackData {
        /**
         * 快递单号
         */
        private String nu;
        
        /**
         * 快递公司编码
         */
        private String com;
        
        /**
         * 快递状态
         */
        private String status;
        
        /**
         * 附加参数
         */
        private String salt;
        
        /**
         * 轨迹信息
         */
        private ExpressTrackDTO data;
    }
} 