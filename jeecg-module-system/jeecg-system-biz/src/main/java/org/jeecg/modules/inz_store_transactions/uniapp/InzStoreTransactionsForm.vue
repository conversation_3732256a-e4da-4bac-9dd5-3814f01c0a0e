<template>
    <view>
        <!--标题和返回-->
		<cu-custom :bgColor="NavBarColor" isBack :backRouterName="backRouteName">
			<block slot="backText">返回</block>
			<block slot="content">inz_store_transaction</block>
		</cu-custom>
		 <!--表单区域-->
		<view>
			<form>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">店鋪ID：</text></view>
                  <input  placeholder="请输入店鋪ID" v-model="model.storeId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">流水号：</text></view>
                  <input  placeholder="请输入流水号" v-model="model.transactionNo"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">関聯訂単ID：</text></view>
                  <input  placeholder="请输入関聯訂単ID" v-model="model.orderId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">関聯訂単号：</text></view>
                  <input  placeholder="请输入関聯訂単号" v-model="model.orderNo"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">交易类型：1-訂単收入，2-退款，3-提現，4-人工調整，5-平台傭金：</text></view>
                  <input type="number" placeholder="请输入交易类型：1-訂単收入，2-退款，3-提現，4-人工調整，5-平台傭金" v-model="model.type"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">交易金額：</text></view>
                  <input type="number" placeholder="请输入交易金額" v-model="model.amount"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">交易前余額：</text></view>
                  <input type="number" placeholder="请输入交易前余額" v-model="model.beforeBalance"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">交易後余額：</text></view>
                  <input type="number" placeholder="请输入交易後余額" v-model="model.afterBalance"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">交易狀態：1-待処理，2-已完成，3-失敗，4-已取消：</text></view>
                  <input type="number" placeholder="请输入交易狀態：1-待処理，2-已完成，3-失敗，4-已取消" v-model="model.status"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">備註：</text></view>
                  <input  placeholder="请输入備註" v-model="model.remark"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">第三方交易ID：</text></view>
                  <input  placeholder="请输入第三方交易ID" v-model="model.externalId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">操作人：</text></view>
                  <input  placeholder="请输入操作人" v-model="model.operator"/>
                </view>
              </view>
              <my-date label="完成时间：" v-model="model.completeTime" placeholder="请输入完成时间"></my-date>
				<view class="padding">
					<button class="cu-btn block bg-blue margin-tb-sm lg" @click="onSubmit">
						<text v-if="loading" class="cuIcon-loading2 cuIconfont-spin"></text>提交
					</button>
				</view>
			</form>
		</view>
    </view>
</template>

<script>
    import myDate from '@/components/my-componets/my-date.vue'

    export default {
        name: "InzStoreTransactionsForm",
        components:{ myDate },
        props:{
          formData:{
              type:Object,
              default:()=>{},
              required:false
          }
        },
        data(){
            return {
				CustomBar: this.CustomBar,
				NavBarColor: this.NavBarColor,
				loading:false,
                model: {},
                backRouteName:'index',
                url: {
                  queryById: "/inz_store_transactions/inzStoreTransactions/queryById",
                  add: "/inz_store_transactions/inzStoreTransactions/add",
                  edit: "/inz_store_transactions/inzStoreTransactions/edit",
                },
            }
        },
        created(){
             this.initFormData();
        },
        methods:{
           initFormData(){
               if(this.formData){
                    let dataId = this.formData.dataId;
                    this.$http.get(this.url.queryById,{params:{id:dataId}}).then((res)=>{
                        if(res.data.success){
                            console.log("表单数据",res);
                            this.model = res.data.result;
                        }
                    })
                }
            },
            onSubmit() {
                let myForm = {...this.model};
                this.loading = true;
                let url = myForm.id?this.url.edit:this.url.add;
				this.$http.post(url,myForm).then(res=>{
				   console.log("res",res)
				   this.loading = false
				   this.$Router.push({name:this.backRouteName})
				}).catch(()=>{
					this.loading = false
				});
            }
        }
    }
</script>
