import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '业务ID',
    align:"center",
    dataIndex: 'businessId'
   },
   {
    title: '审核类型（1：商品上架，2：售后申请）',
    align:"center",
    dataIndex: 'auditType'
   },
   {
    title: '审核状态（0：待审核，1：审核通过，2：审核驳回）',
    align:"center",
    dataIndex: 'status'
   },
   {
    title: '审核意见',
    align:"center",
    dataIndex: 'comments'
   },
   {
    title: '审核人ID',
    align:"center",
    dataIndex: 'auditorId'
   },
   {
    title: '审核时间',
    align:"center",
    dataIndex: 'auditTime'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '业务ID',
    field: 'businessId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入业务ID!'},
          ];
     },
  },
  {
    label: '审核类型（1：商品上架，2：售后申请）',
    field: 'auditType',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入审核类型（1：商品上架，2：售后申请）!'},
          ];
     },
  },
  {
    label: '审核状态（0：待审核，1：审核通过，2：审核驳回）',
    field: 'status',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入审核状态（0：待审核，1：审核通过，2：审核驳回）!'},
          ];
     },
  },
  {
    label: '审核意见',
    field: 'comments',
    component: 'InputTextArea',
  },
  {
    label: '审核人ID',
    field: 'auditorId',
    component: 'Input',
  },
  {
    label: '审核时间',
    field: 'auditTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  businessId: {title: '业务ID',order: 0,view: 'text', type: 'string',},
  auditType: {title: '审核类型（1：商品上架，2：售后申请）',order: 1,view: 'number', type: 'number',},
  status: {title: '审核状态（0：待审核，1：审核通过，2：审核驳回）',order: 2,view: 'number', type: 'number',},
  comments: {title: '审核意见',order: 3,view: 'textarea', type: 'string',},
  auditorId: {title: '审核人ID',order: 4,view: 'text', type: 'string',},
  auditTime: {title: '审核时间',order: 5,view: 'datetime', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}