package org.jeecg.modules.system.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * @Description: 文件上传安全工具类
 * @Author: jeecg-boot
 * @Date: 2025-01-24
 * @Version: V1.0
 */
@Slf4j
public class FileUploadSecurityUtil {

    // 危险文件扩展名黑名单
    private static final List<String> DANGEROUS_EXTENSIONS = Arrays.asList(
        "jsp", "jspx", "php", "asp", "aspx", "exe", "bat", "cmd", "com", "pif", "scr",
        "vbs", "js", "jar", "war", "ear", "class", "sh", "py", "rb", "pl", "cgi",
        "htaccess", "htpasswd", "ini", "conf", "config", "sql"
    );
    
    // 危险文件内容特征
    private static final List<String> DANGEROUS_CONTENT_PATTERNS = Arrays.asList(
        "<%", "%>", "<?php", "?>", "<script", "</script>", "javascript:",
        "eval(", "exec(", "system(", "shell_exec(", "passthru(",
        "base64_decode(", "gzinflate(", "str_rot13(", "assert("
    );
    
    // 文件名危险字符
    private static final Pattern DANGEROUS_FILENAME_PATTERN = 
        Pattern.compile("[<>:\"/\\\\|?*\\x00-\\x1f]");
    
    // 最大文件大小（字节）
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    
    /**
     * 综合文件上传安全检查
     * @param file 上传的文件
     * @throws SecurityException 如果文件不安全
     */
    public static void checkFileUploadSecurity(MultipartFile file) throws SecurityException {
        if (file == null || file.isEmpty()) {
            throw new SecurityException("文件不能为空");
        }
        
        // 1. 检查文件大小
        checkFileSize(file);
        
        // 2. 检查文件名安全性
        checkFileName(file.getOriginalFilename());
        
        // 3. 检查文件扩展名
        checkFileExtension(file.getOriginalFilename());
        
        // 4. 检查文件内容
        checkFileContent(file);
        
        // 5. 检查MIME类型
        checkMimeType(file);
        
        log.info("文件上传安全检查通过：{}", file.getOriginalFilename());
    }
    
    /**
     * 检查文件大小
     */
    private static void checkFileSize(MultipartFile file) {
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new SecurityException("文件大小超过限制：" + (MAX_FILE_SIZE / 1024 / 1024) + "MB");
        }
    }
    
    /**
     * 检查文件名安全性
     */
    private static void checkFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new SecurityException("文件名不能为空");
        }
        
        // 检查文件名长度
        if (fileName.length() > 255) {
            throw new SecurityException("文件名过长");
        }
        
        // 检查危险字符
        if (DANGEROUS_FILENAME_PATTERN.matcher(fileName).find()) {
            throw new SecurityException("文件名包含非法字符");
        }
        
        // 检查路径遍历
        if (fileName.contains("../") || fileName.contains("..\\")) {
            throw new SecurityException("文件名包含路径遍历字符");
        }
        
        // 检查隐藏文件
        if (fileName.startsWith(".")) {
            throw new SecurityException("不允许上传隐藏文件");
        }
    }
    
    /**
     * 检查文件扩展名
     */
    private static void checkFileExtension(String fileName) {
        if (fileName == null) {
            return;
        }
        
        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex == -1) {
            throw new SecurityException("文件必须有扩展名");
        }
        
        String extension = fileName.substring(lastDotIndex + 1).toLowerCase();
        
        // 检查是否为危险扩展名
        if (DANGEROUS_EXTENSIONS.contains(extension)) {
            throw new SecurityException("不允许上传此类型文件：" + extension);
        }
        
        // 检查双扩展名
        String nameWithoutExt = fileName.substring(0, lastDotIndex);
        if (nameWithoutExt.contains(".")) {
            String secondExt = nameWithoutExt.substring(nameWithoutExt.lastIndexOf(".") + 1).toLowerCase();
            if (DANGEROUS_EXTENSIONS.contains(secondExt)) {
                throw new SecurityException("检测到双扩展名攻击：" + secondExt + "." + extension);
            }
        }
    }
    
    /**
     * 检查文件内容
     */
    private static void checkFileContent(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            // 读取文件前1KB内容进行检查
            byte[] buffer = new byte[1024];
            int bytesRead = inputStream.read(buffer);
            
            if (bytesRead > 0) {
                String content = new String(buffer, 0, bytesRead).toLowerCase();
                
                // 检查危险内容模式
                for (String pattern : DANGEROUS_CONTENT_PATTERNS) {
                    if (content.contains(pattern.toLowerCase())) {
                        throw new SecurityException("文件内容包含危险代码：" + pattern);
                    }
                }
                
                // 检查文件头是否与扩展名匹配
                checkFileHeader(buffer, file.getOriginalFilename());
            }
        } catch (IOException e) {
            throw new SecurityException("读取文件内容失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查文件头
     */
    private static void checkFileHeader(byte[] fileHeader, String fileName) {
        if (fileName == null || fileHeader.length < 4) {
            return;
        }
        
        String extension = getFileExtension(fileName).toLowerCase();
        String hexHeader = bytesToHex(fileHeader, 8); // 取前8字节
        
        // 常见文件类型的文件头检查
        switch (extension) {
            case "jpg":
            case "jpeg":
                if (!hexHeader.startsWith("ffd8ff")) {
                    throw new SecurityException("JPEG文件头不匹配");
                }
                break;
            case "png":
                if (!hexHeader.startsWith("89504e47")) {
                    throw new SecurityException("PNG文件头不匹配");
                }
                break;
            case "gif":
                if (!hexHeader.startsWith("47494638")) {
                    throw new SecurityException("GIF文件头不匹配");
                }
                break;
            case "pdf":
                if (!hexHeader.startsWith("25504446")) {
                    throw new SecurityException("PDF文件头不匹配");
                }
                break;
            case "zip":
                if (!hexHeader.startsWith("504b0304")) {
                    throw new SecurityException("ZIP文件头不匹配");
                }
                break;
        }
        
        // 检查是否为伪装的可执行文件
        if (hexHeader.startsWith("4d5a")) { // PE文件头
            throw new SecurityException("检测到可执行文件");
        }
        if (hexHeader.startsWith("7f454c46")) { // ELF文件头
            throw new SecurityException("检测到Linux可执行文件");
        }
    }
    
    /**
     * 检查MIME类型
     */
    private static void checkMimeType(MultipartFile file) {
        String contentType = file.getContentType();
        if (contentType == null) {
            throw new SecurityException("无法确定文件MIME类型");
        }
        
        // 危险的MIME类型
        List<String> dangerousMimeTypes = Arrays.asList(
            "application/x-msdownload",
            "application/x-executable",
            "application/x-dosexec",
            "application/x-winexe",
            "text/x-php",
            "application/x-httpd-php",
            "text/x-jsp"
        );
        
        if (dangerousMimeTypes.contains(contentType.toLowerCase())) {
            throw new SecurityException("危险的MIME类型：" + contentType);
        }
    }
    
    /**
     * 生成安全的文件名
     */
    public static String generateSafeFileName(String originalFileName) {
        if (originalFileName == null) {
            return "file_" + System.currentTimeMillis();
        }
        
        // 移除路径信息
        String fileName = originalFileName.replaceAll(".*[/\\\\]", "");
        
        // 移除危险字符
        fileName = DANGEROUS_FILENAME_PATTERN.matcher(fileName).replaceAll("_");
        
        // 限制长度
        if (fileName.length() > 100) {
            String extension = getFileExtension(fileName);
            String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf("."));
            fileName = nameWithoutExt.substring(0, 100 - extension.length() - 1) + "." + extension;
        }
        
        // 添加时间戳避免重名
        String extension = getFileExtension(fileName);
        String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf("."));
        return nameWithoutExt + "_" + System.currentTimeMillis() + "." + extension;
    }
    
    /**
     * 获取文件扩展名
     */
    private static String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }
    
    /**
     * 字节数组转十六进制字符串
     */
    private static String bytesToHex(byte[] bytes, int length) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < Math.min(bytes.length, length); i++) {
            result.append(String.format("%02x", bytes[i]));
        }
        return result.toString();
    }
}
