<template>
    <view>
        <!--标题和返回-->
		<cu-custom :bgColor="NavBarColor" isBack :backRouterName="backRouteName">
			<block slot="backText">返回</block>
			<block slot="content">公告表</block>
		</cu-custom>
		 <!--表单区域-->
		<view>
			<form>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">公告标题：</text></view>
                  <input  placeholder="请输入公告标题" v-model="model.title"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">公告内容：</text></view>
                  <input  placeholder="请输入公告内容" v-model="model.content"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">公告类型 1-系统公告 2-活动公告 3-维护公告 4-其他：</text></view>
                  <input type="number" placeholder="请输入公告类型 1-系统公告 2-活动公告 3-维护公告 4-其他" v-model="model.type"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">发布状态 0-草稿 1-已发布 2-已下线：</text></view>
                  <input type="number" placeholder="请输入发布状态 0-草稿 1-已发布 2-已下线" v-model="model.status"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">是否置顶 0-否 1-是：</text></view>
                  <input type="number" placeholder="请输入是否置顶 0-否 1-是" v-model="model.isTop"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">优先级 数字越大优先级越高：</text></view>
                  <input type="number" placeholder="请输入优先级 数字越大优先级越高" v-model="model.priority"/>
                </view>
              </view>
              <my-date label="发布时间：" v-model="model.publishTime" placeholder="请输入发布时间"></my-date>
              <my-date label="生效时间：" v-model="model.startTime" placeholder="请输入生效时间"></my-date>
              <my-date label="失效时间：" v-model="model.endTime" placeholder="请输入失效时间"></my-date>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">阅读次数：</text></view>
                  <input type="number" placeholder="请输入阅读次数" v-model="model.readCount"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">封面图片：</text></view>
                  <input  placeholder="请输入封面图片" v-model="model.coverImage"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">是否发送通知 0-否 1-是：</text></view>
                  <input type="number" placeholder="请输入是否发送通知 0-否 1-是" v-model="model.sendNotification"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">目标用户类型 0-全部用户 1-普通用户 2-店主用户 3-VIP用户：</text></view>
                  <input type="number" placeholder="请输入目标用户类型 0-全部用户 1-普通用户 2-店主用户 3-VIP用户" v-model="model.targetUserType"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">备注：</text></view>
                  <input  placeholder="请输入备注" v-model="model.remark"/>
                </view>
              </view>
				<view class="padding">
					<button class="cu-btn block bg-blue margin-tb-sm lg" @click="onSubmit">
						<text v-if="loading" class="cuIcon-loading2 cuIconfont-spin"></text>提交
					</button>
				</view>
			</form>
		</view>
    </view>
</template>

<script>
    import myDate from '@/components/my-componets/my-date.vue'

    export default {
        name: "InzAnnouncementForm",
        components:{ myDate },
        props:{
          formData:{
              type:Object,
              default:()=>{},
              required:false
          }
        },
        data(){
            return {
				CustomBar: this.CustomBar,
				NavBarColor: this.NavBarColor,
				loading:false,
                model: {},
                backRouteName:'index',
                url: {
                  queryById: "/inz_announcements/inzAnnouncement/queryById",
                  add: "/inz_announcements/inzAnnouncement/add",
                  edit: "/inz_announcements/inzAnnouncement/edit",
                },
            }
        },
        created(){
             this.initFormData();
        },
        methods:{
           initFormData(){
               if(this.formData){
                    let dataId = this.formData.dataId;
                    this.$http.get(this.url.queryById,{params:{id:dataId}}).then((res)=>{
                        if(res.data.success){
                            console.log("表单数据",res);
                            this.model = res.data.result;
                        }
                    })
                }
            },
            onSubmit() {
                let myForm = {...this.model};
                this.loading = true;
                let url = myForm.id?this.url.edit:this.url.add;
				this.$http.post(url,myForm).then(res=>{
				   console.log("res",res)
				   this.loading = false
				   this.$Router.push({name:this.backRouteName})
				}).catch(()=>{
					this.loading = false
				});
            }
        }
    }
</script>
