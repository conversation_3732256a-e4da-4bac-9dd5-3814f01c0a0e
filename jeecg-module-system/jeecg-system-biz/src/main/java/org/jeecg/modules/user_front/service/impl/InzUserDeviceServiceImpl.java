package org.jeecg.modules.user_front.service.impl;

import org.jeecg.modules.user_front.entity.InzUserDevice;
import org.jeecg.modules.user_front.mapper.InzUserDeviceMapper;
import org.jeecg.modules.user_front.service.IInzUserDeviceService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 用户设备管理
 * @Author: jeecg-boot
 * @Date:   2025-03-28
 * @Version: V1.0
 */
@Service
public class InzUserDeviceServiceImpl extends ServiceImpl<InzUserDeviceMapper, InzUserDevice> implements IInzUserDeviceService {
	
	@Autowired
	private InzUserDeviceMapper inzUserDeviceMapper;
	
	@Override
	public List<InzUserDevice> selectByMainId(String mainId) {
		return inzUserDeviceMapper.selectByMainId(mainId);
	}
}
