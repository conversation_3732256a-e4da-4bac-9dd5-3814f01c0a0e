package org.jeecg.modules.inz_announcements.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.inz_announcements.entity.InzAnnouncement;
import org.jeecg.modules.inz_announcements.mapper.InzAnnouncementMapper;
import org.jeecg.modules.inz_announcements.service.IInzAnnouncementService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;

/**
 * @Description: 平台公告管理
 * @Author: jeecg-boot
 * @Date: 2025-01-24
 * @Version: V1.0
 */
@Service
@Slf4j
public class InzAnnouncementServiceImpl extends ServiceImpl<InzAnnouncementMapper, InzAnnouncement> implements IInzAnnouncementService {

    @Override
    public boolean publishAnnouncement(String id) {
        try {
            LambdaUpdateWrapper<InzAnnouncement> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(InzAnnouncement::getId, id)
                    .set(InzAnnouncement::getStatus, 1) // 设置为已发布
                    .set(InzAnnouncement::getPublishTime, new Date()); // 设置发布时间

            return this.update(updateWrapper);
        } catch (Exception e) {
            log.error("发布公告失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean offlineAnnouncement(String id) {
        try {
            LambdaUpdateWrapper<InzAnnouncement> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(InzAnnouncement::getId, id)
                    .set(InzAnnouncement::getStatus, 2); // 设置为已下线

            return this.update(updateWrapper);
        } catch (Exception e) {
            log.error("下线公告失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean batchPublish(List<String> ids) {
        try {
            LambdaUpdateWrapper<InzAnnouncement> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(InzAnnouncement::getId, ids)
                    .set(InzAnnouncement::getStatus, 1) // 设置为已发布
                    .set(InzAnnouncement::getPublishTime, new Date()); // 设置发布时间

            return this.update(updateWrapper);
        } catch (Exception e) {
            log.error("批量发布公告失败，IDs: {}, 错误: {}", ids, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean batchOffline(List<String> ids) {
        try {
            LambdaUpdateWrapper<InzAnnouncement> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(InzAnnouncement::getId, ids)
                    .set(InzAnnouncement::getStatus, 2); // 设置为已下线

            return this.update(updateWrapper);
        } catch (Exception e) {
            log.error("批量下线公告失败，IDs: {}, 错误: {}", ids, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean toggleTop(String id, Integer isTop) {
        try {
            LambdaUpdateWrapper<InzAnnouncement> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(InzAnnouncement::getId, id)
                    .set(InzAnnouncement::getIsTop, isTop);

            return this.update(updateWrapper);
        } catch (Exception e) {
            log.error("置顶操作失败，ID: {}, isTop: {}, 错误: {}", id, isTop, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public IPage<InzAnnouncement> getValidAnnouncements(Page<InzAnnouncement> page, Integer type, Integer targetUserType) {
        LambdaQueryWrapper<InzAnnouncement> queryWrapper = new LambdaQueryWrapper<>();

        // 只查询已发布的公告
        queryWrapper.eq(InzAnnouncement::getStatus, 1);

        // 在有效期内的公告
        Date now = new Date();
        queryWrapper.and(wrapper -> wrapper
                .isNull(InzAnnouncement::getStartTime)
                .or()
                .le(InzAnnouncement::getStartTime, now))
                .and(wrapper -> wrapper
                        .isNull(InzAnnouncement::getEndTime)
                        .or()
                        .ge(InzAnnouncement::getEndTime, now));

        // 按类型筛选
        if (type != null) {
            queryWrapper.eq(InzAnnouncement::getType, type);
        }

        // 按目标用户类型筛选
        if (targetUserType != null) {
            queryWrapper.and(wrapper -> wrapper
                    .eq(InzAnnouncement::getTargetUserType, 0) // 全部用户
                    .or()
                    .eq(InzAnnouncement::getTargetUserType, targetUserType)); // 指定用户类型
        } else {
            queryWrapper.eq(InzAnnouncement::getTargetUserType, 0); // 只显示全部用户的公告
        }

        // 排序：置顶优先，然后按优先级降序，最后按发布时间降序
        queryWrapper.orderByDesc(InzAnnouncement::getIsTop)
                .orderByDesc(InzAnnouncement::getPriority)
                .orderByDesc(InzAnnouncement::getPublishTime);

        return this.page(page, queryWrapper);
    }

    @Override
    public List<InzAnnouncement> getTopAnnouncements(Integer targetUserType) {
        LambdaQueryWrapper<InzAnnouncement> queryWrapper = new LambdaQueryWrapper<>();

        // 只查询已发布且置顶的公告
        queryWrapper.eq(InzAnnouncement::getStatus, 1)
                .eq(InzAnnouncement::getIsTop, 1);

        // 在有效期内的公告
        Date now = new Date();
        queryWrapper.and(wrapper -> wrapper
                .isNull(InzAnnouncement::getStartTime)
                .or()
                .le(InzAnnouncement::getStartTime, now))
                .and(wrapper -> wrapper
                        .isNull(InzAnnouncement::getEndTime)
                        .or()
                        .ge(InzAnnouncement::getEndTime, now));

        // 按目标用户类型筛选
        if (targetUserType != null) {
            queryWrapper.and(wrapper -> wrapper
                    .eq(InzAnnouncement::getTargetUserType, 0) // 全部用户
                    .or()
                    .eq(InzAnnouncement::getTargetUserType, targetUserType)); // 指定用户类型
        } else {
            queryWrapper.eq(InzAnnouncement::getTargetUserType, 0); // 只显示全部用户的公告
        }

        // 按优先级降序，发布时间降序
        queryWrapper.orderByDesc(InzAnnouncement::getPriority)
                .orderByDesc(InzAnnouncement::getPublishTime);

        return this.list(queryWrapper);
    }

    @Override
    public List<InzAnnouncement> getLatestAnnouncements(Integer limit, Integer targetUserType) {
        LambdaQueryWrapper<InzAnnouncement> queryWrapper = new LambdaQueryWrapper<>();

        // 只查询已发布的公告
        queryWrapper.eq(InzAnnouncement::getStatus, 1);

        // 在有效期内的公告
        Date now = new Date();
        queryWrapper.and(wrapper -> wrapper
                .isNull(InzAnnouncement::getStartTime)
                .or()
                .le(InzAnnouncement::getStartTime, now))
                .and(wrapper -> wrapper
                        .isNull(InzAnnouncement::getEndTime)
                        .or()
                        .ge(InzAnnouncement::getEndTime, now));

        // 按目标用户类型筛选
        if (targetUserType != null) {
            queryWrapper.and(wrapper -> wrapper
                    .eq(InzAnnouncement::getTargetUserType, 0) // 全部用户
                    .or()
                    .eq(InzAnnouncement::getTargetUserType, targetUserType)); // 指定用户类型
        } else {
            queryWrapper.eq(InzAnnouncement::getTargetUserType, 0); // 只显示全部用户的公告
        }

        // 按发布时间降序
        queryWrapper.orderByDesc(InzAnnouncement::getPublishTime);

        // 限制数量
        queryWrapper.last("LIMIT " + limit);

        return this.list(queryWrapper);
    }

    @Override
    public InzAnnouncement getAnnouncementDetail(String id) {
        try {
            log.info("开始获取公告详情，ID: {}", id);

            // 获取公告详情
            InzAnnouncement announcement = this.getById(id);
            if (announcement == null) {
                log.warn("公告不存在，ID: {}", id);
                return null;
            }

            log.info("找到公告，标题: {}, 状态: {}", announcement.getTitle(), announcement.getStatus());

            if (announcement.getStatus() != 1) {
                log.warn("公告未发布，ID: {}, 状态: {}", id, announcement.getStatus());
                return null; // 公告未发布
            }

            // 检查是否在有效期内
            Date now = new Date();
            if (announcement.getStartTime() != null && announcement.getStartTime().after(now)) {
                log.warn("公告还未生效，ID: {}, 生效时间: {}", id, announcement.getStartTime());
                return null; // 还未生效
            }
            if (announcement.getEndTime() != null && announcement.getEndTime().before(now)) {
                log.warn("公告已过期，ID: {}, 失效时间: {}", id, announcement.getEndTime());
                return null; // 已过期
            }

            log.info("公告有效，开始增加阅读次数");

            // 增加阅读次数
            LambdaUpdateWrapper<InzAnnouncement> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(InzAnnouncement::getId, id)
                    .setSql("read_count = read_count + 1");
            boolean updateResult = this.update(updateWrapper);

            log.info("阅读次数更新结果: {}", updateResult);

            // 更新返回对象的阅读次数
            if (announcement.getReadCount() == null) {
                announcement.setReadCount(1);
            } else {
                announcement.setReadCount(announcement.getReadCount() + 1);
            }

            log.info("成功获取公告详情，标题: {}, 当前阅读次数: {}", announcement.getTitle(), announcement.getReadCount());
            return announcement;
        } catch (Exception e) {
            log.error("获取公告详情失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<Object> countByType() {
        try {
            // 统计每种类型的数量
            Map<Integer, Long> typeCountMap = new HashMap<>();
            for (int i = 1; i <= 4; i++) {
                LambdaQueryWrapper<InzAnnouncement> countWrapper = new LambdaQueryWrapper<>();
                countWrapper.eq(InzAnnouncement::getStatus, 1)
                        .eq(InzAnnouncement::getType, i);
                long count = this.count(countWrapper);
                typeCountMap.put(i, count);
            }

            // 转换为前端需要的格式
            List<Object> typeCountList = new ArrayList<>();
            for (Map.Entry<Integer, Long> entry : typeCountMap.entrySet()) {
                Map<String, Object> typeCount = new HashMap<>();
                typeCount.put("type", entry.getKey());
                typeCount.put("typeName", getTypeName(entry.getKey()));
                typeCount.put("count", entry.getValue());
                typeCountList.add(typeCount);
            }

            return typeCountList;
        } catch (Exception e) {
            log.error("统计公告数量失败，错误: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取公告类型名称
     * @param type 类型编码
     * @return 类型名称
     */
    private String getTypeName(Integer type) {
        switch (type) {
            case 1:
                return "系统公告";
            case 2:
                return "活动公告";
            case 3:
                return "维护公告";
            case 4:
                return "其他";
            default:
                return "未知类型";
        }
    }

    @Override
    public Integer incrementReadCount(String id) {
        try {
            // 检查公告是否存在且已发布
            InzAnnouncement announcement = this.getById(id);
            if (announcement == null || announcement.getStatus() != 1) {
                return null; // 公告不存在或未发布
            }

            // 检查是否在有效期内
            Date now = new Date();
            if (announcement.getStartTime() != null && announcement.getStartTime().after(now)) {
                return null; // 还未生效
            }
            if (announcement.getEndTime() != null && announcement.getEndTime().before(now)) {
                return null; // 已过期
            }

            // 增加阅读次数
            LambdaUpdateWrapper<InzAnnouncement> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(InzAnnouncement::getId, id)
                    .setSql("read_count = read_count + 1");
            this.update(updateWrapper);

            // 获取最新的阅读次数
            announcement = this.getById(id);
            return announcement.getReadCount();
        } catch (Exception e) {
            log.error("增加公告阅读次数失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            return null;
        }
    }
}
