package org.jeecg.modules.inz_product_settlement.service;

import org.jeecg.modules.inz_product_settlement.entity.InzProductSettlement;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 商品结算
 * @Author: jeecg-boot
 * @Date: 2025-06-26
 * @Version: V1.0
 */
public interface IInzProductSettlementService extends IService<InzProductSettlement> {

    /**
     * 创建结算记录
     * @param orderId 订单ID
     * @return 是否创建成功
     */
    boolean createSettlementRecord(String orderId);

    /**
     * 批量结算
     * @param settlementIds 结算记录ID列表
     * @param batchName 批次名称
     * @param remark 备注
     * @return 是否结算成功
     */
    boolean batchSettle(List<String> settlementIds, String batchName, String remark);

    /**
     * 计算结算金额
     * @param orderId 订单ID
     * @return 结算计算结果
     */
    Map<String, Object> calculateSettlement(String orderId);

    /**
     * 获取店铺结算统计
     * @param storeId 店铺ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    Map<String, Object> getStoreSettlementStatistics(String storeId, Date startTime, Date endTime);

    /**
     * 获取平台结算统计
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    Map<String, Object> getPlatformSettlementStatistics(Date startTime, Date endTime);

    /**
     * 获取店铺佣金排行榜
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 排行榜数据
     */
    List<Map<String, Object>> getStoreCommissionRanking(Date startTime, Date endTime, Integer limit);

    /**
     * 自动结算检查
     * 检查符合条件的订单并自动创建结算记录
     */
    void autoSettlementCheck();

    /**
     * 获取待结算记录统计
     * @param storeId 店铺ID
     * @return 统计结果
     */
    Map<String, Object> getPendingSettlementStatistics(String storeId);

    /**
     * 获取结算趋势数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 趋势数据
     */
    List<Map<String, Object>> getSettlementTrendData(Date startTime, Date endTime);

    /**
     * 取消结算
     * @param settlementId 结算记录ID
     * @param reason 取消原因
     * @return 是否取消成功
     */
    boolean cancelSettlement(String settlementId, String reason);

    /**
     * 重新计算结算金额
     * @param settlementId 结算记录ID
     * @return 是否重新计算成功
     */
    boolean recalculateSettlement(String settlementId);

    /**
     * 获取结算详情
     * @param settlementId 结算记录ID
     * @return 结算详情
     */
    Map<String, Object> getSettlementDetail(String settlementId);

    /**
     * 导出结算数据
     * @param queryParams 查询参数
     * @return 导出数据
     */
    List<InzProductSettlement> exportSettlementData(Map<String, Object> queryParams);

    /**
     * 获取结算金额分布统计
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分布统计
     */
    List<Map<String, Object>> getSettlementAmountDistribution(Date startTime, Date endTime);

    /**
     * 验证结算数据
     * @param settlementId 结算记录ID
     * @return 验证结果
     */
    Map<String, Object> validateSettlementData(String settlementId);

    /**
     * 生成结算报告
     * @param batchNo 批次号
     * @return 结算报告
     */
    Map<String, Object> generateSettlementReport(String batchNo);
}
