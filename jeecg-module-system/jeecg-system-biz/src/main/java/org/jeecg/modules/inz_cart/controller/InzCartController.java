package org.jeecg.modules.inz_cart.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_cart.entity.InzCart;
import org.jeecg.modules.inz_cart.service.IInzCartService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 购物车表
 * @Author: jeecg-boot
 * @Date:   2025-06-15
 * @Version: V1.0
 */
/*@Api(tags="购物车表")*/
@RestController
@RequestMapping("/inz_cart/inzCart")
@Slf4j
public class InzCartController extends JeecgController<InzCart, IInzCartService> {
	@Autowired
	private IInzCartService inzCartService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzCart
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "购物车表-分页列表查询")
	/*@ApiOperation(value="购物车表-分页列表查询", notes="购物车表-分页列表查询")*/
	@GetMapping(value = "/list")
	public Result<IPage<InzCart>> queryPageList(InzCart inzCart,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<InzCart> queryWrapper = QueryGenerator.initQueryWrapper(inzCart, req.getParameterMap());
		Page<InzCart> page = new Page<InzCart>(pageNo, pageSize);
		IPage<InzCart> pageList = inzCartService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param inzCart
	 * @return
	 */
	/*@AutoLog(value = "购物车表-添加")
	@ApiOperation(value="购物车表-添加", notes="购物车表-添加")*/
	@RequiresPermissions("inz_cart:inz_cart:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InzCart inzCart) {
		inzCartService.save(inzCart);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param inzCart
	 * @return
	 */
	/*@AutoLog(value = "购物车表-编辑")
	@ApiOperation(value="购物车表-编辑", notes="购物车表-编辑")*/
	@RequiresPermissions("inz_cart:inz_cart:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InzCart inzCart) {
		inzCartService.updateById(inzCart);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	/*@AutoLog(value = "购物车表-通过id删除")
	@ApiOperation(value="购物车表-通过id删除", notes="购物车表-通过id删除")*/
	@RequiresPermissions("inz_cart:inz_cart:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzCartService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	/*@AutoLog(value = "购物车表-批量删除")
	@ApiOperation(value="购物车表-批量删除", notes="购物车表-批量删除")*/
	@RequiresPermissions("inz_cart:inz_cart:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzCartService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "购物车表-通过id查询")
	/*@ApiOperation(value="购物车表-通过id查询", notes="购物车表-通过id查询")*/
	@GetMapping(value = "/queryById")
	public Result<InzCart> queryById(@RequestParam(name="id",required=true) String id) {
		InzCart inzCart = inzCartService.getById(id);
		if(inzCart==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzCart);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param inzCart
    */
    @RequiresPermissions("inz_cart:inz_cart:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzCart inzCart) {
        return super.exportXls(request, inzCart, InzCart.class, "购物车表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("inz_cart:inz_cart:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzCart.class);
    }

}
