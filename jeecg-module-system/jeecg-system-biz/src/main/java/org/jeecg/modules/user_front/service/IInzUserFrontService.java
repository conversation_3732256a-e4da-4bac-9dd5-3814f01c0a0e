package org.jeecg.modules.user_front.service;

import org.jeecg.modules.user_front.entity.InzUserDevice;
import org.jeecg.modules.user_front.entity.InzUserFront;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 用户表
 * @Author: jeecg-boot
 * @Date:   2025-03-28
 * @Version: V1.0
 */
public interface IInzUserFrontService extends IService<InzUserFront> {

	/**
	 * 添加一对多
	 *
	 * @param inzUserFront
	 * @param inzUserDeviceList
	 */
	public void saveMain(InzUserFront inzUserFront,List<InzUserDevice> inzUserDeviceList) ;
	
	/**
	 * 修改一对多
	 *
   * @param inzUserFront
   * @param inzUserDeviceList
	 */
	public void updateMain(InzUserFront inzUserFront,List<InzUserDevice> inzUserDeviceList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
	
}
