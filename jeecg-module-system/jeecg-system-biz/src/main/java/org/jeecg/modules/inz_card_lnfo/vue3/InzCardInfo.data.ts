import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '卡片名称',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '卡片类型',
    align:"center",
    dataIndex: 'type'
   },
   {
    title: '卡片等级',
    align:"center",
    dataIndex: 'grade'
   },
   {
    title: '卡片图片',
    align:"center",
    dataIndex: 'image'
   },
   {
    title: '卡片价格',
    align:"center",
    dataIndex: 'price'
   },
   {
    title: '库存数量',
    align:"center",
    dataIndex: 'stock'
   },
   {
    title: '店铺ID',
    align:"center",
    dataIndex: 'storeId'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '卡片名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入卡片名称!'},
          ];
     },
  },
  {
    label: '卡片类型',
    field: 'type',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入卡片类型!'},
          ];
     },
  },
  {
    label: '卡片等级',
    field: 'grade',
    component: 'InputNumber',
  },
  {
    label: '卡片图片',
    field: 'image',
    component: 'Input',
  },
  {
    label: '卡片价格',
    field: 'price',
    component: 'InputNumber',
  },
  {
    label: '库存数量',
    field: 'stock',
    component: 'InputNumber',
  },
  {
    label: '店铺ID',
    field: 'storeId',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '卡片名称',order: 0,view: 'text', type: 'string',},
  type: {title: '卡片类型',order: 1,view: 'text', type: 'string',},
  grade: {title: '卡片等级',order: 2,view: 'number', type: 'number',},
  image: {title: '卡片图片',order: 3,view: 'text', type: 'string',},
  price: {title: '卡片价格',order: 4,view: 'number', type: 'number',},
  stock: {title: '库存数量',order: 5,view: 'number', type: 'number',},
  storeId: {title: '店铺ID',order: 6,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}