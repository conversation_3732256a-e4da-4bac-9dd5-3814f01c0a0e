import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '真实姓名',
    align:"center",
    dataIndex: 'realName'
   },
   {
    title: '年级',
    align:"center",
    dataIndex: 'grade'
   },
   {
    title: '手机号',
    align:"center",
    dataIndex: 'phone'
   },
   {
    title: '密码',
    align:"center",
    dataIndex: 'password'
   },
   {
    title: '地址',
    align:"center",
    dataIndex: 'address'
   },
   {
    title: '公众号昵称',
    align:"center",
    dataIndex: 'oaNickname'
   },
   {
    title: '公众号 OpenId',
    align:"center",
    dataIndex: 'oaOpenid'
   },
   {
    title: '小程序OpenId',
    align:"center",
    dataIndex: 'mpOpenid'
   },
   {
    title: '企业微信 OpenId',
    align:"center",
    dataIndex: 'ewOpenid'
   },
   {
    title: '联合 Id',
    align:"center",
    dataIndex: 'unionId'
   },
   {
    title: '角色',
    align:"center",
    dataIndex: 'role'
   },
   {
    title: '状态 (1正常 0停用)',
    align:"center",
    dataIndex: 'status'
   },
   {
    title: '备注',
    align:"center",
    dataIndex: 'remark'
   },
   {
    title: '头像',
    align:"center",
    dataIndex: 'avatar'
   },
   {
    title: '最后一次进入系统的时间',
    align:"center",
    dataIndex: 'lastUseAt'
   },
   {
    title: '会员时间',
    align:"center",
    dataIndex: 'vipTime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'salt',
    align:"center",
    dataIndex: 'salt'
   },
   {
    title: 'thirdType',
    align:"center",
    dataIndex: 'thirdType'
   },
   {
    title: '会员状态 (0-非会员, 1-普通会员, 2-永久会员)',
    align:"center",
    dataIndex: 'isVip'
   },
   {
    title: '微信OpenID',
    align:"center",
    dataIndex: 'openid'
   },
   {
    title: '微信昵称',
    align:"center",
    dataIndex: 'nickname'
   },
   {
    title: '微信头像URL',
    align:"center",
    dataIndex: 'headimgurl'
   },
   {
    title: '性别 (1男 2女 0未选择)',
    align:"center",
    dataIndex: 'gender'
   },
   {
    title: '玩家号',
    align:"center",
    dataIndex: 'playid'
   },
   {
    title: '用户名',
    align:"center",
    dataIndex: 'username'
   },
   {
    title: '玩家号',
    align:"center",
    dataIndex: 'playId'
   },
   {
    title: '用户积分',
    align:"center",
    dataIndex: 'points'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '真实姓名',
    field: 'realName',
    component: 'Input',
  },
  {
    label: '年级',
    field: 'grade',
    component: 'Input',
  },
  {
    label: '手机号',
    field: 'phone',
    component: 'Input',
  },
  {
    label: '密码',
    field: 'password',
    component: 'Input',
  },
  {
    label: '地址',
    field: 'address',
    component: 'Input',
  },
  {
    label: '公众号昵称',
    field: 'oaNickname',
    component: 'Input',
  },
  {
    label: '公众号 OpenId',
    field: 'oaOpenid',
    component: 'Input',
  },
  {
    label: '小程序OpenId',
    field: 'mpOpenid',
    component: 'Input',
  },
  {
    label: '企业微信 OpenId',
    field: 'ewOpenid',
    component: 'Input',
  },
  {
    label: '联合 Id',
    field: 'unionId',
    component: 'Input',
  },
  {
    label: '角色',
    field: 'role',
    component: 'Input',
  },
  {
    label: '状态 (1正常 0停用)',
    field: 'status',
    component: 'Input',
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
  },
  {
    label: '头像',
    field: 'avatar',
    component: 'Input',
  },
  {
    label: '最后一次进入系统的时间',
    field: 'lastUseAt',
    component: 'Input',
  },
  {
    label: '会员时间',
    field: 'vipTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'salt',
    field: 'salt',
    component: 'Input',
  },
  {
    label: 'thirdType',
    field: 'thirdType',
    component: 'Input',
  },
  {
    label: '会员状态 (0-非会员, 1-普通会员, 2-永久会员)',
    field: 'isVip',
    component: 'InputNumber',
  },
  {
    label: '微信OpenID',
    field: 'openid',
    component: 'Input',
  },
  {
    label: '微信昵称',
    field: 'nickname',
    component: 'Input',
  },
  {
    label: '微信头像URL',
    field: 'headimgurl',
    component: 'Input',
  },
  {
    label: '性别 (1男 2女 0未选择)',
    field: 'gender',
    component: 'InputNumber',
  },
  {
    label: '玩家号',
    field: 'playid',
    component: 'Input',
  },
  {
    label: '用户名',
    field: 'username',
    component: 'Input',
  },
  {
    label: '玩家号',
    field: 'playId',
    component: 'Input',
  },
  {
    label: '用户积分',
    field: 'points',
    component: 'InputNumber',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  realName: {title: '真实姓名',order: 0,view: 'text', type: 'string',},
  grade: {title: '年级',order: 1,view: 'text', type: 'string',},
  phone: {title: '手机号',order: 2,view: 'text', type: 'string',},
  password: {title: '密码',order: 3,view: 'text', type: 'string',},
  address: {title: '地址',order: 4,view: 'text', type: 'string',},
  oaNickname: {title: '公众号昵称',order: 5,view: 'text', type: 'string',},
  oaOpenid: {title: '公众号 OpenId',order: 6,view: 'text', type: 'string',},
  mpOpenid: {title: '小程序OpenId',order: 7,view: 'text', type: 'string',},
  ewOpenid: {title: '企业微信 OpenId',order: 8,view: 'text', type: 'string',},
  unionId: {title: '联合 Id',order: 9,view: 'text', type: 'string',},
  role: {title: '角色',order: 10,view: 'text', type: 'string',},
  status: {title: '状态 (1正常 0停用)',order: 11,view: 'text', type: 'string',},
  remark: {title: '备注',order: 12,view: 'text', type: 'string',},
  avatar: {title: '头像',order: 13,view: 'text', type: 'string',},
  lastUseAt: {title: '最后一次进入系统的时间',order: 14,view: 'text', type: 'string',},
  vipTime: {title: '会员时间',order: 15,view: 'date', type: 'string',},
  salt: {title: 'salt',order: 16,view: 'text', type: 'string',},
  thirdType: {title: 'thirdType',order: 17,view: 'text', type: 'string',},
  isVip: {title: '会员状态 (0-非会员, 1-普通会员, 2-永久会员)',order: 18,view: 'number', type: 'number',},
  openid: {title: '微信OpenID',order: 19,view: 'text', type: 'string',},
  nickname: {title: '微信昵称',order: 20,view: 'text', type: 'string',},
  headimgurl: {title: '微信头像URL',order: 21,view: 'text', type: 'string',},
  gender: {title: '性别 (1男 2女 0未选择)',order: 22,view: 'number', type: 'number',},
  playid: {title: '玩家号',order: 23,view: 'text', type: 'string',},
  username: {title: '用户名',order: 24,view: 'text', type: 'string',},
  playId: {title: '玩家号',order: 25,view: 'text', type: 'string',},
  points: {title: '用户积分',order: 26,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}