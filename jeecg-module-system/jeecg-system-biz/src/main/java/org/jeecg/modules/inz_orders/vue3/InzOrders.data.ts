import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '订单编号',
    align:"center",
    dataIndex: 'orderNo'
   },
   {
    title: '用户ID',
    align:"center",
    dataIndex: 'userId'
   },
   {
    title: '店铺ID',
    align:"center",
    dataIndex: 'storeId'
   },
   {
    title: '订单类型(1:金钱订单,2:积分订单,3:混合支付)',
    align:"center",
    dataIndex: 'orderType'
   },
   {
    title: '订单总金额',
    align:"center",
    dataIndex: 'totalPrice'
   },
   {
    title: '实付金额',
    align:"center",
    dataIndex: 'payPrice'
   },
   {
    title: '使用的积分数量',
    align:"center",
    dataIndex: 'pointAmount'
   },
   {
    title: '优惠金额',
    align:"center",
    dataIndex: 'discountAmount'
   },
   {
    title: '运费',
    align:"center",
    dataIndex: 'freightAmount'
   },
   {
    title: '订单状态(1:未支付,2:已支付待发货,3:已发货待收货,4:已完成,5:已取消,6:已退款)',
    align:"center",
    dataIndex: 'orderStatus'
   },
   {
    title: '支付时间',
    align:"center",
    dataIndex: 'payTime'
   },
   {
    title: '支付方式(1-支付宝,2-微信)',
    align:"center",
    dataIndex: 'payType'
   },
   {
    title: '支付流水号',
    align:"center",
    dataIndex: 'transactionId'
   },
   {
    title: '收货人姓名',
    align:"center",
    dataIndex: 'receiverName'
   },
   {
    title: '收货人电话',
    align:"center",
    dataIndex: 'receiverPhone'
   },
   {
    title: '收货地址',
    align:"center",
    dataIndex: 'receiverAddress'
   },
   {
    title: '订单备注',
    align:"center",
    dataIndex: 'remark'
   },
   {
    title: '退款状态(1-未操作退款,2-用户已提交，未退款,3-退款中,4-退款成功,5-退款失败,6-退款驳回)',
    align:"center",
    dataIndex: 'refundStatus'
   },
   {
    title: '退款时间',
    align:"center",
    dataIndex: 'refundTime'
   },
   {
    title: '支付prepay_id',
    align:"center",
    dataIndex: 'prepayId'
   },
   {
    title: '物流单号',
    align:"center",
    dataIndex: 'trackingNo'
   },
   {
    title: '物流公司',
    align:"center",
    dataIndex: 'expressCompany'
   },
   {
    title: '物流公司编码',
    align:"center",
    dataIndex: 'expressCode'
   },
   {
    title: '发货时间',
    align:"center",
    dataIndex: 'shipTime'
   },
   {
    title: '完成时间',
    align:"center",
    dataIndex: 'finishTime'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '订单编号',
    field: 'orderNo',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入订单编号!'},
          ];
     },
  },
  {
    label: '用户ID',
    field: 'userId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入用户ID!'},
          ];
     },
  },
  {
    label: '店铺ID',
    field: 'storeId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入店铺ID!'},
          ];
     },
  },
  {
    label: '订单类型(1:金钱订单,2:积分订单,3:混合支付)',
    field: 'orderType',
    component: 'InputNumber',
  },
  {
    label: '订单总金额',
    field: 'totalPrice',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入订单总金额!'},
          ];
     },
  },
  {
    label: '实付金额',
    field: 'payPrice',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入实付金额!'},
          ];
     },
  },
  {
    label: '使用的积分数量',
    field: 'pointAmount',
    component: 'InputNumber',
  },
  {
    label: '优惠金额',
    field: 'discountAmount',
    component: 'InputNumber',
  },
  {
    label: '运费',
    field: 'freightAmount',
    component: 'InputNumber',
  },
  {
    label: '订单状态(1:未支付,2:已支付待发货,3:已发货待收货,4:已完成,5:已取消,6:已退款)',
    field: 'orderStatus',
    component: 'InputNumber',
  },
  {
    label: '支付时间',
    field: 'payTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '支付方式(1-支付宝,2-微信)',
    field: 'payType',
    component: 'InputNumber',
  },
  {
    label: '支付流水号',
    field: 'transactionId',
    component: 'Input',
  },
  {
    label: '收货人姓名',
    field: 'receiverName',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入收货人姓名!'},
          ];
     },
  },
  {
    label: '收货人电话',
    field: 'receiverPhone',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入收货人电话!'},
          ];
     },
  },
  {
    label: '收货地址',
    field: 'receiverAddress',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入收货地址!'},
          ];
     },
  },
  {
    label: '订单备注',
    field: 'remark',
    component: 'Input',
  },
  {
    label: '退款状态(1-未操作退款,2-用户已提交，未退款,3-退款中,4-退款成功,5-退款失败,6-退款驳回)',
    field: 'refundStatus',
    component: 'InputNumber',
  },
  {
    label: '退款时间',
    field: 'refundTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '支付prepay_id',
    field: 'prepayId',
    component: 'Input',
  },
  {
    label: '物流单号',
    field: 'trackingNo',
    component: 'Input',
  },
  {
    label: '物流公司',
    field: 'expressCompany',
    component: 'Input',
  },
  {
    label: '物流公司编码',
    field: 'expressCode',
    component: 'Input',
  },
  {
    label: '发货时间',
    field: 'shipTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '完成时间',
    field: 'finishTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  orderNo: {title: '订单编号',order: 0,view: 'text', type: 'string',},
  userId: {title: '用户ID',order: 1,view: 'text', type: 'string',},
  storeId: {title: '店铺ID',order: 2,view: 'text', type: 'string',},
  orderType: {title: '订单类型(1:金钱订单,2:积分订单,3:混合支付)',order: 3,view: 'number', type: 'number',},
  totalPrice: {title: '订单总金额',order: 4,view: 'number', type: 'number',},
  payPrice: {title: '实付金额',order: 5,view: 'number', type: 'number',},
  pointAmount: {title: '使用的积分数量',order: 6,view: 'number', type: 'number',},
  discountAmount: {title: '优惠金额',order: 7,view: 'number', type: 'number',},
  freightAmount: {title: '运费',order: 8,view: 'number', type: 'number',},
  orderStatus: {title: '订单状态(1:未支付,2:已支付待发货,3:已发货待收货,4:已完成,5:已取消,6:已退款)',order: 9,view: 'number', type: 'number',},
  payTime: {title: '支付时间',order: 10,view: 'datetime', type: 'string',},
  payType: {title: '支付方式(1-支付宝,2-微信)',order: 11,view: 'number', type: 'number',},
  transactionId: {title: '支付流水号',order: 12,view: 'text', type: 'string',},
  receiverName: {title: '收货人姓名',order: 13,view: 'text', type: 'string',},
  receiverPhone: {title: '收货人电话',order: 14,view: 'text', type: 'string',},
  receiverAddress: {title: '收货地址',order: 15,view: 'text', type: 'string',},
  remark: {title: '订单备注',order: 16,view: 'text', type: 'string',},
  refundStatus: {title: '退款状态(1-未操作退款,2-用户已提交，未退款,3-退款中,4-退款成功,5-退款失败,6-退款驳回)',order: 17,view: 'number', type: 'number',},
  refundTime: {title: '退款时间',order: 18,view: 'datetime', type: 'string',},
  prepayId: {title: '支付prepay_id',order: 19,view: 'text', type: 'string',},
  trackingNo: {title: '物流单号',order: 20,view: 'text', type: 'string',},
  expressCompany: {title: '物流公司',order: 21,view: 'text', type: 'string',},
  expressCode: {title: '物流公司编码',order: 22,view: 'text', type: 'string',},
  shipTime: {title: '发货时间',order: 23,view: 'datetime', type: 'string',},
  finishTime: {title: '完成时间',order: 24,view: 'datetime', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}