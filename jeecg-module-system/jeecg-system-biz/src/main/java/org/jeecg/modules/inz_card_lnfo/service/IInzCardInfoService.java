package org.jeecg.modules.inz_card_lnfo.service;

import org.jeecg.modules.inz_card_lnfo.entity.InzCardInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * @Description: 卡片信息表
 * @Author: jeecg-boot
 * @Date:   2025-06-20
 * @Version: V1.0
 */
public interface IInzCardInfoService extends IService<InzCardInfo> {
    
    /**
     * 通过店铺ID查询卡片信息
     * @param page 分页参数
     * @param storeId 店铺ID
     * @return 分页结果
     */
    IPage<InzCardInfo> queryCardInfosByStoreId(Page<InzCardInfo> page, String storeId);
}
