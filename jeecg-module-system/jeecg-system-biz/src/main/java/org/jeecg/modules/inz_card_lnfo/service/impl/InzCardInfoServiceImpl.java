package org.jeecg.modules.inz_card_lnfo.service.impl;

import org.jeecg.modules.inz_card_lnfo.entity.InzCardInfo;
import org.jeecg.modules.inz_card_lnfo.mapper.InzCardInfoMapper;
import org.jeecg.modules.inz_card_lnfo.service.IInzCardInfoService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 卡片信息表
 * @Author: jeecg-boot
 * @Date:   2025-06-20
 * @Version: V1.0
 */
@Service
public class InzCardInfoServiceImpl extends ServiceImpl<InzCardInfoMapper, InzCardInfo> implements IInzCardInfoService {
    
    @Override
    public IPage<InzCardInfo> queryCardInfosByStoreId(Page<InzCardInfo> page, String storeId) {
        if (StringUtils.isBlank(storeId)) {
            return new Page<>(page.getCurrent(), page.getSize());
        }
        
        // 构建查询条件
        QueryWrapper<InzCardInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("store_id", storeId);
        
        // 按创建时间倒序排序，最新创建的卡片排在前面
        queryWrapper.orderByDesc("create_time");
        
        // 执行分页查询
        return this.page(page, queryWrapper);
    }
}
