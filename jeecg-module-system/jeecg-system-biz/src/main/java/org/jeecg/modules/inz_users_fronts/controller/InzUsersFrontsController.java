package org.jeecg.modules.inz_users_fronts.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_users_fronts.entity.InzUsersFronts;
import org.jeecg.modules.inz_users_fronts.service.IInzUsersFrontsService;
import org.jeecg.modules.inz_store.entity.InzStore;
import org.jeecg.modules.inz_store.service.IInzStoreService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 用户表
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
/*@Api(tags="用户表")*/
@RestController
@RequestMapping("/inz_users_fronts/inzUsersFronts")
@Slf4j
public class InzUsersFrontsController extends JeecgController<InzUsersFronts, IInzUsersFrontsService> {
	@Autowired
	private IInzUsersFrontsService inzUsersFrontsService;
	@Autowired
	private IInzStoreService inzStoreService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzUsersFronts
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "用户表-分页列表查询")
	/*@ApiOperation(value="用户表-分页列表查询", notes="用户表-分页列表查询")*/
	@GetMapping(value = "/list")
	public Result<IPage<InzUsersFronts>> queryPageList(InzUsersFronts inzUsersFronts,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   @RequestParam(name="username", required=false) String username,
								   @RequestParam(name="storeName", required=false) String storeName,
								   HttpServletRequest req) {
        QueryWrapper<InzUsersFronts> queryWrapper = QueryGenerator.initQueryWrapper(inzUsersFronts, req.getParameterMap());
        
        // 处理用户名筛选
        if (org.apache.commons.lang3.StringUtils.isNotBlank(username)) {
            queryWrapper.like("username", username);
        }
        
        // 处理店铺名称筛选
        if (org.apache.commons.lang3.StringUtils.isNotBlank(storeName)) {
            // 通过店铺名称查询店铺，获取店主用户ID
            String storeId = inzStoreService.getStoreIdByName(storeName);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(storeId)) {
                // 查询店铺信息获取店主用户ID
                InzStore store = inzStoreService.getById(storeId);
                if (store != null && org.apache.commons.lang3.StringUtils.isNotBlank(store.getUserId())) {
                    queryWrapper.eq("id", store.getUserId());
                } else {
                    // 如果找不到店铺或店主信息，返回空结果
                    return Result.OK(new Page<>(pageNo, pageSize, 0));
                }
            } else {
                // 如果找不到店铺，返回空结果
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }
        }
        
		Page<InzUsersFronts> page = new Page<InzUsersFronts>(pageNo, pageSize);
		IPage<InzUsersFronts> pageList = inzUsersFrontsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param inzUsersFronts
	 * @return
	 */
	/*@AutoLog(value = "用户表-添加")
	@ApiOperation(value="用户表-添加", notes="用户表-添加")*/
	@RequiresPermissions("inz_users_fronts:inz_user_front:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InzUsersFronts inzUsersFronts) {
		inzUsersFrontsService.save(inzUsersFronts);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param inzUsersFronts
	 * @return
	 */
	/*@AutoLog(value = "用户表-编辑")
	@ApiOperation(value="用户表-编辑", notes="用户表-编辑")*/
	@RequiresPermissions("inz_users_fronts:inz_user_front:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InzUsersFronts inzUsersFronts) {
		inzUsersFrontsService.updateById(inzUsersFronts);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	/*@AutoLog(value = "用户表-通过id删除")
	@ApiOperation(value="用户表-通过id删除", notes="用户表-通过id删除")*/
	@RequiresPermissions("inz_users_fronts:inz_user_front:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzUsersFrontsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	/*@AutoLog(value = "用户表-批量删除")
	@ApiOperation(value="用户表-批量删除", notes="用户表-批量删除")*/
	@RequiresPermissions("inz_users_fronts:inz_user_front:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzUsersFrontsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "用户表-通过id查询")
	/*@ApiOperation(value="用户表-通过id查询", notes="用户表-通过id查询")*/
	@GetMapping(value = "/queryById")
	public Result<InzUsersFronts> queryById(@RequestParam(name="id",required=true) String id) {
		InzUsersFronts inzUsersFronts = inzUsersFrontsService.getById(id);
		if(inzUsersFronts==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzUsersFronts);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param inzUsersFronts
    */
    @RequiresPermissions("inz_users_fronts:inz_user_front:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzUsersFronts inzUsersFronts) {
        return super.exportXls(request, inzUsersFronts, InzUsersFronts.class, "用户表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("inz_users_fronts:inz_user_front:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzUsersFronts.class);
    }

}
