package org.jeecg.modules.inz_content.service.impl;

import org.jeecg.modules.inz_content.entity.ContentItems;
import org.jeecg.modules.inz_content.mapper.ContentItemsMapper;
import org.jeecg.modules.inz_content.service.IContentItemsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 内容项表
 * @Author: jeecg-boot
 * @Date:   2025-06-15
 * @Version: V1.0
 */
@Service
public class ContentItemsServiceImpl extends ServiceImpl<ContentItemsMapper, ContentItems> implements IContentItemsService {

}
