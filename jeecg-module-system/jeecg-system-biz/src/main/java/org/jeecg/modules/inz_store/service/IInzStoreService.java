package org.jeecg.modules.inz_store.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.inz_store.entity.InzStore;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * @Description: 店铺表
 * @Author: jeecg-boot
 * @Date:   2025-06-15
 * @Version: V1.0
 */
public interface IInzStoreService extends IService<InzStore> {

    /**
     * 通过店铺名称查询店铺ID
     * @param storeName 店铺名称
     * @return 店铺ID，如果不存在则返回null
     */
    String getStoreIdByName(String storeName);
    
    /**
     * 通过用户ID查询店铺
     * @param userId 用户ID
     * @return 店铺信息，如果不存在则返回null
     */
    InzStore getStoreByUserId(String userId);
    
    /**
     * 通过店铺ID查询店铺信息
     * @param storeId 店铺ID
     * @return 店铺信息，如果不存在则返回null
     */
    InzStore getStoreById(String storeId);

    /**
     * 获取店铺详细信息
     * @param storeId 店铺ID
     * @return 店铺详细信息，包含评分、关注数等
     */
    Map<String, Object> getStoreInfo(String storeId);

    // ==================== 第二阶段：审核管理集成 ====================
    // 注意：审核功能已集成到auditRecord模块，避免重复开发

    /**
     * 提交店铺审核到统一审核系统
     * @param storeId 店铺ID
     * @return 是否成功
     */
    boolean submitStoreForAudit(String storeId);
}
