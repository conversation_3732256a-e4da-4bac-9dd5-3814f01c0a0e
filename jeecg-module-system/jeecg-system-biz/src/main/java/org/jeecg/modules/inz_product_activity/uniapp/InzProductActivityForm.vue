<template>
    <view>
        <!--标题和返回-->
		<cu-custom :bgColor="NavBarColor" isBack :backRouterName="backRouteName">
			<block slot="backText">返回</block>
			<block slot="content">商品活动表</block>
		</cu-custom>
		 <!--表单区域-->
		<view>
			<form>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">活动名称：</text></view>
                  <input  placeholder="请输入活动名称" v-model="model.name"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">商品ID：</text></view>
                  <input  placeholder="请输入商品ID" v-model="model.productId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">活动标签（如限时购、爆款推荐等）：</text></view>
                  <input  placeholder="请输入活动标签（如限时购、爆款推荐等）" v-model="model.tag"/>
                </view>
              </view>
              <my-date label="活动开始时间：" v-model="model.startTime" placeholder="请输入活动开始时间"></my-date>
              <my-date label="活动结束时间：" v-model="model.endTime" placeholder="请输入活动结束时间"></my-date>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">活动价格（用于覆盖原价）：</text></view>
                  <input type="number" placeholder="请输入活动价格（用于覆盖原价）" v-model="model.activityPrice"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">活动库存（为空则取商品原始库存）：</text></view>
                  <input type="number" placeholder="请输入活动库存（为空则取商品原始库存）" v-model="model.activityStock"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">活动说明：</text></view>
                  <input  placeholder="请输入活动说明" v-model="model.description"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">是否启用（0-关闭，1-启用）：</text></view>
                  <input type="number" placeholder="请输入是否启用（0-关闭，1-启用）" v-model="model.status"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">是否全平台可见：</text></view>
                  <input type="number" placeholder="请输入是否全平台可见" v-model="model.globalVisible"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">排序值：</text></view>
                  <input type="number" placeholder="请输入排序值" v-model="model.sort"/>
                </view>
              </view>
				<view class="padding">
					<button class="cu-btn block bg-blue margin-tb-sm lg" @click="onSubmit">
						<text v-if="loading" class="cuIcon-loading2 cuIconfont-spin"></text>提交
					</button>
				</view>
			</form>
		</view>
    </view>
</template>

<script>
    import myDate from '@/components/my-componets/my-date.vue'

    export default {
        name: "InzProductActivityForm",
        components:{ myDate },
        props:{
          formData:{
              type:Object,
              default:()=>{},
              required:false
          }
        },
        data(){
            return {
				CustomBar: this.CustomBar,
				NavBarColor: this.NavBarColor,
				loading:false,
                model: {},
                backRouteName:'index',
                url: {
                  queryById: "/inz_product_activity/inzProductActivity/queryById",
                  add: "/inz_product_activity/inzProductActivity/add",
                  edit: "/inz_product_activity/inzProductActivity/edit",
                },
            }
        },
        created(){
             this.initFormData();
        },
        methods:{
           initFormData(){
               if(this.formData){
                    let dataId = this.formData.dataId;
                    this.$http.get(this.url.queryById,{params:{id:dataId}}).then((res)=>{
                        if(res.data.success){
                            console.log("表单数据",res);
                            this.model = res.data.result;
                        }
                    })
                }
            },
            onSubmit() {
                let myForm = {...this.model};
                this.loading = true;
                let url = myForm.id?this.url.edit:this.url.add;
				this.$http.post(url,myForm).then(res=>{
				   console.log("res",res)
				   this.loading = false
				   this.$Router.push({name:this.backRouteName})
				}).catch(()=>{
					this.loading = false
				});
            }
        }
    }
</script>
