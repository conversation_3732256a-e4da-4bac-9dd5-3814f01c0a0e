package org.jeecg.modules.inz_orders_logistics.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.inz_orders_logistics.entity.ShippingRecord;
import org.jeecg.modules.inz_orders_logistics.model.ExpressTrackDTO;
import org.jeecg.modules.inz_orders_logistics.model.Kuaidi100Request;
import org.jeecg.modules.inz_orders_logistics.service.IExpressTrackingService;
import org.jeecg.modules.inz_orders_logistics.service.IShippingRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;

/**
 * @Description: 发货记录控制器
 * @Author: jeecg-boot
 * @Date: 2024-07-28
 * @Version: V1.0
 */
@Api(tags = "发货记录管理")
@RestController
@RequestMapping("/inz_orders_logistics/shipping")
@Slf4j
@Validated
public class ShippingRecordController extends JeecgController<ShippingRecord, IShippingRecordService> {
    
    @Autowired
    private IShippingRecordService shippingRecordService;
    
    @Autowired
    private IExpressTrackingService expressTrackingService;
    
    /**
     * 分页列表查询
     *
     * @param shippingRecord 查询条件
     * @param pageNo 页码
     * @param pageSize 每页记录数
     * @param req HTTP请求
     * @return 分页结果
     */
    @ApiOperation(value = "发货记录-分页列表查询", notes = "发货记录-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ShippingRecord>> queryPageList(ShippingRecord shippingRecord,
                                                      @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                      HttpServletRequest req) {
        Page<ShippingRecord> page = new Page<>(pageNo, pageSize);
        IPage<ShippingRecord> pageList = shippingRecordService.pageList(page, shippingRecord);
        return Result.OK(pageList);
    }
    
    /**
     * 添加
     *
     * @param shippingRecord 发货记录
     * @return 操作结果
     */
    @ApiOperation(value = "发货记录-添加", notes = "发货记录-添加")
    @RequiresPermissions("inz_orders_logistics:shipping:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody ShippingRecord shippingRecord) {
        // 设置创建人
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null) {
            shippingRecord.setCreateBy(sysUser.getUsername());
        }
        
        // 调用服务发货
        boolean success = shippingRecordService.ship(shippingRecord);
        if (success) {
            return Result.OK("添加成功！");
        } else {
            return Result.error("添加失败，请检查输入的发货信息！");
        }
    }
    
    /**
     * 编辑
     *
     * @param shippingRecord 发货记录
     * @return 操作结果
     */
    @ApiOperation(value = "发货记录-编辑", notes = "发货记录-编辑")
    @RequiresPermissions("inz_orders_logistics:shipping:edit")
    @PutMapping(value = "/edit")
    public Result<String> edit(@RequestBody ShippingRecord shippingRecord) {
        // 设置更新人
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null) {
            shippingRecord.setUpdateBy(sysUser.getUsername());
        }
        shippingRecord.setUpdateTime(new Date());
        
        // 更新记录
        shippingRecordService.updateById(shippingRecord);
        return Result.OK("编辑成功!");
    }
    
    /**
     * 通过id删除
     *
     * @param id 记录ID
     * @return 操作结果
     */
    @ApiOperation(value = "发货记录-通过id删除", notes = "发货记录-通过id删除")
    @RequiresPermissions("inz_orders_logistics:shipping:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        shippingRecordService.removeById(id);
        return Result.OK("删除成功!");
    }
    
    /**
     * 批量删除
     *
     * @param ids 记录ID列表，英文逗号分隔
     * @return 操作结果
     */
    @ApiOperation(value = "发货记录-批量删除", notes = "发货记录-批量删除")
    @RequiresPermissions("inz_orders_logistics:shipping:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.shippingRecordService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }
    
    /**
     * 通过id查询
     *
     * @param id 记录ID
     * @return 发货记录
     */
    @ApiOperation(value = "发货记录-通过id查询", notes = "发货记录-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ShippingRecord> queryById(@RequestParam(name = "id", required = true) String id) {
        ShippingRecord shippingRecord = shippingRecordService.getById(id);
        if (shippingRecord == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(shippingRecord);
    }
    
    /**
     * 修改物流信息
     *
     * @param id 记录ID
     * @param expressCompany 快递公司
     * @param trackingNo 快递单号
     * @return 操作结果
     */
    @ApiOperation(value = "修改物流信息", notes = "修改发货记录的物流公司和单号")
    @RequiresPermissions("inz_orders_logistics:shipping:updateLogistics")
    @PostMapping(value = "/updateLogistics")
    public Result<String> updateLogistics(@RequestParam(name = "id", required = true) String id,
                                         @RequestParam(name = "expressCompany", required = true) String expressCompany,
                                         @RequestParam(name = "trackingNo", required = true) String trackingNo) {
        if (StringUtils.isBlank(id) || StringUtils.isBlank(expressCompany) || StringUtils.isBlank(trackingNo)) {
            return Result.error("参数不完整");
        }
        
        boolean success = shippingRecordService.updateLogistics(id, expressCompany, trackingNo);
        if (success) {
            return Result.OK("修改物流信息成功");
        } else {
            return Result.error("修改物流信息失败");
        }
    }
    
    /**
     * 查询物流轨迹
     *
     * @param id 记录ID
     * @return 物流轨迹
     */
    @ApiOperation(value = "查询物流轨迹", notes = "查询发货记录的物流轨迹信息")
    @GetMapping(value = "/track/{id}")
    public Result<ExpressTrackDTO> queryTrack(@PathVariable(name = "id") String id) {
        if (StringUtils.isBlank(id)) {
            return Result.error("参数不能为空");
        }
        
        ExpressTrackDTO trackDTO = shippingRecordService.queryLogisticsTrack(id);
        if (trackDTO == null) {
            return Result.error("查询物流信息失败，请检查物流单号或稍后重试");
        }
        
        return Result.OK(trackDTO);
    }
    
    /**
     * 批量查询物流轨迹
     *
     * @param ids 记录ID列表，英文逗号分隔
     * @return 物流轨迹映射，key为记录ID，value为轨迹数据
     */
    @ApiOperation(value = "批量查询物流轨迹", notes = "批量查询多个发货记录的物流轨迹信息")
    @GetMapping(value = "/batchTrack")
    public Result<Map<String, ExpressTrackDTO>> batchQueryTrack(@RequestParam(name = "ids", required = true) String ids) {
        if (StringUtils.isBlank(ids)) {
            return Result.error("参数不能为空");
        }
        
        Map<String, ExpressTrackDTO> trackMap = shippingRecordService.batchQueryLogisticsTrack(Arrays.asList(ids.split(",")));
        return Result.OK(trackMap);
    }
    
    /**
     * 订阅物流推送
     *
     * @param ids 记录ID列表，英文逗号分隔
     * @return 操作结果
     */
    @ApiOperation(value = "订阅物流推送", notes = "订阅指定发货记录的物流推送服务")
    @PostMapping(value = "/subscribe")
    public Result<String> subscribeLogistics(@RequestParam(name = "ids", required = true) String ids) {
        if (StringUtils.isBlank(ids)) {
            return Result.error("参数不能为空");
        }
        
        boolean success = shippingRecordService.batchSubscribe(Arrays.asList(ids.split(",")));
        if (success) {
            return Result.OK("订阅物流推送成功");
        } else {
            return Result.error("部分或全部订阅失败，请检查物流信息是否完整");
        }
    }
    
    /**
     * 物流推送回调接口
     *
     * @param callbackData 回调数据
     * @return 处理结果
     */
    @ApiOperation(value = "物流推送回调", notes = "接收快递100推送的物流轨迹变更通知")
    @PostMapping(value = "/callback")
    public Result<String> logisticsCallback(@RequestBody String callbackData) {
        log.info("收到物流回调数据: {}", callbackData);
        if (StringUtils.isBlank(callbackData)) {
            return Result.error("回调数据为空");
        }
        
        boolean success = expressTrackingService.processCallbackData(callbackData);
        if (success) {
            return Result.OK("处理成功");
        } else {
            return Result.error("处理失败");
        }
    }
    
    /**
     * 导出excel
     *
     * @param request HTTP请求
     * @param shippingRecord 查询条件
     * @return Excel文件
     */
    @RequiresPermissions("inz_orders_logistics:shipping:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ShippingRecord shippingRecord) {
        return super.exportXls(request, shippingRecord, ShippingRecord.class, "发货记录");
    }
    
    /**
     * 通过excel导入数据
     *
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 操作结果
     */
    @RequiresPermissions("inz_orders_logistics:shipping:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ShippingRecord.class);
    }
} 