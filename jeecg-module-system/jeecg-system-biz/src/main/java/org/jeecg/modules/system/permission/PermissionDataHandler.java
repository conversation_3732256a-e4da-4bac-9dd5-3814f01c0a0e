package org.jeecg.modules.system.permission;

import org.jeecg.common.system.vo.SysPermissionDataRuleModel;

import javax.servlet.http.HttpServletRequest;

/**
 * 数据权限处理器接口
 */
public interface PermissionDataHandler {
    
    /**
     * 获取数据权限SQL片段
     *
     * @param rule 数据规则模型
     * @param mappingField 规则配置的字段
     * @param request 当前请求
     * @return SQL片段
     */
    String getSQLSegment(SysPermissionDataRuleModel rule, String mappingField, HttpServletRequest request);
} 