package org.jeecg.modules.inz_product.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.inz_product.entity.InzProduct;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 商品表
 * @Author: jeecg-boot
 * @Date:   2025-06-15
 * @Version: V1.0
 */
public interface IInzProductService extends IService<InzProduct> {

    /**
     * 根据店铺ID查询商品信息
     * @param page 分页参数
     * @param inzProduct 查询条件
     * @param storeId 店铺ID
     * @param parameterMap 请求参数
     * @return 分页结果
     */
    IPage<InzProduct> queryProductsByStoreId(Page<InzProduct> page,
                                             InzProduct inzProduct,
                                             String storeId,
                                             Map<String, String[]> parameterMap);
                                             
    /**
     * 获取店铺中的热销商品（销量占库存的70%以上）
     * @param page 分页参数
     * @param storeId 店铺ID
     * @return 热销商品分页结果
     */
    IPage<InzProduct> getHotSellingProducts(Page<InzProduct> page, String storeId);
    
    /**
     * 获取店铺中的热销商品（销量占库存的70%以上）
     * @param page 分页参数
     * @param storeName 店铺名称
     * @return 热销商品分页结果
     */
    IPage<InzProduct> getHotSellingProductsByStoreName(Page<InzProduct> page, String storeName);
    
    /**
     * 通过店铺名称查询商品列表（不分页）
     * @param storeName 店铺名称
     * @return 商品列表
     */
    List<InzProduct> getProductsByStoreName(String storeName);
    
    /**
     * 通过店铺名称查询商品
     * @param page 分页参数
     * @param inzProduct 商品参数
     * @param storeName 店铺名称
     * @param parameterMap 查询参数
     * @return 分页数据
     */
    IPage<InzProduct> queryProductsByStoreName(Page<InzProduct> page, InzProduct inzProduct, String storeName, Map<String, String[]> parameterMap);

    /**
     * 商品上架
     * 
     * @param id 商品ID
     */
    void putOnSale(String id);
    
    /**
     * 商品下架
     * 
     * @param id 商品ID
     */
    void putOffSale(String id);
    
    /**
     * 获取店铺特色商品列表
     * 
     * @param page 分页参数
     * @param storeId 店铺ID
     * @return 特色商品分页列表
     */
    IPage<InzProduct> getFeaturedProducts(Page<InzProduct> page, String storeId);
    
    /**
     * 获取商品详情（包含助力信息和倒计时）
     * 
     * @param id 商品ID
     * @return 商品详情
     */
    Map<String, Object> getProductDetail(String id);
}
