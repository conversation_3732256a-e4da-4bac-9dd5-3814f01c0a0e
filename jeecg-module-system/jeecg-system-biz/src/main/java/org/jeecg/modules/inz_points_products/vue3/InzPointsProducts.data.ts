import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '商品名称',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '商品图片',
    align:"center",
    dataIndex: 'image'
   },
   {
    title: '商品描述',
    align:"center",
    dataIndex: 'description'
   },
   {
    title: '所需积分',
    align:"center",
    dataIndex: 'points'
   },
   {
    title: '库存数量',
    align:"center",
    dataIndex: 'stock'
   },
   {
    title: '已兑换数量',
    align:"center",
    dataIndex: 'exchangeCount'
   },
   {
    title: '限购数量(0表示不限购)',
    align:"center",
    dataIndex: 'limitCount'
   },
   {
    title: '商品类型(1:实物商品,2:虚拟商品,3:优惠券)',
    align:"center",
    dataIndex: 'type'
   },
   {
    title: '商品状态(0:下架,1:上架)',
    align:"center",
    dataIndex: 'status'
   },
   {
    title: '上架时间',
    align:"center",
    dataIndex: 'shelfTime'
   },
   {
    title: '下架时间',
    align:"center",
    dataIndex: 'offShelfTime'
   },
   {
    title: '排序',
    align:"center",
    dataIndex: 'sort'
   },
   {
    title: '是否限量版(0:否,1:是)',
    align:"center",
    dataIndex: 'isLimited'
   },
   {
    title: '限量编号',
    align:"center",
    dataIndex: 'limitedNumber'
   },
   {
    title: '关联商品ID',
    align:"center",
    dataIndex: 'productId'
   },
   {
    title: '商品标签',
    align:"center",
    dataIndex: 'tags'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '商品名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入商品名称!'},
          ];
     },
  },
  {
    label: '商品图片',
    field: 'image',
    component: 'Input',
  },
  {
    label: '商品描述',
    field: 'description',
    component: 'InputTextArea',
  },
  {
    label: '所需积分',
    field: 'points',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入所需积分!'},
          ];
     },
  },
  {
    label: '库存数量',
    field: 'stock',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入库存数量!'},
          ];
     },
  },
  {
    label: '已兑换数量',
    field: 'exchangeCount',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入已兑换数量!'},
          ];
     },
  },
  {
    label: '限购数量(0表示不限购)',
    field: 'limitCount',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入限购数量(0表示不限购)!'},
          ];
     },
  },
  {
    label: '商品类型(1:实物商品,2:虚拟商品,3:优惠券)',
    field: 'type',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入商品类型(1:实物商品,2:虚拟商品,3:优惠券)!'},
          ];
     },
  },
  {
    label: '商品状态(0:下架,1:上架)',
    field: 'status',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入商品状态(0:下架,1:上架)!'},
          ];
     },
  },
  {
    label: '上架时间',
    field: 'shelfTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '下架时间',
    field: 'offShelfTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '排序',
    field: 'sort',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入排序!'},
          ];
     },
  },
  {
    label: '是否限量版(0:否,1:是)',
    field: 'isLimited',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入是否限量版(0:否,1:是)!'},
          ];
     },
  },
  {
    label: '限量编号',
    field: 'limitedNumber',
    component: 'Input',
  },
  {
    label: '关联商品ID',
    field: 'productId',
    component: 'Input',
  },
  {
    label: '商品标签',
    field: 'tags',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '商品名称',order: 0,view: 'text', type: 'string',},
  image: {title: '商品图片',order: 1,view: 'text', type: 'string',},
  description: {title: '商品描述',order: 2,view: 'textarea', type: 'string',},
  points: {title: '所需积分',order: 3,view: 'number', type: 'number',},
  stock: {title: '库存数量',order: 4,view: 'number', type: 'number',},
  exchangeCount: {title: '已兑换数量',order: 5,view: 'number', type: 'number',},
  limitCount: {title: '限购数量(0表示不限购)',order: 6,view: 'number', type: 'number',},
  type: {title: '商品类型(1:实物商品,2:虚拟商品,3:优惠券)',order: 7,view: 'number', type: 'number',},
  status: {title: '商品状态(0:下架,1:上架)',order: 8,view: 'number', type: 'number',},
  shelfTime: {title: '上架时间',order: 9,view: 'datetime', type: 'string',},
  offShelfTime: {title: '下架时间',order: 10,view: 'datetime', type: 'string',},
  sort: {title: '排序',order: 11,view: 'number', type: 'number',},
  isLimited: {title: '是否限量版(0:否,1:是)',order: 12,view: 'number', type: 'number',},
  limitedNumber: {title: '限量编号',order: 13,view: 'text', type: 'string',},
  productId: {title: '关联商品ID',order: 14,view: 'text', type: 'string',},
  tags: {title: '商品标签',order: 15,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}