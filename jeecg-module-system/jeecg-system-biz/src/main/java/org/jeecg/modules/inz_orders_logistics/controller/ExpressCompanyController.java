package org.jeecg.modules.inz_orders_logistics.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.inz_orders_logistics.model.ExpressCompanyEnum;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 快递公司控制器
 */
@Api(tags = "快递公司管理")
@RestController
@RequestMapping("/inz_orders_logistics/expressCompany")
@Slf4j
public class ExpressCompanyController {
    
    /**
     * 获取支持的快递公司列表
     *
     * @return 快递公司列表
     */
    @ApiOperation(value = "获取支持的快递公司列表", notes = "获取支持的快递公司列表")
    @GetMapping("/list")
    public Result<List<Map<String, String>>> getExpressCompanies() {
        List<Map<String, String>> companies = Arrays.stream(ExpressCompanyEnum.values())
                .filter(company -> company != ExpressCompanyEnum.UNKNOWN)
                .map(company -> {
                    Map<String, String> map = new HashMap<>();
                    map.put("code", company.getCode());
                    map.put("name", company.getName());
                    return map;
                })
                .collect(Collectors.toList());
        
        return Result.OK(companies);
    }
} 