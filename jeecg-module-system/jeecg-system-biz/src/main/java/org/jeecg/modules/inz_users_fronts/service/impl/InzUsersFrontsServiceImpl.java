package org.jeecg.modules.inz_users_fronts.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.inz_users_fronts.entity.InzUsersFronts;
import org.jeecg.modules.inz_users_fronts.mapper.InzUsersFrontsMapper;
import org.jeecg.modules.inz_users_fronts.service.IInzUsersFrontsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 用户表
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Service
public class InzUsersFrontsServiceImpl extends ServiceImpl<InzUsersFrontsMapper, InzUsersFronts> implements IInzUsersFrontsService {

    @Override
    public String getUserIdByUsername(String username) {
        if (StringUtils.isBlank(username)) {
            return null;
        }
        
        LambdaQueryWrapper<InzUsersFronts> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InzUsersFronts::getUsername, username);
        // 只查询ID字段以提高性能
        queryWrapper.select(InzUsersFronts::getId);
        
        InzUsersFronts user = this.getOne(queryWrapper);
        return user != null ? user.getId() : null;
    }

    @Override
    public String getUserIdByNickname(String nickname) {
        if (StringUtils.isBlank(nickname)) {
            return null;
        }
        
        LambdaQueryWrapper<InzUsersFronts> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InzUsersFronts::getNickname, nickname);
        // 只查询ID字段以提高性能
        queryWrapper.select(InzUsersFronts::getId);
        
        InzUsersFronts user = this.getOne(queryWrapper);
        return user != null ? user.getId() : null;
    }

    @Override
    public String getUserIdByPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return null;
        }
        
        LambdaQueryWrapper<InzUsersFronts> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InzUsersFronts::getPhone, phone);
        // 只查询ID字段以提高性能
        queryWrapper.select(InzUsersFronts::getId);
        
        InzUsersFronts user = this.getOne(queryWrapper);
        return user != null ? user.getId() : null;
    }

}
