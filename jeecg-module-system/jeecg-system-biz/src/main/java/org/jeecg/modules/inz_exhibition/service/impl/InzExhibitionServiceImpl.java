package org.jeecg.modules.inz_exhibition.service.impl;

import org.jeecg.modules.inz_exhibition.entity.InzExhibition;
import org.jeecg.modules.inz_exhibition.mapper.InzExhibitionMapper;
import org.jeecg.modules.inz_exhibition.service.IInzExhibitionService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 用户展馆表
 * @Author: jeecg-boot
 * @Date:   2025-06-15
 * @Version: V1.0
 */
@Service
public class InzExhibitionServiceImpl extends ServiceImpl<InzExhibitionMapper, InzExhibition> implements IInzExhibitionService {

}
