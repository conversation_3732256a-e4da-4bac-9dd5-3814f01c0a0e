import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '店鋪ID',
    align:"center",
    dataIndex: 'storeId'
   },
   {
    title: '流水号',
    align:"center",
    dataIndex: 'transactionNo'
   },
   {
    title: '関聯訂単ID',
    align:"center",
    dataIndex: 'orderId'
   },
   {
    title: '関聯訂単号',
    align:"center",
    dataIndex: 'orderNo'
   },
   {
    title: '交易类型：1-訂単收入，2-退款，3-提現，4-人工調整，5-平台傭金',
    align:"center",
    dataIndex: 'type'
   },
   {
    title: '交易金額',
    align:"center",
    dataIndex: 'amount'
   },
   {
    title: '交易前余額',
    align:"center",
    dataIndex: 'beforeBalance'
   },
   {
    title: '交易後余額',
    align:"center",
    dataIndex: 'afterBalance'
   },
   {
    title: '交易狀態：1-待処理，2-已完成，3-失敗，4-已取消',
    align:"center",
    dataIndex: 'status'
   },
   {
    title: '備註',
    align:"center",
    dataIndex: 'remark'
   },
   {
    title: '第三方交易ID',
    align:"center",
    dataIndex: 'externalId'
   },
   {
    title: '操作人',
    align:"center",
    dataIndex: 'operator'
   },
   {
    title: '完成时间',
    align:"center",
    dataIndex: 'completeTime'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '店鋪ID',
    field: 'storeId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入店鋪ID!'},
          ];
     },
  },
  {
    label: '流水号',
    field: 'transactionNo',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入流水号!'},
          ];
     },
  },
  {
    label: '関聯訂単ID',
    field: 'orderId',
    component: 'Input',
  },
  {
    label: '関聯訂単号',
    field: 'orderNo',
    component: 'Input',
  },
  {
    label: '交易类型：1-訂単收入，2-退款，3-提現，4-人工調整，5-平台傭金',
    field: 'type',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入交易类型：1-訂単收入，2-退款，3-提現，4-人工調整，5-平台傭金!'},
          ];
     },
  },
  {
    label: '交易金額',
    field: 'amount',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入交易金額!'},
          ];
     },
  },
  {
    label: '交易前余額',
    field: 'beforeBalance',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入交易前余額!'},
          ];
     },
  },
  {
    label: '交易後余額',
    field: 'afterBalance',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入交易後余額!'},
          ];
     },
  },
  {
    label: '交易狀態：1-待処理，2-已完成，3-失敗，4-已取消',
    field: 'status',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入交易狀態：1-待処理，2-已完成，3-失敗，4-已取消!'},
          ];
     },
  },
  {
    label: '備註',
    field: 'remark',
    component: 'Input',
  },
  {
    label: '第三方交易ID',
    field: 'externalId',
    component: 'Input',
  },
  {
    label: '操作人',
    field: 'operator',
    component: 'Input',
  },
  {
    label: '完成时间',
    field: 'completeTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  storeId: {title: '店鋪ID',order: 0,view: 'text', type: 'string',},
  transactionNo: {title: '流水号',order: 1,view: 'text', type: 'string',},
  orderId: {title: '関聯訂単ID',order: 2,view: 'text', type: 'string',},
  orderNo: {title: '関聯訂単号',order: 3,view: 'text', type: 'string',},
  type: {title: '交易类型：1-訂単收入，2-退款，3-提現，4-人工調整，5-平台傭金',order: 4,view: 'number', type: 'number',},
  amount: {title: '交易金額',order: 5,view: 'number', type: 'number',},
  beforeBalance: {title: '交易前余額',order: 6,view: 'number', type: 'number',},
  afterBalance: {title: '交易後余額',order: 7,view: 'number', type: 'number',},
  status: {title: '交易狀態：1-待処理，2-已完成，3-失敗，4-已取消',order: 8,view: 'number', type: 'number',},
  remark: {title: '備註',order: 9,view: 'text', type: 'string',},
  externalId: {title: '第三方交易ID',order: 10,view: 'text', type: 'string',},
  operator: {title: '操作人',order: 11,view: 'text', type: 'string',},
  completeTime: {title: '完成时间',order: 12,view: 'datetime', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}