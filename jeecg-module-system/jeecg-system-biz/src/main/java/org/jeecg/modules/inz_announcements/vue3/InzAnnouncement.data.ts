import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '公告标题',
    align:"center",
    dataIndex: 'title'
   },
   {
    title: '公告内容',
    align:"center",
    dataIndex: 'content'
   },
   {
    title: '公告类型 1-系统公告 2-活动公告 3-维护公告 4-其他',
    align:"center",
    dataIndex: 'type'
   },
   {
    title: '发布状态 0-草稿 1-已发布 2-已下线',
    align:"center",
    dataIndex: 'status'
   },
   {
    title: '是否置顶 0-否 1-是',
    align:"center",
    dataIndex: 'isTop'
   },
   {
    title: '优先级 数字越大优先级越高',
    align:"center",
    dataIndex: 'priority'
   },
   {
    title: '发布时间',
    align:"center",
    dataIndex: 'publishTime'
   },
   {
    title: '生效时间',
    align:"center",
    dataIndex: 'startTime'
   },
   {
    title: '失效时间',
    align:"center",
    dataIndex: 'endTime'
   },
   {
    title: '阅读次数',
    align:"center",
    dataIndex: 'readCount'
   },
   {
    title: '封面图片',
    align:"center",
    dataIndex: 'coverImage'
   },
   {
    title: '是否发送通知 0-否 1-是',
    align:"center",
    dataIndex: 'sendNotification'
   },
   {
    title: '目标用户类型 0-全部用户 1-普通用户 2-店主用户 3-VIP用户',
    align:"center",
    dataIndex: 'targetUserType'
   },
   {
    title: '备注',
    align:"center",
    dataIndex: 'remark'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '公告标题',
    field: 'title',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入公告标题!'},
          ];
     },
  },
  {
    label: '公告内容',
    field: 'content',
    component: 'InputTextArea',
  },
  {
    label: '公告类型 1-系统公告 2-活动公告 3-维护公告 4-其他',
    field: 'type',
    component: 'InputNumber',
  },
  {
    label: '发布状态 0-草稿 1-已发布 2-已下线',
    field: 'status',
    component: 'InputNumber',
  },
  {
    label: '是否置顶 0-否 1-是',
    field: 'isTop',
    component: 'InputNumber',
  },
  {
    label: '优先级 数字越大优先级越高',
    field: 'priority',
    component: 'InputNumber',
  },
  {
    label: '发布时间',
    field: 'publishTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '生效时间',
    field: 'startTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '失效时间',
    field: 'endTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '阅读次数',
    field: 'readCount',
    component: 'InputNumber',
  },
  {
    label: '封面图片',
    field: 'coverImage',
    component: 'Input',
  },
  {
    label: '是否发送通知 0-否 1-是',
    field: 'sendNotification',
    component: 'InputNumber',
  },
  {
    label: '目标用户类型 0-全部用户 1-普通用户 2-店主用户 3-VIP用户',
    field: 'targetUserType',
    component: 'InputNumber',
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  title: {title: '公告标题',order: 0,view: 'text', type: 'string',},
  content: {title: '公告内容',order: 1,view: 'textarea', type: 'string',},
  type: {title: '公告类型 1-系统公告 2-活动公告 3-维护公告 4-其他',order: 2,view: 'number', type: 'number',},
  status: {title: '发布状态 0-草稿 1-已发布 2-已下线',order: 3,view: 'number', type: 'number',},
  isTop: {title: '是否置顶 0-否 1-是',order: 4,view: 'number', type: 'number',},
  priority: {title: '优先级 数字越大优先级越高',order: 5,view: 'number', type: 'number',},
  publishTime: {title: '发布时间',order: 6,view: 'datetime', type: 'string',},
  startTime: {title: '生效时间',order: 7,view: 'datetime', type: 'string',},
  endTime: {title: '失效时间',order: 8,view: 'datetime', type: 'string',},
  readCount: {title: '阅读次数',order: 9,view: 'number', type: 'number',},
  coverImage: {title: '封面图片',order: 10,view: 'text', type: 'string',},
  sendNotification: {title: '是否发送通知 0-否 1-是',order: 11,view: 'number', type: 'number',},
  targetUserType: {title: '目标用户类型 0-全部用户 1-普通用户 2-店主用户 3-VIP用户',order: 12,view: 'number', type: 'number',},
  remark: {title: '备注',order: 13,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}