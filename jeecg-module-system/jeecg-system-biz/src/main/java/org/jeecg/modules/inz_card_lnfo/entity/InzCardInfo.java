package org.jeecg.modules.inz_card_lnfo.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 卡片信息表
 * @Author: jeecg-boot
 * @Date:   2025-06-20
 * @Version: V1.0
 */
@Data
@TableName("card_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="card_info对象", description="卡片信息表")
public class InzCardInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**卡片名称*/
	@Excel(name = "卡片名称", width = 15)
    @ApiModelProperty(value = "卡片名称")
    private java.lang.String name;
	/**卡片类型*/
	@Excel(name = "卡片类型", width = 15)
    @ApiModelProperty(value = "卡片类型")
    private java.lang.String type;
	/**卡片等级*/
	@Excel(name = "卡片等级", width = 15)
    @ApiModelProperty(value = "卡片等级")
    private java.math.BigDecimal grade;
	/**卡片图片*/
	@Excel(name = "卡片图片", width = 15)
    @ApiModelProperty(value = "卡片图片")
    private java.lang.String image;
	/**卡片价格*/
	@Excel(name = "卡片价格", width = 15)
    @ApiModelProperty(value = "卡片价格")
    private java.math.BigDecimal price;
	/**库存数量*/
	@Excel(name = "库存数量", width = 15)
    @ApiModelProperty(value = "库存数量")
    private java.lang.Integer stock;
	/**店铺ID*/
	@Excel(name = "店铺ID", width = 15)
    @ApiModelProperty(value = "店铺ID")
    private java.lang.String storeId;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**删除标记*/
	@Excel(name = "删除标记", width = 15)
    @ApiModelProperty(value = "删除标记")
    @TableLogic
    private java.lang.Integer delFlag;
}
