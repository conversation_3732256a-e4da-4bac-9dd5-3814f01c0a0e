package org.jeecg.modules.inz_coupons.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_coupons.entity.InzCoupons;
import org.jeecg.modules.inz_coupons.mapper.InzCouponsMapper;
import org.jeecg.modules.inz_coupons.service.IInzCouponsService;
import org.jeecg.modules.inz_store.service.IInzStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Description: 优惠券表
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Service
public class InzCouponsServiceImpl extends ServiceImpl<InzCouponsMapper, InzCoupons> implements IInzCouponsService {

    @Autowired
    private InzCouponsMapper couponsMapper;
    
    @Autowired
    private IInzStoreService inzStoreService;
    
    // 使用内存缓存模拟兑换码黑名单，实际项目中应该使用数据库或Redis
    private static final Set<String> COUPON_CODE_BLACKLIST = Collections.newSetFromMap(new ConcurrentHashMap<>());

    @Override
    public IPage<InzCoupons> queryCouponsByStoreId(Page<InzCoupons> page, 
                                                  InzCoupons inzCoupons, 
                                                  String storeId, 
                                                  Map<String, String[]> parameterMap) {
        // 输入验证
        if (page == null) {
            throw new JeecgBootException("分页参数不能为空");
        }
        
        // 构建查询条件
        QueryWrapper<InzCoupons> queryWrapper = QueryGenerator.initQueryWrapper(inzCoupons, parameterMap);
        
        if (StringUtils.isNotBlank(storeId)) {
            // 添加店铺ID过滤条件
            queryWrapper.eq("store_id", storeId);
        }
        
        // 执行分页查询
        return this.baseMapper.selectPage(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableCoupon(String id) {
        // 输入验证
        if (StringUtils.isBlank(id)) {
            throw new JeecgBootException("优惠券ID不能为空");
        }
        
        InzCoupons coupon = this.getById(id);
        if (coupon == null) {
            throw new JeecgBootException("优惠券不存在，ID: " + id);
        }
        
        // 状态检查 - 避免重复操作
        if (coupon.getStatus() != null && coupon.getStatus() == 0) {
            throw new JeecgBootException("优惠券已经是禁用状态，无需重复操作");
        }
        
        // 禁用优惠券的方式：
        // 1. 将状态设置为禁用(0)
        coupon.setStatus(0);
        
        // 2. 将结束时间设置为当前时间，使其立即过期（可选）
        Date currentTime = new Date();
        if (coupon.getEndTime() == null || coupon.getEndTime().after(currentTime)) {
            coupon.setEndTime(currentTime);
        }
        
        // 更新优惠券信息
        boolean result = this.updateById(coupon);
        if (!result) {
            throw new JeecgBootException("禁用优惠券失败，数据库更新错误");
        }
        
        // 记录操作日志（实际项目中可能需要记录到数据库）
        log.warn("优惠券 [" + id + "] 已被禁用，名称: " + coupon.getName());
    }
    
    @Override
    public void disableCouponCode(String code) {
        // 输入验证
        if (StringUtils.isBlank(code)) {
            throw new JeecgBootException("兑换码不能为空");
        }
        
        // 格式验证（假设兑换码是16位大写字母和数字的组合）
        if (!code.matches("^[A-Z0-9]{16}$")) {
            throw new JeecgBootException("兑换码格式不正确，应为16位大写字母和数字的组合");
        }
        
        // 状态检查 - 避免重复操作
        if (COUPON_CODE_BLACKLIST.contains(code)) {
            throw new JeecgBootException("该兑换码已被禁用，无需重复操作");
        }
        
        // 将兑换码加入黑名单
        COUPON_CODE_BLACKLIST.add(code);
        
        // 由于兑换码是动态生成的，没有存储在数据库中
        // 这里可以通过以下方式处理：
        // 1. 如果有兑换码黑名单表，将此兑换码添加到黑名单中
        // 2. 如果兑换码已经被用户领取并存储在用户优惠券表中，可以将其标记为已使用或过期
        
        // 这里模拟将兑换码加入黑名单的操作
        // 实际实现时，需要根据系统设计来决定如何处理
        // 例如：blacklistMapper.insert(new CouponCodeBlacklist(code));
        
        // 记录操作日志
        log.warn("兑换码 " + code + " 已被禁用，但当前系统没有兑换码存储机制，需要完善此功能");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateAndPublishCoupon(InzCoupons inzCoupons) {
        // 输入验证
        if (inzCoupons == null) {
            throw new JeecgBootException("优惠券信息不能为空");
        }
        
        // 必填字段验证
        if (StringUtils.isBlank(inzCoupons.getName())) {
            throw new JeecgBootException("优惠券名称不能为空");
        }
        
        if (inzCoupons.getType() == null || (inzCoupons.getType() != 1 && inzCoupons.getType() != 2)) {
            throw new JeecgBootException("优惠券类型必须为1(固定金额)或2(折扣)");
        }
        
        if (inzCoupons.getAmount() == null || inzCoupons.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new JeecgBootException("优惠金额/折扣率必须大于0");
        }
        
        // 折扣类型时，折扣率不能超过10（假设是十折制）
        if (inzCoupons.getType() == 2 && inzCoupons.getAmount().compareTo(new BigDecimal("10")) > 0) {
            throw new JeecgBootException("折扣率不能超过10");
        }
        
        if (inzCoupons.getMinAmount() == null) {
            inzCoupons.setMinAmount(BigDecimal.ZERO); // 默认最低使用金额为0
        } else if (inzCoupons.getMinAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new JeecgBootException("最低使用金额不能为负数");
        }
        
        if (inzCoupons.getTotalQuantity() == null || inzCoupons.getTotalQuantity() <= 0) {
            throw new JeecgBootException("发行总量必须大于0");
        }
        
        if (inzCoupons.getStartTime() == null) {
            throw new JeecgBootException("生效时间不能为空");
        }
        
        if (inzCoupons.getEndTime() == null) {
            throw new JeecgBootException("失效时间不能为空");
        }
        
        if (inzCoupons.getStartTime().after(inzCoupons.getEndTime())) {
            throw new JeecgBootException("生效时间不能晚于失效时间");
        }
        
        // 设置剩余数量和状态
        inzCoupons.setRemainingQuantity(inzCoupons.getTotalQuantity());
        inzCoupons.setStatus(1); // 默认启用
        
        // 保存到数据库
        boolean result = this.save(inzCoupons);
        if (!result) {
            throw new JeecgBootException("创建优惠券失败，数据库插入错误");
        }
        
        // 记录操作日志
        log.warn("成功创建优惠券: " + inzCoupons.getName() + ", ID: " + inzCoupons.getId());
    }

    @Override
    public Result<Map<String, Object>> generateCouponCode(String couponId, Integer count) {
        // 输入验证
        if (StringUtils.isBlank(couponId)) {
            return Result.error("优惠券ID不能为空");
        }
        
        if (count == null) {
            return Result.error("生成数量不能为空");
        }
        
        // 验证优惠券是否存在
        InzCoupons coupon = this.getById(couponId);
        if (coupon == null) {
            return Result.error("优惠券不存在，ID: " + couponId);
        }
        
        // 验证优惠券是否已禁用
        if (coupon.getStatus() != null && coupon.getStatus() == 0) {
            return Result.error("优惠券已禁用，无法生成兑换码");
        }
        
        // 验证优惠券是否已过期
        Date currentTime = new Date();
        if (coupon.getEndTime().before(currentTime)) {
            return Result.error("优惠券已过期，无法生成兑换码");
        }
        
        // 验证优惠券是否已生效
        if (coupon.getStartTime().after(currentTime)) {
            return Result.error("优惠券尚未生效，无法生成兑换码");
        }

        // 验证生成数量
        if (count <= 0) {
            return Result.error("生成数量必须大于0");
        }
        
        if (count > 1000) {
            return Result.error("单次生成数量不能超过1000");
        }

        // 生成兑换码列表
        List<String> couponCodes = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            String code = generateUniqueCode();
            // 确保兑换码唯一且不在黑名单中
            while (couponCodes.contains(code) || COUPON_CODE_BLACKLIST.contains(code)) {
                code = generateUniqueCode();
            }
            couponCodes.add(code);
        }

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("couponId", couponId);
        result.put("couponName", coupon.getName());
        result.put("couponType", coupon.getType());
        result.put("couponAmount", coupon.getAmount());
        result.put("couponMinAmount", coupon.getMinAmount());
        result.put("generatedCount", count);
        result.put("couponCodes", couponCodes);
        result.put("generateTime", currentTime);

        return Result.OK(result);
    }
    
    @Override
    public Result<Map<String, Object>> generateCouponCode(Integer type, BigDecimal amount, BigDecimal minAmount, Integer count, String storeId) {
        // 输入验证
        if (type == null) {
            return Result.error("优惠券类型不能为空");
        }
        
        if (type != 1 && type != 2) {
            return Result.error("优惠券类型必须为1(固定金额)或2(折扣)");
        }
        
        if (amount == null) {
            return Result.error("优惠金额/折扣率不能为空");
        }
        
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return Result.error("优惠金额/折扣率必须大于0");
        }
        
        // 折扣类型时，折扣率不能超过10 (假设是十折制)
        if (type == 2 && amount.compareTo(new BigDecimal("10")) > 0) {
            return Result.error("折扣率不能超过10");
        }
        
        if (minAmount == null) {
            minAmount = BigDecimal.ZERO; // 默认最低使用金额为0
        } else if (minAmount.compareTo(BigDecimal.ZERO) < 0) {
            return Result.error("最低使用金额不能为负数");
        }
        
        if (count == null) {
            return Result.error("生成数量不能为空");
        }
        
        // 验证生成数量
        if (count <= 0) {
            return Result.error("生成数量必须大于0");
        }
        
        if (count > 1000) {
            return Result.error("单次生成数量不能超过1000");
        }
        
        // 店铺ID验证（如果需要验证店铺是否存在，可以在这里添加代码）
        
        // 生成兑换码列表和描述信息
        List<String> couponCodes = new ArrayList<>();
        List<Map<String, Object>> codeDetails = new ArrayList<>();
        Date currentTime = new Date();
        
        for (int i = 0; i < count; i++) {
            String code = generateUniqueCode();
            // 确保兑换码唯一且不在黑名单中
            while (couponCodes.contains(code) || COUPON_CODE_BLACKLIST.contains(code)) {
                code = generateUniqueCode();
            }
            couponCodes.add(code);
            
            // 每个兑换码的详细信息
            Map<String, Object> codeDetail = new HashMap<>();
            codeDetail.put("code", code);
            codeDetail.put("type", type);
            codeDetail.put("amount", amount);
            codeDetail.put("minAmount", minAmount);
            codeDetail.put("createTime", currentTime);
            codeDetail.put("status", 1); // 默认启用
            if (StringUtils.isNotBlank(storeId)) {
                codeDetail.put("storeId", storeId);
            }
            codeDetails.add(codeDetail);
        }
        
        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("type", type);
        result.put("typeName", type == 1 ? "固定金额" : "折扣");
        result.put("amount", amount);
        result.put("minAmount", minAmount);
        if (StringUtils.isNotBlank(storeId)) {
            result.put("storeId", storeId);
        }
        result.put("generatedCount", count);
        result.put("couponCodes", couponCodes);
        result.put("codeDetails", codeDetails);
        result.put("generateTime", currentTime);
        
        return Result.OK(result);
    }

    /**
     * 生成唯一的兑换码
     * @return 兑换码
     */
    private String generateUniqueCode() {
        // 生成16位随机字符串，包含数字和大写字母
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder code = new StringBuilder();
        Random random = new Random();
        
        for (int i = 0; i < 16; i++) {
            code.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return code.toString();
    }
    
    /**
     * 检查兑换码是否被禁用
     * @param code 兑换码
     * @return 是否被禁用
     */
    public boolean isCouponCodeDisabled(String code) {
        if (StringUtils.isBlank(code)) {
            return false;
        }
        return COUPON_CODE_BLACKLIST.contains(code);
    }

    @Override
    public IPage<InzCoupons> queryCouponsByStoreName(Page<InzCoupons> page, String storeName) {
        // 先通过店铺名称获取店铺ID
        String storeId = inzStoreService.getStoreIdByName(storeName);
        if (storeId == null) {
            return new Page<>(page.getCurrent(), page.getSize(), 0);
        }
        
        QueryWrapper<InzCoupons> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("store_id", storeId);
        return this.baseMapper.selectPage(page, queryWrapper);
    }
}
