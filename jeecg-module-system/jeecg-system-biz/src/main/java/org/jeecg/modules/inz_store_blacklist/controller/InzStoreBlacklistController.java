package org.jeecg.modules.inz_store_blacklist.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_store_blacklist.entity.InzStoreBlacklist;
import org.jeecg.modules.inz_store_blacklist.service.IInzStoreBlacklistService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.inz_store.entity.InzStore;
import org.jeecg.modules.inz_store.service.IInzStoreService;
import org.jeecg.modules.inz_users_fronts.service.IInzUsersFrontsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 黑名单表
 * @Author: jeecg-boot
 * @Date:   2025-06-22
 * @Version: V1.0
 */
@Api(tags="黑名单表")
@RestController
@RequestMapping("/inz_store_blacklist/inzStoreBlacklist")
@Slf4j
public class InzStoreBlacklistController extends JeecgController<InzStoreBlacklist, IInzStoreBlacklistService> {
	@Autowired
	private IInzStoreBlacklistService inzStoreBlacklistService;
	
	@Autowired
	private IInzStoreService inzStoreService;
	
	@Autowired
	private IInzUsersFrontsService inzUsersFrontsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzStoreBlacklist 基本查询条件
	 * @param pageNo 页码
	 * @param pageSize 每页记录数
	 * @param storeId 店铺ID筛选
	 * @param storeName 店铺名称筛选
	 * @param userName 用户名称筛选
	 * @param req HTTP请求
	 * @return 分页结果
	 */
	//@AutoLog(value = "黑名单表-分页列表查询")
	@ApiOperation(value="黑名单表-分页列表查询", notes="黑名单表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<InzStoreBlacklist>> queryPageList(InzStoreBlacklist inzStoreBlacklist,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   @RequestParam(name="storeId", required=false) String storeId,
								   @RequestParam(name="storeName", required=false) String storeName,
								   @RequestParam(name="userName", required=false) String userName,
								   HttpServletRequest req) {
        try {
            QueryWrapper<InzStoreBlacklist> queryWrapper = QueryGenerator.initQueryWrapper(inzStoreBlacklist, req.getParameterMap());
            Page<InzStoreBlacklist> page = new Page<InzStoreBlacklist>(pageNo, pageSize);
            
            // 处理店铺ID筛选
            if (StringUtils.isNotBlank(storeId)) {
                queryWrapper.eq("store_id", storeId);
            }
            
            // 处理店铺名称筛选
            if (StringUtils.isNotBlank(storeName)) {
                // 通过店铺名称获取店铺ID
                String storeIdFromName = inzStoreService.getStoreIdByName(storeName);
                if (StringUtils.isNotBlank(storeIdFromName)) {
                    queryWrapper.eq("store_id", storeIdFromName);
                } else {
                    // 如果找不到店铺，返回空结果
                    return Result.OK(new Page<>(pageNo, pageSize, 0));
                }
            }
            
            // 处理用户名称筛选
            if (StringUtils.isNotBlank(userName)) {
                // 通过用户名称获取用户ID
                String userIdFromName = inzUsersFrontsService.getUserIdByUsername(userName);
                if (StringUtils.isBlank(userIdFromName)) {
                    // 如果通过用户名找不到，尝试通过昵称查找
                    userIdFromName = inzUsersFrontsService.getUserIdByNickname(userName);
                }
                
                if (StringUtils.isNotBlank(userIdFromName)) {
                    queryWrapper.eq("user_id", userIdFromName);
                } else {
                    // 如果找不到用户，返回空结果
                    return Result.OK(new Page<>(pageNo, pageSize, 0));
                }
            }
            
            // ====== 数据权限控制逻辑开始 ======
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser != null) {
                if (!isAdmin(sysUser)) {
                    log.info("非管理员用户访问黑名单列表，进行数据权限过滤");
                    String loginUsername = sysUser.getUsername();
                    String userId = null;
                    try {
                        userId = inzUsersFrontsService.getUserIdByUsername(loginUsername);
                        log.info("通过用户名[{}]查询到前端用户ID: {}", loginUsername, userId);
                    } catch (Exception e) {
                        log.warn("通过用户名查询前端用户失败: {}", e.getMessage());
                    }
                    if (StringUtils.isBlank(userId)) {
                        try {
                            userId = inzUsersFrontsService.getUserIdByNickname(loginUsername);
                            log.info("通过昵称[{}]查询到前端用户ID: {}", loginUsername, userId);
                        } catch (Exception e) {
                            log.warn("通过昵称查询前端用户失败: {}", e.getMessage());
                        }
                    }
                    if (StringUtils.isNotBlank(userId)) {
                        QueryWrapper<InzStore> storeQueryWrapper = new QueryWrapper<>();
                        storeQueryWrapper.eq("user_id", userId);
                        InzStore store = inzStoreService.getOne(storeQueryWrapper);
                        if (store != null) {
                            log.info("用户[{}]关联店铺[{}]，只能查看该店铺黑名单", userId, store.getId());
                            queryWrapper.eq("store_id", store.getId());
                        } else {
                            log.warn("用户[{}]没有关联店铺，返回空结果", userId);
                            return Result.OK(new Page<>(pageNo, pageSize, 0));
                        }
                    } else {
                        log.warn("未找到后台用户[{}]对应的前端用户，返回空结果", loginUsername);
                        return Result.OK(new Page<>(pageNo, pageSize, 0));
                    }
                } else {
                    log.info("管理员用户访问黑名单列表，不进行数据权限过滤");
                }
            }
            // ====== 数据权限控制逻辑结束 ======
            
            // 执行查询
            IPage<InzStoreBlacklist> pageList = inzStoreBlacklistService.page(page, queryWrapper);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error("查询黑名单列表失败", e);
            return Result.error("查询黑名单列表失败: " + e.getMessage());
        }
	}
	
	/**
     * 判断用户是否为管理员
     * @param user 登录用户
     * @return 是否为管理员
     */
    private boolean isAdmin(LoginUser user) {
        if (user == null) {
            return false;
        }
        String roleCode = user.getRoleCode();
        return StringUtils.isNotBlank(roleCode) && (roleCode.contains("admin") || roleCode.contains("ADMIN"));
    }
	
	/**
	 *   添加
	 *
	 * @param inzStoreBlacklist
	 * @return
	 */
	@AutoLog(value = "黑名单表-添加")
	@ApiOperation(value="黑名单表-添加", notes="黑名单表-添加")
	@RequiresPermissions("inz_store_blacklist:inz_store_blacklist:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InzStoreBlacklist inzStoreBlacklist) {
	    try {
	        // 验证必填字段
	        if (inzStoreBlacklist == null) {
	            return Result.error("黑名单信息不能为空");
	        }
	        
	        if (StringUtils.isBlank(inzStoreBlacklist.getUserId())) {
	            return Result.error("用户ID不能为空");
	        }
	        
	        if (StringUtils.isBlank(inzStoreBlacklist.getStoreId())) {
	            return Result.error("店铺ID不能为空");
	        }
	        
	        // 检查是否已存在相同记录
	        QueryWrapper<InzStoreBlacklist> queryWrapper = new QueryWrapper<>();
	        queryWrapper.eq("user_id", inzStoreBlacklist.getUserId())
	                   .eq("store_id", inzStoreBlacklist.getStoreId());
	        InzStoreBlacklist existingRecord = inzStoreBlacklistService.getOne(queryWrapper);
	        if (existingRecord != null) {
	            return Result.error("该用户已在黑名单中，无需重复添加");
	        }
	        
	        // 设置拉黑时间
	        if (inzStoreBlacklist.getBlockTime() == null) {
	            inzStoreBlacklist.setBlockTime(new java.util.Date());
	        }
	        
	        // 保存黑名单记录
	        inzStoreBlacklistService.save(inzStoreBlacklist);
	        return Result.OK("添加成功！");
	    } catch (Exception e) {
	        log.error("添加黑名单失败", e);
	        return Result.error("添加黑名单失败: " + e.getMessage());
	    }
	}
	
	/**
	 *  编辑
	 *
	 * @param inzStoreBlacklist
	 * @return
	 */
	@AutoLog(value = "黑名单表-编辑")
	@ApiOperation(value="黑名单表-编辑", notes="黑名单表-编辑")
	@RequiresPermissions("inz_store_blacklist:inz_store_blacklist:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InzStoreBlacklist inzStoreBlacklist) {
		inzStoreBlacklistService.updateById(inzStoreBlacklist);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "黑名单表-通过id删除")
	@ApiOperation(value="黑名单表-通过id删除", notes="黑名单表-通过id删除")
	@RequiresPermissions("inz_store_blacklist:inz_store_blacklist:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzStoreBlacklistService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "黑名单表-批量删除")
	@ApiOperation(value="黑名单表-批量删除", notes="黑名单表-批量删除")
	@RequiresPermissions("inz_store_blacklist:inz_store_blacklist:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzStoreBlacklistService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "黑名单表-通过id查询")
	@ApiOperation(value="黑名单表-通过id查询", notes="黑名单表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<InzStoreBlacklist> queryById(@RequestParam(name="id",required=true) String id) {
		InzStoreBlacklist inzStoreBlacklist = inzStoreBlacklistService.getById(id);
		if(inzStoreBlacklist==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzStoreBlacklist);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param inzStoreBlacklist
    */
    @RequiresPermissions("inz_store_blacklist:inz_store_blacklist:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzStoreBlacklist inzStoreBlacklist) {
        return super.exportXls(request, inzStoreBlacklist, InzStoreBlacklist.class, "黑名单表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("inz_store_blacklist:inz_store_blacklist:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzStoreBlacklist.class);
    }

    /**
     * 判断用户是否被店铺拉黑
     * @param req {"storeId":"xxx", "userId":"yyy"}
     * @return true/false
     */
    @ApiOperation(value = "判断用户是否被店铺拉黑", notes = "判断指定用户是否在店铺黑名单中")
    @PostMapping("/check")
    public Result<Boolean> isUserBlacklisted(@RequestBody BlacklistCheckRequest req) {
        try {
            if (StringUtils.isBlank(req.storeId) || StringUtils.isBlank(req.userId)) {
                return Result.error("店铺ID和用户ID不能为空");
            }
            
            InzStoreBlacklist one = inzStoreBlacklistService.getOne(
                new QueryWrapper<InzStoreBlacklist>()
                    .eq("store_id", req.storeId).eq("user_id", req.userId)
            );
            return Result.OK(one != null);
        } catch (Exception e) {
            log.error("查询黑名单状态失败", e);
            return Result.error("查询黑名单状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取店铺黑名单列表
     * @param req {"storeId":"xxx"}
     * @return 黑名单用户列表
     */
    @ApiOperation(value = "获取店铺黑名单列表", notes = "获取指定店铺的黑名单用户列表")
    @PostMapping("/listByStore")
    public Result<java.util.List<InzStoreBlacklist>> getBlacklist(@RequestBody BlacklistStoreRequest req) {
        try {
            if (StringUtils.isBlank(req.storeId)) {
                return Result.error("店铺ID不能为空");
            }
            
            // 数据权限控制
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser != null && !isAdmin(sysUser)) {
                String loginUsername = sysUser.getUsername();
                String userId = inzUsersFrontsService.getUserIdByUsername(loginUsername);
                if (StringUtils.isBlank(userId)) {
                    userId = inzUsersFrontsService.getUserIdByNickname(loginUsername);
                }
                
                if (StringUtils.isNotBlank(userId)) {
                    QueryWrapper<InzStore> storeQueryWrapper = new QueryWrapper<>();
                    storeQueryWrapper.eq("user_id", userId);
                    InzStore store = inzStoreService.getOne(storeQueryWrapper);
                    
                    if (store != null && !store.getId().equals(req.storeId)) {
                        return Result.error("您只能查看自己店铺的黑名单");
                    }
                }
            }
            
            java.util.List<InzStoreBlacklist> list = inzStoreBlacklistService.list(
                new QueryWrapper<InzStoreBlacklist>()
                    .eq("store_id", req.storeId)
            );
            return Result.OK(list);
        } catch (Exception e) {
            log.error("获取店铺黑名单列表失败", e);
            return Result.error("获取店铺黑名单列表失败: " + e.getMessage());
        }
    }

    /**
     * 解除黑名单
     * @param req {"storeId":"xxx", "userId":"yyy"}
     * @return 操作结果
     */
    @ApiOperation(value = "解除黑名单", notes = "将指定用户从店铺黑名单中移除")
    @PostMapping("/remove")
    public Result<String> removeFromBlacklist(@RequestBody BlacklistRemoveRequest req) {
        try {
            if (StringUtils.isBlank(req.storeId) || StringUtils.isBlank(req.userId)) {
                return Result.error("店铺ID和用户ID不能为空");
            }
            
            // 数据权限控制
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser != null && !isAdmin(sysUser)) {
                String loginUsername = sysUser.getUsername();
                String userId = inzUsersFrontsService.getUserIdByUsername(loginUsername);
                if (StringUtils.isBlank(userId)) {
                    userId = inzUsersFrontsService.getUserIdByNickname(loginUsername);
                }
                
                if (StringUtils.isNotBlank(userId)) {
                    QueryWrapper<InzStore> storeQueryWrapper = new QueryWrapper<>();
                    storeQueryWrapper.eq("user_id", userId);
                    InzStore store = inzStoreService.getOne(storeQueryWrapper);
                    
                    if (store != null && !store.getId().equals(req.storeId)) {
                        return Result.error("您只能操作自己店铺的黑名单");
                    }
                }
            }
            
            boolean removed = inzStoreBlacklistService.remove(
                new QueryWrapper<InzStoreBlacklist>()
                    .eq("store_id", req.storeId).eq("user_id", req.userId)
            );
            if (removed) {
                return Result.OK("已解除黑名单");
            } else {
                return Result.error("解除失败，记录不存在");
            }
        } catch (Exception e) {
            log.error("解除黑名单失败", e);
            return Result.error("解除黑名单失败: " + e.getMessage());
        }
    }

    // DTO参数类
    public static class BlacklistCheckRequest {
        public String storeId;
        public String userId;
    }
    public static class BlacklistStoreRequest {
        public String storeId;
    }
    public static class BlacklistRemoveRequest {
        public String storeId;
        public String userId;
    }
}
