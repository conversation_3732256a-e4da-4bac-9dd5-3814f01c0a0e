package org.jeecg.modules.inz_store_transactions.service;

import org.jeecg.modules.inz_store_transactions.entity.InzStoreTransactions;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * @Description: inz_store_transaction
 * @Author: jeecg-boot
 * @Date:   2025-06-26
 * @Version: V1.0
 */
public interface IInzStoreTransactionsService extends IService<InzStoreTransactions> {

    /**
     * 获取店铺交易数据概览
     * @return 包含总交易数量、总交易金额、合作门店数量、月增长率的Map
     */
    Map<String, Object> getTransactionSummary();
}
