package org.jeecg.modules.inz_exhibition.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 用户展馆表
 * @Author: jeecg-boot
 * @Date:   2025-06-15
 * @Version: V1.0
 */
@Data
@TableName("inz_exhibition")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_exhibition对象", description="用户展馆表")
public class InzExhibition implements Serializable {
    private static final long serialVersionUID = 1L;

	/**ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "ID")
    private java.lang.String id;
	/**用户ID*/
	@Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private java.lang.String userId;
	/**展馆名称*/
	@Excel(name = "展馆名称", width = 15)
    @ApiModelProperty(value = "展馆名称")
    private java.lang.String name;
	/**展馆描述*/
	@Excel(name = "展馆描述", width = 15)
    @ApiModelProperty(value = "展馆描述")
    private java.lang.String description;
	/**展馆封面图*/
	@Excel(name = "展馆封面图", width = 15)
    @ApiModelProperty(value = "展馆封面图")
    private java.lang.String coverImage;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**状态 (1-公开 0-私有)*/
	@Excel(name = "状态 (1-公开 0-私有)", width = 15)
    @ApiModelProperty(value = "状态 (1-公开 0-私有)")
    private java.lang.Integer status;
}
