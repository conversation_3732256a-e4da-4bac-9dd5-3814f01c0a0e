import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '商品名称',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '商品编号',
    align:"center",
    dataIndex: 'productNo'
   },
   {
    title: '店铺ID',
    align:"center",
    dataIndex: 'storeId'
   },
   {
    title: '商品主图',
    align:"center",
    dataIndex: 'mainImage'
   },
   {
    title: '商品图片，多个逗号分隔',
    align:"center",
    dataIndex: 'images'
   },
   {
    title: '商品价格',
    align:"center",
    dataIndex: 'price'
   },
   {
    title: '商品原价',
    align:"center",
    dataIndex: 'originalPrice'
   },
   {
    title: '商品库存',
    align:"center",
    dataIndex: 'stock'
   },
   {
    title: '商品销量',
    align:"center",
    dataIndex: 'sales'
   },
   {
    title: '商品状态（0-下架，1-上架）',
    align:"center",
    dataIndex: 'status'
   },
   {
    title: '商品类型（1-普通商品，2-限量版，3-签名版）',
    align:"center",
    dataIndex: 'type'
   },
   {
    title: '商品描述',
    align:"center",
    dataIndex: 'description'
   },
   {
    title: '商品详情',
    align:"center",
    dataIndex: 'detail'
   },
   {
    title: '商品规格（JSON格式）',
    align:"center",
    dataIndex: 'specifications'
   },
   {
    title: '商品分类',
    align:"center",
    dataIndex: 'category'
   },
   {
    title: '商品标签，逗号分隔',
    align:"center",
    dataIndex: 'tags'
   },
   {
    title: '年份',
    align:"center",
    dataIndex: 'year'
   },
   {
    title: '球队',
    align:"center",
    dataIndex: 'team'
   },
   {
    title: '球员',
    align:"center",
    dataIndex: 'player'
   },
   {
    title: '卡片评级',
    align:"center",
    dataIndex: 'grade'
   },
   {
    title: '评分',
    align:"center",
    dataIndex: 'rating'
   },
   {
    title: '限量编号（如1/99）',
    align:"center",
    dataIndex: 'limitedNumber'
   },
   {
    title: '是否有签名（0-无，1-有）',
    align:"center",
    dataIndex: 'hasSigned'
   },
   {
    title: '是否有球衣碎片（0-无，1-有）',
    align:"center",
    dataIndex: 'hasJerseyPatch'
   },
   {
    title: '卡片类型',
    align:"center",
    dataIndex: 'cardType'
   },
   {
    title: '运费模板ID',
    align:"center",
    dataIndex: 'freightTemplateId'
   },
   {
    title: '是否包邮（0-否，1-是）',
    align:"center",
    dataIndex: 'freeShipping'
   },
   {
    title: '排序',
    align:"center",
    dataIndex: 'sort'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '商品名称',
    field: 'name',
    component: 'Input',
  },
  {
    label: '商品编号',
    field: 'productNo',
    component: 'Input',
  },
  {
    label: '店铺ID',
    field: 'storeId',
    component: 'Input',
  },
  {
    label: '商品主图',
    field: 'mainImage',
    component: 'Input',
  },
  {
    label: '商品图片，多个逗号分隔',
    field: 'images',
    component: 'InputTextArea',
  },
  {
    label: '商品价格',
    field: 'price',
    component: 'InputNumber',
  },
  {
    label: '商品原价',
    field: 'originalPrice',
    component: 'InputNumber',
  },
  {
    label: '商品库存',
    field: 'stock',
    component: 'InputNumber',
  },
  {
    label: '商品销量',
    field: 'sales',
    component: 'InputNumber',
  },
  {
    label: '商品状态（0-下架，1-上架）',
    field: 'status',
    component: 'InputNumber',
  },
  {
    label: '商品类型',
    field: 'type',
    component: 'Select',
    componentProps: {
      options: [
        { label: '随机卡种', value: 1 },
        { label: '随机球员', value: 2 },
        { label: '随机球队', value: 3 },
        { label: '选队随机', value: 4 },
        { label: '自选球队', value: 5 },
        { label: '队伍随机', value: 6 },
      ],
    },
  },
  {
    label: '商品描述',
    field: 'description',
    component: 'Input',
  },
  {
    label: '商品详情',
    field: 'detail',
    component: 'InputTextArea',
  },
  {
    label: '商品规格（JSON格式）',
    field: 'specifications',
    component: 'InputTextArea',
  },
  {
    label: '商品分类',
    field: 'category',
    component: 'Input',
  },
  {
    label: '商品标签，逗号分隔',
    field: 'tags',
    component: 'Input',
  },
  {
    label: '年份',
    field: 'year',
    component: 'Input',
  },
  {
    label: '球队',
    field: 'team',
    component: 'Input',
  },
  {
    label: '球员',
    field: 'player',
    component: 'Input',
  },
  {
    label: '卡片评级',
    field: 'grade',
    component: 'Input',
  },
  {
    label: '评分',
    field: 'rating',
    component: 'InputNumber',
  },
  {
    label: '限量编号（如1/99）',
    field: 'limitedNumber',
    component: 'Input',
  },
  {
    label: '是否有签名（0-无，1-有）',
    field: 'hasSigned',
    component: 'InputNumber',
  },
  {
    label: '是否有球衣碎片（0-无，1-有）',
    field: 'hasJerseyPatch',
    component: 'InputNumber',
  },
  {
    label: '卡片类型',
    field: 'cardType',
    component: 'Input',
  },
  {
    label: '运费模板ID',
    field: 'freightTemplateId',
    component: 'Input',
  },
  {
    label: '是否包邮（0-否，1-是）',
    field: 'freeShipping',
    component: 'InputNumber',
  },
  {
    label: '排序',
    field: 'sort',
    component: 'InputNumber',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '商品名称',order: 0,view: 'text', type: 'string',},
  productNo: {title: '商品编号',order: 1,view: 'text', type: 'string',},
  storeId: {title: '店铺ID',order: 2,view: 'text', type: 'string',},
  mainImage: {title: '商品主图',order: 3,view: 'text', type: 'string',},
  images: {title: '商品图片，多个逗号分隔',order: 4,view: 'textarea', type: 'string',},
  price: {title: '商品价格',order: 5,view: 'number', type: 'number',},
  originalPrice: {title: '商品原价',order: 6,view: 'number', type: 'number',},
  stock: {title: '商品库存',order: 7,view: 'number', type: 'number',},
  sales: {title: '商品销量',order: 8,view: 'number', type: 'number',},
  status: {title: '商品状态（0-下架，1-上架）',order: 9,view: 'number', type: 'number',},
  type: {title: '商品类型（1-随机卡种，2-随机球员，3-随机球队，4-选队随机，5-自选球队，6-队伍随机）',order: 10,view: 'select', type: 'number',},
  description: {title: '商品描述',order: 11,view: 'text', type: 'string',},
  detail: {title: '商品详情',order: 12,view: 'textarea', type: 'string',},
  specifications: {title: '商品规格（JSON格式）',order: 13,view: 'textarea', type: 'string',},
  category: {title: '商品分类',order: 14,view: 'text', type: 'string',},
  tags: {title: '商品标签，逗号分隔',order: 15,view: 'text', type: 'string',},
  year: {title: '年份',order: 16,view: 'text', type: 'string',},
  team: {title: '球队',order: 17,view: 'text', type: 'string',},
  player: {title: '球员',order: 18,view: 'text', type: 'string',},
  grade: {title: '卡片评级',order: 19,view: 'text', type: 'string',},
  rating: {title: '评分',order: 20,view: 'number', type: 'number',},
  limitedNumber: {title: '限量编号（如1/99）',order: 21,view: 'text', type: 'string',},
  hasSigned: {title: '是否有签名（0-无，1-有）',order: 22,view: 'number', type: 'number',},
  hasJerseyPatch: {title: '是否有球衣碎片（0-无，1-有）',order: 23,view: 'number', type: 'number',},
  cardType: {title: '卡片类型',order: 24,view: 'text', type: 'string',},
  freightTemplateId: {title: '运费模板ID',order: 25,view: 'text', type: 'string',},
  freeShipping: {title: '是否包邮（0-否，1-是）',order: 26,view: 'number', type: 'number',},
  sort: {title: '排序',order: 27,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}