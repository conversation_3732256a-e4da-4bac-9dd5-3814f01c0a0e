package org.jeecg.modules.inz_product_settlement.service.impl;

import org.jeecg.modules.inz_product_settlement.entity.InzSettlementBatch;
import org.jeecg.modules.inz_product_settlement.entity.InzProductSettlement;
import org.jeecg.modules.inz_product_settlement.mapper.InzSettlementBatchMapper;
import org.jeecg.modules.inz_product_settlement.service.IInzSettlementBatchService;
import org.jeecg.modules.inz_product_settlement.service.IInzProductSettlementService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.context.annotation.Lazy;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Description: 结算批次
 * @Author: jeecg-boot
 * @Date: 2025-06-26
 * @Version: V1.0
 */
@Service
@Slf4j
public class InzSettlementBatchServiceImpl extends ServiceImpl<InzSettlementBatchMapper, InzSettlementBatch> implements IInzSettlementBatchService {

    @Autowired
    @Lazy
    private IInzProductSettlementService productSettlementService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createSettlementBatch(String batchName, List<String> storeIds, Date startTime, Date endTime, String remark) {
        try {
            // 创建批次记录
            InzSettlementBatch batch = new InzSettlementBatch();
            batch.setBatchNo(InzSettlementBatch.generateBatchNo());
            batch.setBatchName(batchName);
            batch.setBatchStatus(InzSettlementBatch.STATUS_PROCESSING);
            batch.setStartTime(startTime);
            batch.setRemark(remark);
            batch.setCreateTime(new Date());
            batch.setCreateBy("system");

            // 查询符合条件的待结算记录
            QueryWrapper<InzProductSettlement> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("settlement_status", InzProductSettlement.STATUS_PENDING);
            
            if (storeIds != null && !storeIds.isEmpty()) {
                queryWrapper.in("store_id", storeIds);
            }
            
            if (startTime != null) {
                queryWrapper.ge("create_time", startTime);
            }
            
            if (endTime != null) {
                queryWrapper.le("create_time", endTime);
            }

            List<InzProductSettlement> pendingSettlements = productSettlementService.list(queryWrapper);
            
            if (pendingSettlements.isEmpty()) {
                log.warn("没有找到符合条件的待结算记录");
                return null;
            }

            // 计算批次统计信息
            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal totalCommission = BigDecimal.ZERO;
            BigDecimal totalStoreIncome = BigDecimal.ZERO;

            for (InzProductSettlement settlement : pendingSettlements) {
                totalAmount = totalAmount.add(settlement.getSettlementAmount());
                totalCommission = totalCommission.add(settlement.getPlatformCommission());
                totalStoreIncome = totalStoreIncome.add(settlement.getStoreIncome());
            }

            batch.updateStatistics(pendingSettlements.size(), totalAmount, totalCommission, totalStoreIncome);

            // 保存批次
            this.save(batch);

            log.info("创建结算批次成功，批次号：{}，包含{}条记录", batch.getBatchNo(), pendingSettlements.size());
            return batch.getId();

        } catch (Exception e) {
            log.error("创建结算批次失败", e);
            throw new RuntimeException("创建结算批次失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean executeBatchSettlement(String batchId) {
        try {
            InzSettlementBatch batch = this.getById(batchId);
            if (batch == null) {
                log.warn("结算批次不存在：{}", batchId);
                return false;
            }

            if (batch.getBatchStatus() != InzSettlementBatch.STATUS_PROCESSING) {
                log.warn("批次状态不允许执行结算：{}", batchId);
                return false;
            }

            // 查询批次下的待结算记录
            QueryWrapper<InzProductSettlement> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("settlement_status", InzProductSettlement.STATUS_PENDING);
            // 这里可以根据批次创建时的条件来查询相关记录
            
            List<InzProductSettlement> settlements = productSettlementService.list(queryWrapper);
            List<String> settlementIds = new ArrayList<>();
            
            for (InzProductSettlement settlement : settlements) {
                settlementIds.add(settlement.getId());
            }

            if (!settlementIds.isEmpty()) {
                // 执行批量结算
                boolean success = productSettlementService.batchSettle(settlementIds, batch.getBatchName(), batch.getRemark());
                
                if (success) {
                    // 更新批次状态
                    batch.setBatchStatus(InzSettlementBatch.STATUS_COMPLETED);
                    batch.setEndTime(new Date());
                    batch.setUpdateTime(new Date());
                    this.updateById(batch);
                    
                    log.info("执行批次结算成功，批次ID：{}", batchId);
                    return true;
                }
            }

            return false;

        } catch (Exception e) {
            log.error("执行批次结算失败", e);
            throw new RuntimeException("执行批次结算失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelSettlementBatch(String batchId, String reason) {
        try {
            InzSettlementBatch batch = this.getById(batchId);
            if (batch == null) {
                log.warn("结算批次不存在：{}", batchId);
                return false;
            }

            if (batch.getBatchStatus() != InzSettlementBatch.STATUS_PROCESSING) {
                log.warn("批次状态不允许取消：{}", batchId);
                return false;
            }

            batch.setBatchStatus(InzSettlementBatch.STATUS_CANCELLED);
            batch.setRemark(reason);
            batch.setUpdateTime(new Date());

            boolean result = this.updateById(batch);
            
            if (result) {
                log.info("取消结算批次成功，批次ID：{}，原因：{}", batchId, reason);
            }
            
            return result;

        } catch (Exception e) {
            log.error("取消结算批次失败", e);
            throw new RuntimeException("取消结算批次失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getBatchDetail(String batchId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            InzSettlementBatch batch = this.getById(batchId);
            if (batch == null) {
                result.put("success", false);
                result.put("message", "批次不存在");
                return result;
            }

            result.put("success", true);
            result.put("batch", batch);

            // 获取批次下的结算记录（如果批次已完成）
            if (batch.getBatchStatus() == InzSettlementBatch.STATUS_COMPLETED) {
                QueryWrapper<InzProductSettlement> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("settlement_batch_no", batch.getBatchNo());
                List<InzProductSettlement> settlements = productSettlementService.list(queryWrapper);
                result.put("settlements", settlements);
            }

        } catch (Exception e) {
            log.error("获取批次详情失败", e);
            result.put("success", false);
            result.put("message", "获取详情失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getBatchStatistics(String batchId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            InzSettlementBatch batch = this.getById(batchId);
            if (batch == null) {
                result.put("success", false);
                result.put("message", "批次不存在");
                return result;
            }

            result.put("success", true);
            result.put("batchNo", batch.getBatchNo());
            result.put("batchName", batch.getBatchName());
            result.put("settlementCount", batch.getSettlementCount());
            result.put("totalAmount", batch.getTotalAmount());
            result.put("totalCommission", batch.getTotalCommission());
            result.put("totalStoreIncome", batch.getTotalStoreIncome());
            result.put("averageCommissionRate", batch.getAverageCommissionRate());
            result.put("batchStatus", batch.getBatchStatus());
            result.put("batchStatusText", batch.getBatchStatusText());

        } catch (Exception e) {
            log.error("获取批次统计失败", e);
            result.put("success", false);
            result.put("message", "获取统计失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchStatistics(String batchId) {
        try {
            InzSettlementBatch batch = this.getById(batchId);
            if (batch == null) {
                return false;
            }

            // 重新计算批次统计信息
            QueryWrapper<InzProductSettlement> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("settlement_batch_no", batch.getBatchNo());
            List<InzProductSettlement> settlements = productSettlementService.list(queryWrapper);

            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal totalCommission = BigDecimal.ZERO;
            BigDecimal totalStoreIncome = BigDecimal.ZERO;

            for (InzProductSettlement settlement : settlements) {
                totalAmount = totalAmount.add(settlement.getSettlementAmount());
                totalCommission = totalCommission.add(settlement.getPlatformCommission());
                totalStoreIncome = totalStoreIncome.add(settlement.getStoreIncome());
            }

            batch.updateStatistics(settlements.size(), totalAmount, totalCommission, totalStoreIncome);
            batch.setUpdateTime(new Date());

            return this.updateById(batch);

        } catch (Exception e) {
            log.error("更新批次统计失败", e);
            return false;
        }
    }
}
