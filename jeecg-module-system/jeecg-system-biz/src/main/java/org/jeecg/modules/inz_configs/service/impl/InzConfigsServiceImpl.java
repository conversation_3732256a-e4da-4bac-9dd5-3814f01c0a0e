package org.jeecg.modules.inz_configs.service.impl;

import org.jeecg.modules.inz_configs.entity.InzConfigs;
import org.jeecg.modules.inz_configs.mapper.InzConfigsMapper;
import org.jeecg.modules.inz_configs.service.IInzConfigsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 配置信息表
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Service
public class InzConfigsServiceImpl extends ServiceImpl<InzConfigsMapper, InzConfigs> implements IInzConfigsService {

}
