<template>
    <view>
        <!--标题和返回-->
		<cu-custom :bgColor="NavBarColor" isBack :backRouterName="backRouteName">
			<block slot="backText">返回</block>
			<block slot="content">用户表</block>
		</cu-custom>
		 <!--表单区域-->
		<view>
			<form>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">真实姓名：</text></view>
                  <input  placeholder="请输入真实姓名" v-model="model.realName"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">年级：</text></view>
                  <input  placeholder="请输入年级" v-model="model.grade"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">手机号：</text></view>
                  <input  placeholder="请输入手机号" v-model="model.phone"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">密码：</text></view>
                  <input  placeholder="请输入密码" v-model="model.password"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">地址：</text></view>
                  <input  placeholder="请输入地址" v-model="model.address"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">公众号昵称：</text></view>
                  <input  placeholder="请输入公众号昵称" v-model="model.oaNickname"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">公众号 OpenId：</text></view>
                  <input  placeholder="请输入公众号 OpenId" v-model="model.oaOpenid"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">小程序OpenId：</text></view>
                  <input  placeholder="请输入小程序OpenId" v-model="model.mpOpenid"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">企业微信 OpenId：</text></view>
                  <input  placeholder="请输入企业微信 OpenId" v-model="model.ewOpenid"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">联合 Id：</text></view>
                  <input  placeholder="请输入联合 Id" v-model="model.unionId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">角色：</text></view>
                  <input  placeholder="请输入角色" v-model="model.role"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">状态 (1正常 0停用)：</text></view>
                  <input  placeholder="请输入状态 (1正常 0停用)" v-model="model.status"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">备注：</text></view>
                  <input  placeholder="请输入备注" v-model="model.remark"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">头像：</text></view>
                  <input  placeholder="请输入头像" v-model="model.avatar"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">最后一次进入系统的时间：</text></view>
                  <input  placeholder="请输入最后一次进入系统的时间" v-model="model.lastUseAt"/>
                </view>
              </view>
              <my-date label="会员时间：" fields="day" v-model="model.vipTime" placeholder="请输入会员时间"></my-date>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">salt：</text></view>
                  <input  placeholder="请输入salt" v-model="model.salt"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">thirdType：</text></view>
                  <input  placeholder="请输入thirdType" v-model="model.thirdType"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">会员状态 (0-非会员, 1-普通会员, 2-永久会员)：</text></view>
                  <input type="number" placeholder="请输入会员状态 (0-非会员, 1-普通会员, 2-永久会员)" v-model="model.isVip"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">微信OpenID：</text></view>
                  <input  placeholder="请输入微信OpenID" v-model="model.openid"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">微信昵称：</text></view>
                  <input  placeholder="请输入微信昵称" v-model="model.nickname"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">微信头像URL：</text></view>
                  <input  placeholder="请输入微信头像URL" v-model="model.headimgurl"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">性别 (1男 2女 0未选择)：</text></view>
                  <input type="number" placeholder="请输入性别 (1男 2女 0未选择)" v-model="model.gender"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">玩家号：</text></view>
                  <input  placeholder="请输入玩家号" v-model="model.playid"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">用户名：</text></view>
                  <input  placeholder="请输入用户名" v-model="model.username"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">玩家号：</text></view>
                  <input  placeholder="请输入玩家号" v-model="model.playId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">用户积分：</text></view>
                  <input type="number" placeholder="请输入用户积分" v-model="model.points"/>
                </view>
              </view>
				<view class="padding">
					<button class="cu-btn block bg-blue margin-tb-sm lg" @click="onSubmit">
						<text v-if="loading" class="cuIcon-loading2 cuIconfont-spin"></text>提交
					</button>
				</view>
			</form>
		</view>
    </view>
</template>

<script>
    import myDate from '@/components/my-componets/my-date.vue'

    export default {
        name: "InzUsersFrontsForm",
        components:{ myDate },
        props:{
          formData:{
              type:Object,
              default:()=>{},
              required:false
          }
        },
        data(){
            return {
				CustomBar: this.CustomBar,
				NavBarColor: this.NavBarColor,
				loading:false,
                model: {},
                backRouteName:'index',
                url: {
                  queryById: "/inz_users_fronts/inzUsersFronts/queryById",
                  add: "/inz_users_fronts/inzUsersFronts/add",
                  edit: "/inz_users_fronts/inzUsersFronts/edit",
                },
            }
        },
        created(){
             this.initFormData();
        },
        methods:{
           initFormData(){
               if(this.formData){
                    let dataId = this.formData.dataId;
                    this.$http.get(this.url.queryById,{params:{id:dataId}}).then((res)=>{
                        if(res.data.success){
                            console.log("表单数据",res);
                            this.model = res.data.result;
                        }
                    })
                }
            },
            onSubmit() {
                let myForm = {...this.model};
                this.loading = true;
                let url = myForm.id?this.url.edit:this.url.add;
				this.$http.post(url,myForm).then(res=>{
				   console.log("res",res)
				   this.loading = false
				   this.$Router.push({name:this.backRouteName})
				}).catch(()=>{
					this.loading = false
				});
            }
        }
    }
</script>
