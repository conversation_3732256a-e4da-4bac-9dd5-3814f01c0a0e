package org.jeecg.modules.auditRecord.service;

import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.auditRecord.entity.AuditRecord;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 审核表
 * @Author: jeecg-boot
 * @Date:   2025-06-21
 * @Version: V1.0
 */
public interface IAuditRecordService extends IService<AuditRecord> {

    /**
     * 提交审核
     * @param businessId 业务ID
     * @param auditType 审核类型
     * @return 操作结果
     */
    Result<AuditRecord> submitForAudit(String businessId, Integer auditType);

    /**
     * 执行审核
     * @param auditRecordId 审核记录ID
     * @param status 审核状态（1：通过，2：驳回）
     * @param comments 审核意见
     * @return 操作结果
     */
    Result<AuditRecord> performAudit(String auditRecordId, Integer status, String comments);

    /**
     * 提交商品上架审核
     * @param productId 商品ID
     * @return 操作结果
     */
    Result<AuditRecord> submitProductForAudit(String productId);
    
    /**
     * 提交售后申请审核
     * @param aftersaleId 售后ID
     * @return 操作结果
     */
    Result<AuditRecord> submitAftersaleForAudit(String aftersaleId);
    
    /**
     * 根据商品ID查询审核记录
     * @param productId 商品ID
     * @return 审核记录
     */
    AuditRecord getAuditRecordByProductId(String productId);
    
    /**
     * 根据售后ID查询审核记录
     * @param aftersaleId 售后ID
     * @return 审核记录
     */
    AuditRecord getAuditRecordByAftersaleId(String aftersaleId);

    /**
     * 取消商品审核
     * @param productId 商品ID
     * @return 操作结果
     */
    Result<String> cancelProductAudit(String productId);
    
    /**
     * 取消售后审核
     * @param aftersaleId 售后ID
     * @return 操作结果
     */
    Result<String> cancelAftersaleAudit(String aftersaleId);
}
