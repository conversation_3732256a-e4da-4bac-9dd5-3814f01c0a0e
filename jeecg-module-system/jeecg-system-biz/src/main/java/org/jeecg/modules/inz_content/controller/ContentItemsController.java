package org.jeecg.modules.inz_content.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_content.entity.ContentItems;
import org.jeecg.modules.inz_content.service.IContentItemsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 内容项表
 * @Author: jeecg-boot
 * @Date:   2025-06-15
 * @Version: V1.0
 */
/*@Api(tags="内容项表")*/
@RestController
@RequestMapping("/inz_content/contentItems")
@Slf4j
public class ContentItemsController extends JeecgController<ContentItems, IContentItemsService> {
	@Autowired
	private IContentItemsService contentItemsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param contentItems
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "内容项表-分页列表查询")
	/*@ApiOperation(value="内容项表-分页列表查询", notes="内容项表-分页列表查询")*/
	@GetMapping(value = "/list")
	public Result<IPage<ContentItems>> queryPageList(ContentItems contentItems,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<ContentItems> queryWrapper = QueryGenerator.initQueryWrapper(contentItems, req.getParameterMap());
		Page<ContentItems> page = new Page<ContentItems>(pageNo, pageSize);
		IPage<ContentItems> pageList = contentItemsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param contentItems
	 * @return
	 */
	/*@AutoLog(value = "内容项表-添加")
	@ApiOperation(value="内容项表-添加", notes="内容项表-添加")*/
	@RequiresPermissions("inz_content:content_item:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ContentItems contentItems) {
		contentItemsService.save(contentItems);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param contentItems
	 * @return
	 */
	/*@AutoLog(value = "内容项表-编辑")
	@ApiOperation(value="内容项表-编辑", notes="内容项表-编辑")*/
	@RequiresPermissions("inz_content:content_item:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ContentItems contentItems) {
		contentItemsService.updateById(contentItems);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	/*@AutoLog(value = "内容项表-通过id删除")
	@ApiOperation(value="内容项表-通过id删除", notes="内容项表-通过id删除")*/
	@RequiresPermissions("inz_content:content_item:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		contentItemsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	/*@AutoLog(value = "内容项表-批量删除")
	@ApiOperation(value="内容项表-批量删除", notes="内容项表-批量删除")*/
	@RequiresPermissions("inz_content:content_item:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.contentItemsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "内容项表-通过id查询")
	/*@ApiOperation(value="内容项表-通过id查询", notes="内容项表-通过id查询")*/
	@GetMapping(value = "/queryById")
	public Result<ContentItems> queryById(@RequestParam(name="id",required=true) String id) {
		ContentItems contentItems = contentItemsService.getById(id);
		if(contentItems==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(contentItems);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param contentItems
    */
    @RequiresPermissions("inz_content:content_item:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ContentItems contentItems) {
        return super.exportXls(request, contentItems, ContentItems.class, "内容项表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("inz_content:content_item:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ContentItems.class);
    }

}
