package org.jeecg.modules.inz_product_settlement.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.inz_product_settlement.entity.InzSettlementBatch;

/**
 * @Description: 结算批次
 * @Author: jeecg-boot
 * @Date: 2025-06-26
 * @Version: V1.0
 */
public interface InzSettlementBatchMapper extends BaseMapper<InzSettlementBatch> {

    /**
     * 获取批次统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_batch_count, " +
            "SUM(CASE WHEN batch_status = 1 THEN 1 ELSE 0 END) as processing_count, " +
            "SUM(CASE WHEN batch_status = 2 THEN 1 ELSE 0 END) as completed_count, " +
            "SUM(CASE WHEN batch_status = 3 THEN 1 ELSE 0 END) as cancelled_count, " +
            "SUM(total_amount) as total_settlement_amount, " +
            "SUM(total_commission) as total_platform_commission " +
            "FROM inz_settlement_batch " +
            "WHERE create_time BETWEEN #{startTime} AND #{endTime}")
    Map<String, Object> getBatchOverallStatistics(@Param("startTime") String startTime, 
                                                  @Param("endTime") String endTime);

    /**
     * 获取批次处理效率统计
     */
    @Select("SELECT " +
            "batch_no, " +
            "batch_name, " +
            "settlement_count, " +
            "total_amount, " +
            "TIMESTAMPDIFF(MINUTE, start_time, end_time) as processing_minutes, " +
            "ROUND(settlement_count / TIMESTAMPDIFF(MINUTE, start_time, end_time), 2) as efficiency_rate " +
            "FROM inz_settlement_batch " +
            "WHERE batch_status = 2 " +
            "AND start_time IS NOT NULL " +
            "AND end_time IS NOT NULL " +
            "AND create_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY efficiency_rate DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getBatchEfficiencyRanking(@Param("startTime") String startTime, 
                                                        @Param("endTime") String endTime, 
                                                        @Param("limit") Integer limit);

    /**
     * 获取批次金额排行榜
     */
    @Select("SELECT " +
            "batch_no, " +
            "batch_name, " +
            "settlement_count, " +
            "total_amount, " +
            "total_commission, " +
            "total_store_income, " +
            "create_time " +
            "FROM inz_settlement_batch " +
            "WHERE batch_status = 2 " +
            "AND create_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY total_amount DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getBatchAmountRanking(@Param("startTime") String startTime, 
                                                    @Param("endTime") String endTime, 
                                                    @Param("limit") Integer limit);

    /**
     * 获取批次趋势数据
     */
    @Select("SELECT " +
            "DATE(create_time) as batch_date, " +
            "COUNT(*) as daily_batch_count, " +
            "SUM(settlement_count) as daily_settlement_count, " +
            "SUM(total_amount) as daily_total_amount, " +
            "AVG(total_amount) as daily_avg_amount " +
            "FROM inz_settlement_batch " +
            "WHERE batch_status = 2 " +
            "AND create_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY DATE(create_time) " +
            "ORDER BY batch_date")
    List<Map<String, Object>> getBatchTrendData(@Param("startTime") String startTime, 
                                               @Param("endTime") String endTime);

    /**
     * 获取最近的批次列表
     */
    @Select("SELECT " +
            "id, batch_no, batch_name, settlement_count, total_amount, " +
            "batch_status, create_time, end_time " +
            "FROM inz_settlement_batch " +
            "ORDER BY create_time DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getRecentBatches(@Param("limit") Integer limit);

    /**
     * 检查批次号是否存在
     */
    @Select("SELECT COUNT(*) FROM inz_settlement_batch WHERE batch_no = #{batchNo}")
    int checkBatchNoExists(@Param("batchNo") String batchNo);
}
