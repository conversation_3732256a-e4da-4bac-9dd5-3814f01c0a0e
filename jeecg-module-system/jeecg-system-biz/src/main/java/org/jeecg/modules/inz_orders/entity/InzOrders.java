package org.jeecg.modules.inz_orders.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 订单表
 * @Author: jeecg-boot
 * @Date:   2025-06-20
 * @Version: V1.0
 */
@Data
@TableName("inz_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_order对象", description="订单表")
public class InzOrders implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**订单编号*/
	@Excel(name = "订单编号", width = 15)
    @ApiModelProperty(value = "订单编号")
    private java.lang.String orderNo;
	/**用户ID*/
	@Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private java.lang.String userId;
	/**店铺ID*/
	@Excel(name = "店铺ID", width = 15)
    @ApiModelProperty(value = "店铺ID")
    private java.lang.String storeId;
	/**订单类型(1:金钱订单,2:积分订单,3:混合支付)*/
	@Excel(name = "订单类型(1:金钱订单,2:积分订单,3:混合支付)", width = 15)
    @ApiModelProperty(value = "订单类型(1:金钱订单,2:积分订单,3:混合支付)")
    private java.lang.Integer orderType;
	/**订单总金额*/
	@Excel(name = "订单总金额", width = 15)
    @ApiModelProperty(value = "订单总金额")
    private java.math.BigDecimal totalPrice;
	/**实付金额*/
	@Excel(name = "实付金额", width = 15)
    @ApiModelProperty(value = "实付金额")
    private java.math.BigDecimal payPrice;
	/**使用的积分数量*/
	@Excel(name = "使用的积分数量", width = 15)
    @ApiModelProperty(value = "使用的积分数量")
    private java.lang.Integer pointAmount;
	/**优惠金额*/
	@Excel(name = "优惠金额", width = 15)
    @ApiModelProperty(value = "优惠金额")
    private java.math.BigDecimal discountAmount;
	/**运费*/
	@Excel(name = "运费", width = 15)
    @ApiModelProperty(value = "运费")
    private java.math.BigDecimal freightAmount;
	/**订单状态(1:未支付,2:已支付待发货,3:已发货待收货,4:已完成,5:已取消,6:已退款)*/
	@Excel(name = "订单状态(1:未支付,2:已支付待发货,3:已发货待收货,4:已完成,5:已取消,6:已退款)", width = 15)
    @ApiModelProperty(value = "订单状态(1:未支付,2:已支付待发货,3:已发货待收货,4:已完成,5:已取消,6:已退款)")
    private java.lang.Integer orderStatus;
	/**支付时间*/
	@Excel(name = "支付时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "支付时间")
    private java.util.Date payTime;
	/**支付方式(1-支付宝,2-微信)*/
	@Excel(name = "支付方式(1-支付宝,2-微信)", width = 15)
    @ApiModelProperty(value = "支付方式(1-支付宝,2-微信)")
    private java.lang.Integer payType;
	/**支付流水号*/
	@Excel(name = "支付流水号", width = 15)
    @ApiModelProperty(value = "支付流水号")
    private java.lang.String transactionId;
	/**收货人姓名*/
	@Excel(name = "收货人姓名", width = 15)
    @ApiModelProperty(value = "收货人姓名")
    private java.lang.String receiverName;
	/**收货人电话*/
	@Excel(name = "收货人电话", width = 15)
    @ApiModelProperty(value = "收货人电话")
    private java.lang.String receiverPhone;
	/**收货地址*/
	@Excel(name = "收货地址", width = 15)
    @ApiModelProperty(value = "收货地址")
    private java.lang.String receiverAddress;
	/**订单备注*/
	@Excel(name = "订单备注", width = 15)
    @ApiModelProperty(value = "订单备注")
    private java.lang.String remark;
	/**退款状态(1-未操作退款,2-用户已提交，未退款,3-退款中,4-退款成功,5-退款失败,6-退款驳回)*/
	@Excel(name = "退款状态(1-未操作退款,2-用户已提交，未退款,3-退款中,4-退款成功,5-退款失败,6-退款驳回)", width = 15)
    @ApiModelProperty(value = "退款状态(1-未操作退款,2-用户已提交，未退款,3-退款中,4-退款成功,5-退款失败,6-退款驳回)")
    private java.lang.Integer refundStatus;
	/**退款时间*/
	@Excel(name = "退款时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "退款时间")
    private java.util.Date refundTime;
	/**支付prepay_id*/
	@Excel(name = "支付prepay_id", width = 15)
    @ApiModelProperty(value = "支付prepay_id")
    private java.lang.String prepayId;
	/**物流单号*/
	@Excel(name = "物流单号", width = 15)
    @ApiModelProperty(value = "物流单号")
    private java.lang.String trackingNo;
	/**物流公司*/
	@Excel(name = "物流公司", width = 15)
    @ApiModelProperty(value = "物流公司")
    private java.lang.String expressCompany;
	/**物流公司编码*/
	@Excel(name = "物流公司编码", width = 15)
    @ApiModelProperty(value = "物流公司编码")
    private java.lang.String expressCode;
	/**发货时间*/
	@Excel(name = "发货时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发货时间")
    private java.util.Date shipTime;
	/**完成时间*/
	@Excel(name = "完成时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "完成时间")
    private java.util.Date finishTime;
}
