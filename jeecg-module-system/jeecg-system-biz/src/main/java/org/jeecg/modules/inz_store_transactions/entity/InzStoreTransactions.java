package org.jeecg.modules.inz_store_transactions.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 店铺资金流水
 * @Author: jeecg-boot
 * @Date:   2025-06-26
 * @Version: V1.0
 */
@Data
@TableName("inz_store_transaction")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="店铺资金流水对象", description="店铺资金流水")
public class InzStoreTransactions implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**所屬部门*/
    @ApiModelProperty(value = "所屬部门")
    private java.lang.String sysOrgCode;
	/**店鋪ID*/
	@Excel(name = "店鋪ID", width = 15)
    @ApiModelProperty(value = "店鋪ID")
    private java.lang.String storeId;
	/**流水号*/
	@Excel(name = "流水号", width = 15)
    @ApiModelProperty(value = "流水号")
    private java.lang.String transactionNo;
	/**関聯訂単ID*/
	@Excel(name = "関聯訂単ID", width = 15)
    @ApiModelProperty(value = "関聯訂単ID")
    private java.lang.String orderId;
	/**関聯訂単号*/
	@Excel(name = "関聯訂単号", width = 15)
    @ApiModelProperty(value = "関聯訂単号")
    private java.lang.String orderNo;
	/**交易类型：1-訂単收入，2-退款，3-提現，4-人工調整，5-平台傭金*/
	@Excel(name = "交易类型：1-訂単收入，2-退款，3-提現，4-人工調整，5-平台傭金", width = 15)
    @ApiModelProperty(value = "交易类型：1-訂単收入，2-退款，3-提現，4-人工調整，5-平台傭金")
    private java.lang.Integer type;

    /**交易类型字符串：INCOME-收入，EXPENSE-支出，REFUND-退款，WITHDRAWAL-提现，ADJUSTMENT-调整，COMMISSION-佣金*/
	@Excel(name = "交易类型字符串", width = 15)
    @ApiModelProperty(value = "交易类型字符串：INCOME-收入，EXPENSE-支出，REFUND-退款，WITHDRAWAL-提现，ADJUSTMENT-调整，COMMISSION-佣金")
    private java.lang.String transactionType;

    /**交易时间*/
	@Excel(name = "交易时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "交易时间")
    private java.util.Date transactionTime;

	/**交易金額*/
	@Excel(name = "交易金額", width = 15)
    @ApiModelProperty(value = "交易金額")
    private java.math.BigDecimal amount;
	/**交易前余額*/
	@Excel(name = "交易前余額", width = 15)
    @ApiModelProperty(value = "交易前余額")
    private java.math.BigDecimal beforeBalance;

    /**交易前余額（兼容字段）*/
	@Excel(name = "交易前余額", width = 15)
    @ApiModelProperty(value = "交易前余額")
    private java.math.BigDecimal balanceBefore;

	/**交易後余額*/
	@Excel(name = "交易後余額", width = 15)
    @ApiModelProperty(value = "交易後余額")
    private java.math.BigDecimal afterBalance;

    /**交易後余額（兼容字段）*/
	@Excel(name = "交易後余額", width = 15)
    @ApiModelProperty(value = "交易後余額")
    private java.math.BigDecimal balanceAfter;

    /**交易状态：PENDING-待处理，SUCCESS-成功，FAILED-失败，CANCELLED-已取消*/
	@Excel(name = "交易状态", width = 15)
    @ApiModelProperty(value = "交易状态：PENDING-待处理，SUCCESS-成功，FAILED-失败，CANCELLED-已取消")
    private java.lang.String status;

    /**支付方式*/
	@Excel(name = "支付方式", width = 15)
    @ApiModelProperty(value = "支付方式")
    private java.lang.String paymentMethod;

    /**第三方交易号*/
	@Excel(name = "第三方交易号", width = 15)
    @ApiModelProperty(value = "第三方交易号")
    private java.lang.String thirdPartyTransactionId;
	/**備註*/
	@Excel(name = "備註", width = 15)
    @ApiModelProperty(value = "備註")
    private java.lang.String remark;
	/**第三方交易ID*/
	@Excel(name = "第三方交易ID", width = 15)
    @ApiModelProperty(value = "第三方交易ID")
    private java.lang.String externalId;
	/**操作人*/
	@Excel(name = "操作人", width = 15)
    @ApiModelProperty(value = "操作人")
    private java.lang.String operator;
	/**完成时间*/
	@Excel(name = "完成时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "完成时间")
    private java.util.Date completeTime;
}
