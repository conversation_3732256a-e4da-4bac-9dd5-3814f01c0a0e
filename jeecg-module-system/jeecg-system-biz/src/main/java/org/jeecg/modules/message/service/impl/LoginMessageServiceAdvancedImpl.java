package org.jeecg.modules.message.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.message.service.ILoginMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description: 登录后消息发送服务高级实现（支持数据库配置）
 * @Author: jeecg-boot
 * @Date: 2025-01-24
 * @Version: V1.0
 */
@Service("loginMessageServiceAdvanced")
@Slf4j
public class LoginMessageServiceAdvancedImpl implements ILoginMessageService {

    @Autowired
    private RedisUtil redisUtil;

    // Redis缓存键前缀
    private static final String LOGIN_MESSAGE_CACHE_PREFIX = "CardVerse:login_message:";
    private static final String FIRST_LOGIN_CACHE_PREFIX = "CardVerse:first_login:";

    @Override
    public void sendWelcomeMessage(String userId, String username, boolean isFirstLogin) {
        String templateCode = isFirstLogin ? "WELCOME_MESSAGE" : "FIRST_LOGIN_WELCOME";
        sendMessageByTemplate(userId, username, templateCode, "welcome");
    }

    @Override
    public void sendNewUserWelcomeMessage(String userId, String username) {
        sendMessageByTemplate(userId, username, "NEW_USER_GUIDE", "new_user");
    }

    @Override
    public void sendAnnouncementMessage(String userId, String username) {
        sendMessageByTemplate(userId, username, "PLATFORM_ANNOUNCEMENT", "announcement");
    }

    @Override
    public void sendReportRewardMessage(String userId, String username) {
        sendMessageByTemplate(userId, username, "REPORT_REWARD", "report_reward");
    }

    @Override
    public void sendPlatformRulesMessage(String userId, String username) {
        sendMessageByTemplate(userId, username, "PLATFORM_RULES", "rules");
    }

    @Override
    public void sendSecurityReminderMessage(String userId, String username) {
        sendMessageByTemplate(userId, username, "SECURITY_REMINDER", "security");
    }

    @Override
    public void sendAllLoginMessages(String userId, String username, boolean isFirstLogin) {
        log.info("开始为用户 {} 发送登录后自动消息（高级版本），首次登录: {}", username, isFirstLogin);
        
        try {
            // 检查全局开关
            if (!isLoginMessageEnabled()) {
                log.info("登录消息发送功能已关闭，跳过发送");
                return;
            }

            // 获取所有启用的消息模板
            List<Object> templates = getEnabledMessageTemplates();
            
            for (Object template : templates) {
                try {
                    // 通过反射获取模板信息
                    String templateCode = (String) template.getClass().getMethod("getTemplateCode").invoke(template);
                    String sendCondition = (String) template.getClass().getMethod("getSendCondition").invoke(template);
                    Integer cacheHours = (Integer) template.getClass().getMethod("getCacheHours").invoke(template);
                    
                    // 判断发送条件
                    boolean shouldSend = false;
                    if ("every_login".equals(sendCondition)) {
                        shouldSend = true;
                    } else if ("first_login".equals(sendCondition) && isFirstLogin) {
                        shouldSend = true;
                    } else if ("register".equals(sendCondition) && isFirstLogin) {
                        shouldSend = true;
                    }
                    
                    if (shouldSend) {
                        sendMessageByTemplateObject(userId, username, template, templateCode, cacheHours);
                    }
                } catch (Exception e) {
                    log.error("处理消息模板失败: {}", e.getMessage(), e);
                }
            }
            
            // 标记为首次登录用户
            if (isFirstLogin) {
                String cacheKey = FIRST_LOGIN_CACHE_PREFIX + userId;
                redisUtil.set(cacheKey, "first_login", 30 * 24 * 60 * 60); // 30天过期
            }
            
            log.info("完成为用户 {} 发送登录后自动消息（高级版本）", username);
        } catch (Exception e) {
            log.error("发送登录后自动消息失败（高级版本），用户: {}, 错误: {}", username, e.getMessage(), e);
        }
    }

    /**
     * 根据模板发送消息
     */
    private void sendMessageByTemplate(String userId, String username, String templateCode, String cacheType) {
        try {
            // 获取消息模板
            Object template = getMessageTemplate(templateCode);
            if (template == null) {
                log.warn("未找到消息模板: {}", templateCode);
                return;
            }

            // 获取模板信息
            Integer cacheHours = (Integer) template.getClass().getMethod("getCacheHours").invoke(template);
            Boolean isEnabled = (Boolean) template.getClass().getMethod("getIsEnabled").invoke(template);
            
            if (!isEnabled) {
                log.info("消息模板 {} 已禁用，跳过发送", templateCode);
                return;
            }

            sendMessageByTemplateObject(userId, username, template, templateCode, cacheHours);
            
        } catch (Exception e) {
            log.error("根据模板发送消息失败，模板: {}, 用户: {}, 错误: {}", templateCode, username, e.getMessage(), e);
        }
    }

    /**
     * 根据模板对象发送消息
     */
    private void sendMessageByTemplateObject(String userId, String username, Object template, String templateCode, Integer cacheHours) {
        try {
            String cacheKey = LOGIN_MESSAGE_CACHE_PREFIX + templateCode.toLowerCase() + ":" + userId;
            
            // 检查缓存，防止重复发送
            if (redisUtil.hasKey(cacheKey)) {
                log.debug("用户 {} 在缓存时间内已发送过模板 {} 的消息，跳过发送", username, templateCode);
                return;
            }

            // 获取模板内容
            String title = (String) template.getClass().getMethod("getTitle").invoke(template);
            String content = (String) template.getClass().getMethod("getContent").invoke(template);
            Integer messageType = (Integer) template.getClass().getMethod("getMessageType").invoke(template);
            
            // 发送消息
            sendMessageToUser(userId, messageType, title, content, null);

            // 设置缓存
            int cacheSeconds = (cacheHours != null ? cacheHours : 24) * 60 * 60;
            redisUtil.set(cacheKey, "sent", cacheSeconds);
            
            // 记录发送日志
            recordMessageLog(userId, templateCode, null, true, null);
            
            log.info("成功发送模板消息给用户: {}, 模板: {}, 标题: {}", username, templateCode, title);
        } catch (Exception e) {
            log.error("发送模板消息失败，模板: {}, 用户: {}, 错误: {}", templateCode, username, e.getMessage(), e);
            // 记录失败日志
            recordMessageLog(userId, templateCode, null, false, e.getMessage());
        }
    }

    /**
     * 获取消息模板
     */
    private Object getMessageTemplate(String templateCode) {
        try {
            Object templateService = SpringContextUtils.getBean("inzLoginMessageTemplateServiceImpl");
            if (templateService != null) {
                // 构建查询条件
                QueryWrapper queryWrapper = new QueryWrapper();
                queryWrapper.eq("template_code", templateCode);
                queryWrapper.eq("is_enabled", 1);
                
                return templateService.getClass().getMethod("getOne", QueryWrapper.class).invoke(templateService, queryWrapper);
            }
        } catch (Exception e) {
            log.error("获取消息模板失败，模板编码: {}, 错误: {}", templateCode, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取所有启用的消息模板
     */
    private List<Object> getEnabledMessageTemplates() {
        try {
            Object templateService = SpringContextUtils.getBean("inzLoginMessageTemplateServiceImpl");
            if (templateService != null) {
                QueryWrapper queryWrapper = new QueryWrapper();
                queryWrapper.eq("is_enabled", 1);
                queryWrapper.orderByDesc("priority");
                
                return (List<Object>) templateService.getClass().getMethod("list", QueryWrapper.class).invoke(templateService, queryWrapper);
            }
        } catch (Exception e) {
            log.error("获取启用的消息模板失败: {}", e.getMessage(), e);
        }
        return new ArrayList<>(); // 返回空列表
    }

    /**
     * 检查登录消息是否启用
     */
    private boolean isLoginMessageEnabled() {
        try {
            Object configService = SpringContextUtils.getBean("inzConfigServiceImpl");
            if (configService != null) {
                Object config = configService.getClass().getMethod("getByCode", String.class).invoke(configService, "LOGIN_MESSAGE_ENABLED");
                if (config != null) {
                    String value = (String) config.getClass().getMethod("getValue").invoke(config);
                    return "1".equals(value);
                }
            }
        } catch (Exception e) {
            log.error("检查登录消息开关失败: {}", e.getMessage(), e);
        }
        return true; // 默认启用
    }

    /**
     * 发送消息到用户消息表
     */
    private void sendMessageToUser(String userId, Integer type, String title, String content, String relationId) {
        try {
            // 通过反射调用Message相关的服务
            Object messageService = SpringContextUtils.getBean("messageServiceImpl");
            if (messageService != null) {
                // 创建Message对象
                Class<?> messageClass = Class.forName("org.jeecg.modules.api.message.entity.Message");
                Object message = messageClass.newInstance();
                
                // 设置消息属性
                messageClass.getMethod("setUserId", String.class).invoke(message, userId);
                messageClass.getMethod("setType", Integer.class).invoke(message, type);
                messageClass.getMethod("setTitle", String.class).invoke(message, title);
                messageClass.getMethod("setContent", String.class).invoke(message, content);
                messageClass.getMethod("setRelationId", String.class).invoke(message, relationId);
                messageClass.getMethod("setStatus", Integer.class).invoke(message, 0); // 未读
                messageClass.getMethod("setCreateTime", Date.class).invoke(message, new Date());
                messageClass.getMethod("setCreateBy", String.class).invoke(message, "system");
                
                // 调用保存方法
                messageService.getClass().getMethod("createMessage", messageClass).invoke(messageService, message);
                
                log.debug("成功发送消息到用户 {}: {}", userId, title);
            } else {
                log.warn("未找到messageService，无法发送消息");
            }
        } catch (Exception e) {
            log.error("发送消息到用户失败，用户ID: {}, 标题: {}, 错误: {}", userId, title, e.getMessage(), e);
        }
    }

    /**
     * 记录消息发送日志
     */
    private void recordMessageLog(String userId, String templateCode, String messageId, boolean success, String errorMessage) {
        try {
            Object logService = SpringContextUtils.getBean("inzLoginMessageLogServiceImpl");
            if (logService != null) {
                Class<?> logClass = Class.forName("org.jeecg.modules.message.entity.InzLoginMessageLog");
                Object log = logClass.newInstance();
                
                // 设置日志属性
                logClass.getMethod("setUserId", String.class).invoke(log, userId);
                logClass.getMethod("setTemplateCode", String.class).invoke(log, templateCode);
                logClass.getMethod("setMessageId", String.class).invoke(log, messageId);
                logClass.getMethod("setSendTime", Date.class).invoke(log, new Date());
                logClass.getMethod("setSendResult", Integer.class).invoke(log, success ? 1 : 0);
                logClass.getMethod("setErrorMessage", String.class).invoke(log, errorMessage);
                logClass.getMethod("setCreateTime", Date.class).invoke(log, new Date());
                
                // 保存日志
                logService.getClass().getMethod("save", Object.class).invoke(logService, log);
            }
        } catch (Exception e) {
            log.error("记录消息发送日志失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查是否为首次登录
     */
    public boolean isFirstLogin(String userId) {
        String cacheKey = FIRST_LOGIN_CACHE_PREFIX + userId;
        return !redisUtil.hasKey(cacheKey);
    }
}
