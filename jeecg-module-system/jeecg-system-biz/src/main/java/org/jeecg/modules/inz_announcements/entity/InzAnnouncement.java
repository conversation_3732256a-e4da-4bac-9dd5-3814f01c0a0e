package org.jeecg.modules.inz_announcements.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 公告表
 * @Author: jeecg-boot
 * @Date:   2025-06-24
 * @Version: V1.0
 */
@Data
@TableName("inz_announcement")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_announcement对象", description="公告表")
public class InzAnnouncement implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**公告标题*/
	@Excel(name = "公告标题", width = 15)
    @ApiModelProperty(value = "公告标题")
    private java.lang.String title;
	/**公告内容*/
	@Excel(name = "公告内容", width = 15)
    @ApiModelProperty(value = "公告内容")
    private java.lang.String content;
	/**公告类型 1-系统公告 2-活动公告 3-维护公告 4-其他*/
	@Excel(name = "公告类型 1-系统公告 2-活动公告 3-维护公告 4-其他", width = 15)
    @ApiModelProperty(value = "公告类型 1-系统公告 2-活动公告 3-维护公告 4-其他")
    private java.lang.Integer type;
	/**发布状态 0-草稿 1-已发布 2-已下线*/
	@Excel(name = "发布状态 0-草稿 1-已发布 2-已下线", width = 15)
    @ApiModelProperty(value = "发布状态 0-草稿 1-已发布 2-已下线")
    private java.lang.Integer status;
	/**是否置顶 0-否 1-是*/
	@Excel(name = "是否置顶 0-否 1-是", width = 15)
    @ApiModelProperty(value = "是否置顶 0-否 1-是")
    private java.lang.Integer isTop;
	/**优先级 数字越大优先级越高*/
	@Excel(name = "优先级 数字越大优先级越高", width = 15)
    @ApiModelProperty(value = "优先级 数字越大优先级越高")
    private java.lang.Integer priority;
	/**发布时间*/
	@Excel(name = "发布时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发布时间")
    private java.util.Date publishTime;
	/**生效时间*/
	@Excel(name = "生效时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "生效时间")
    private java.util.Date startTime;
	/**失效时间*/
	@Excel(name = "失效时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "失效时间")
    private java.util.Date endTime;
	/**阅读次数*/
	@Excel(name = "阅读次数", width = 15)
    @ApiModelProperty(value = "阅读次数")
    private java.lang.Integer readCount;
	/**封面图片*/
	@Excel(name = "封面图片", width = 15)
    @ApiModelProperty(value = "封面图片")
    private java.lang.String coverImage;
	/**是否发送通知 0-否 1-是*/
	@Excel(name = "是否发送通知 0-否 1-是", width = 15)
    @ApiModelProperty(value = "是否发送通知 0-否 1-是")
    private java.lang.Integer sendNotification;
	/**目标用户类型 0-全部用户 1-普通用户 2-店主用户 3-VIP用户*/
	@Excel(name = "目标用户类型 0-全部用户 1-普通用户 2-店主用户 3-VIP用户", width = 15)
    @ApiModelProperty(value = "目标用户类型 0-全部用户 1-普通用户 2-店主用户 3-VIP用户")
    private java.lang.Integer targetUserType;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
}
