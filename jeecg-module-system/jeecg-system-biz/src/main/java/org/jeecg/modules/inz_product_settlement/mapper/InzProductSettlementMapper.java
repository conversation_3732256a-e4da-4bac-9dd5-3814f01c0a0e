package org.jeecg.modules.inz_product_settlement.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.inz_product_settlement.entity.InzProductSettlement;

/**
 * @Description: 商品结算
 * @Author: jeecg-boot
 * @Date: 2025-06-26
 * @Version: V1.0
 */
public interface InzProductSettlementMapper extends BaseMapper<InzProductSettlement> {

    /**
     * 获取店铺结算统计
     */
    @Select("SELECT " +
            "store_id, " +
            "store_name, " +
            "COUNT(*) as settlement_count, " +
            "SUM(settlement_amount) as total_settlement_amount, " +
            "SUM(platform_commission) as total_platform_commission, " +
            "SUM(store_income) as total_store_income, " +
            "AVG(commission_rate) as avg_commission_rate " +
            "FROM inz_product_settlement " +
            "WHERE store_id = #{storeId} " +
            "AND settlement_status = 2 " +
            "AND settlement_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY store_id, store_name")
    Map<String, Object> getStoreSettlementStatistics(@Param("storeId") String storeId, 
                                                     @Param("startTime") String startTime, 
                                                     @Param("endTime") String endTime);

    /**
     * 获取平台结算统计
     */
    @Select("SELECT " +
            "COUNT(*) as total_settlement_count, " +
            "COUNT(DISTINCT store_id) as total_store_count, " +
            "SUM(settlement_amount) as total_settlement_amount, " +
            "SUM(platform_commission) as total_platform_commission, " +
            "SUM(store_income) as total_store_income, " +
            "AVG(commission_rate) as avg_commission_rate " +
            "FROM inz_product_settlement " +
            "WHERE settlement_status = 2 " +
            "AND settlement_time BETWEEN #{startTime} AND #{endTime}")
    Map<String, Object> getPlatformSettlementStatistics(@Param("startTime") String startTime, 
                                                        @Param("endTime") String endTime);

    /**
     * 获取店铺佣金排行榜
     */
    @Select("SELECT " +
            "store_id, " +
            "store_name, " +
            "SUM(platform_commission) as total_commission, " +
            "COUNT(*) as settlement_count " +
            "FROM inz_product_settlement " +
            "WHERE settlement_status = 2 " +
            "AND settlement_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY store_id, store_name " +
            "ORDER BY total_commission DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getStoreCommissionRanking(@Param("startTime") String startTime, 
                                                        @Param("endTime") String endTime, 
                                                        @Param("limit") Integer limit);

    /**
     * 批量更新结算状态
     */
    @Update("UPDATE inz_product_settlement " +
            "SET settlement_status = #{status}, " +
            "settlement_time = NOW(), " +
            "settlement_batch_no = #{batchNo}, " +
            "update_by = #{updateBy}, " +
            "update_time = NOW() " +
            "WHERE id IN (${settlementIds}) " +
            "AND settlement_status = 1")
    int batchUpdateSettlementStatus(@Param("settlementIds") String settlementIds, 
                                   @Param("status") Integer status, 
                                   @Param("batchNo") String batchNo, 
                                   @Param("updateBy") String updateBy);

    /**
     * 获取待结算记录统计
     */
    @Select("SELECT " +
            "COUNT(*) as pending_count, " +
            "SUM(settlement_amount) as pending_amount, " +
            "SUM(platform_commission) as pending_commission " +
            "FROM inz_product_settlement " +
            "WHERE settlement_status = 1 " +
            "AND store_id = #{storeId}")
    Map<String, Object> getPendingSettlementStatistics(@Param("storeId") String storeId);

    /**
     * 获取结算趋势数据
     */
    @Select("SELECT " +
            "DATE(settlement_time) as settlement_date, " +
            "COUNT(*) as daily_count, " +
            "SUM(settlement_amount) as daily_amount, " +
            "SUM(platform_commission) as daily_commission " +
            "FROM inz_product_settlement " +
            "WHERE settlement_status = 2 " +
            "AND settlement_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY DATE(settlement_time) " +
            "ORDER BY settlement_date")
    List<Map<String, Object>> getSettlementTrendData(@Param("startTime") String startTime, 
                                                     @Param("endTime") String endTime);

    /**
     * 检查订单是否已存在结算记录
     */
    @Select("SELECT COUNT(*) FROM inz_product_settlement WHERE order_id = #{orderId}")
    int checkOrderSettlementExists(@Param("orderId") String orderId);

    /**
     * 根据批次号获取结算记录
     */
    @Select("SELECT * FROM inz_product_settlement WHERE settlement_batch_no = #{batchNo}")
    List<InzProductSettlement> getSettlementsByBatchNo(@Param("batchNo") String batchNo);

    /**
     * 获取结算金额分布统计
     */
    @Select("SELECT " +
            "CASE " +
            "  WHEN settlement_amount < 100 THEN '0-100' " +
            "  WHEN settlement_amount < 500 THEN '100-500' " +
            "  WHEN settlement_amount < 1000 THEN '500-1000' " +
            "  WHEN settlement_amount < 5000 THEN '1000-5000' " +
            "  ELSE '5000+' " +
            "END as amount_range, " +
            "COUNT(*) as count " +
            "FROM inz_product_settlement " +
            "WHERE settlement_status = 2 " +
            "AND settlement_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY amount_range " +
            "ORDER BY MIN(settlement_amount)")
    List<Map<String, Object>> getSettlementAmountDistribution(@Param("startTime") String startTime, 
                                                              @Param("endTime") String endTime);
}
