package org.jeecg.modules.inz_product_activity.service.impl;

import org.jeecg.modules.api.product.entity.ProductActivity;
import org.jeecg.modules.inz_product_activity.entity.InzProductActivity;
import org.jeecg.modules.inz_product_activity.mapper.InzProductActivityMapper;
import org.jeecg.modules.inz_product_activity.service.IInzProductActivityService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @Description: 商品活动表
 * @Author: jeecg-boot
 * @Date:   2025-06-21
 * @Version: V1.0
 */
@Service
public class InzProductActivityServiceImpl extends ServiceImpl<InzProductActivityMapper, InzProductActivity> implements IInzProductActivityService {
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean createActivity(InzProductActivity inzProductActivity) {
        // 设置默认值
        if (inzProductActivity.getStatus() == null) {
            inzProductActivity.setStatus(1); // 默认启用
        }
        if (inzProductActivity.getSort() == null) {
            inzProductActivity.setSort(99); // 默认排序值
        }
        if (inzProductActivity.getGlobalVisible() == null) {
            inzProductActivity.setGlobalVisible(1); // 默认全平台可见
        }

        inzProductActivity.setCreateTime(new Date());

        return this.save(inzProductActivity);
    }
}
