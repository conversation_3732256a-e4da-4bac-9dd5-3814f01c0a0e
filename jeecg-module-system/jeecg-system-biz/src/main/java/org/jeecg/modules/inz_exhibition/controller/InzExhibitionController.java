package org.jeecg.modules.inz_exhibition.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_exhibition.entity.InzExhibition;
import org.jeecg.modules.inz_exhibition.service.IInzExhibitionService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 用户展馆表
 * @Author: jeecg-boot
 * @Date:   2025-06-15
 * @Version: V1.0
 */
/*@Api(tags="用户展馆表")*/
@RestController
@RequestMapping("/inz_exhibition/inzExhibition")
@Slf4j
public class InzExhibitionController extends JeecgController<InzExhibition, IInzExhibitionService> {
	@Autowired
	private IInzExhibitionService inzExhibitionService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzExhibition
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "用户展馆表-分页列表查询")
	/*@ApiOperation(value="用户展馆表-分页列表查询", notes="用户展馆表-分页列表查询")*/
	@GetMapping(value = "/list")
	public Result<IPage<InzExhibition>> queryPageList(InzExhibition inzExhibition,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<InzExhibition> queryWrapper = QueryGenerator.initQueryWrapper(inzExhibition, req.getParameterMap());
		Page<InzExhibition> page = new Page<InzExhibition>(pageNo, pageSize);
		IPage<InzExhibition> pageList = inzExhibitionService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param inzExhibition
	 * @return
	 */
	/*@AutoLog(value = "用户展馆表-添加")
	@ApiOperation(value="用户展馆表-添加", notes="用户展馆表-添加")*/
	@RequiresPermissions("inz_exhibition:inz_exhibition:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InzExhibition inzExhibition) {
		inzExhibitionService.save(inzExhibition);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param inzExhibition
	 * @return
	 */
/*	@AutoLog(value = "用户展馆表-编辑")
	@ApiOperation(value="用户展馆表-编辑", notes="用户展馆表-编辑")*/
	@RequiresPermissions("inz_exhibition:inz_exhibition:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InzExhibition inzExhibition) {
		inzExhibitionService.updateById(inzExhibition);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
/*	@AutoLog(value = "用户展馆表-通过id删除")
	@ApiOperation(value="用户展馆表-通过id删除", notes="用户展馆表-通过id删除")*/
	@RequiresPermissions("inz_exhibition:inz_exhibition:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzExhibitionService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
/*	@AutoLog(value = "用户展馆表-批量删除")
	@ApiOperation(value="用户展馆表-批量删除", notes="用户展馆表-批量删除")*/
	@RequiresPermissions("inz_exhibition:inz_exhibition:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzExhibitionService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "用户展馆表-通过id查询")
/*	@ApiOperation(value="用户展馆表-通过id查询", notes="用户展馆表-通过id查询")*/
	@GetMapping(value = "/queryById")
	public Result<InzExhibition> queryById(@RequestParam(name="id",required=true) String id) {
		InzExhibition inzExhibition = inzExhibitionService.getById(id);
		if(inzExhibition==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzExhibition);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param inzExhibition
    */
    @RequiresPermissions("inz_exhibition:inz_exhibition:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzExhibition inzExhibition) {
        return super.exportXls(request, inzExhibition, InzExhibition.class, "用户展馆表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("inz_exhibition:inz_exhibition:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzExhibition.class);
    }

}
