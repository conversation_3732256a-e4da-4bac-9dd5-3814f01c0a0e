package org.jeecg.modules.inz_points_products.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 积分商品表
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Data
@TableName("inz_points_product")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_points_product对象", description="积分商品表")
public class InzPointsProducts implements Serializable {
    private static final long serialVersionUID = 1L;

    // 状态常量
    public static final Integer STATUS_OFFLINE = 0; // 下架
    public static final Integer STATUS_ONLINE = 1; // 上架

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**商品名称*/
	@Excel(name = "商品名称", width = 15)
    @ApiModelProperty(value = "商品名称")
    private java.lang.String name;
	/**商品图片*/
	@Excel(name = "商品图片", width = 15)
    @ApiModelProperty(value = "商品图片")
    private java.lang.String image;
	/**商品描述*/
	@Excel(name = "商品描述", width = 15)
    @ApiModelProperty(value = "商品描述")
    private java.lang.String description;
	/**所需积分*/
	@Excel(name = "所需积分", width = 15)
    @ApiModelProperty(value = "所需积分")
    private java.lang.Integer points;
	/**库存数量*/
	@Excel(name = "库存数量", width = 15)
    @ApiModelProperty(value = "库存数量")
    private java.lang.Integer stock;
	/**已兑换数量*/
	@Excel(name = "已兑换数量", width = 15)
    @ApiModelProperty(value = "已兑换数量")
    private java.lang.Integer exchangeCount;
	/**限购数量(0表示不限购)*/
	@Excel(name = "限购数量(0表示不限购)", width = 15)
    @ApiModelProperty(value = "限购数量(0表示不限购)")
    private java.lang.Integer limitCount;
	/**商品类型(1:实物商品,2:虚拟商品,3:优惠券)*/
	@Excel(name = "商品类型(1:实物商品,2:虚拟商品,3:优惠券)", width = 15)
    @ApiModelProperty(value = "商品类型(1:实物商品,2:虚拟商品,3:优惠券)")
    private java.lang.Integer type;
	/**商品状态(0:下架,1:上架)*/
	@Excel(name = "商品状态(0:下架,1:上架)", width = 15)
    @ApiModelProperty(value = "商品状态(0:下架,1:上架)")
    private java.lang.Integer status;
	/**上架时间*/
	@Excel(name = "上架时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "上架时间")
    private java.util.Date shelfTime;
	/**下架时间*/
	@Excel(name = "下架时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "下架时间")
    private java.util.Date offShelfTime;
    /**定时上架时间*/
	@Excel(name = "定时上架时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "定时上架时间")
    private java.util.Date scheduledOnTime;
    /**定时下架时间*/
	@Excel(name = "定时下架时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "定时下架时间")
    private java.util.Date scheduledOffTime;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private java.lang.Integer sort;
	/**是否限量版(0:否,1:是)*/
	@Excel(name = "是否限量版(0:否,1:是)", width = 15)
    @ApiModelProperty(value = "是否限量版(0:否,1:是)")
    private java.lang.Integer isLimited;
	/**限量编号*/
	@Excel(name = "限量编号", width = 15)
    @ApiModelProperty(value = "限量编号")
    private java.lang.String limitedNumber;
	/**关联商品ID*/
	@Excel(name = "关联商品ID", width = 15)
    @ApiModelProperty(value = "关联商品ID")
    private java.lang.String productId;
	/**商品标签*/
	@Excel(name = "商品标签", width = 15)
    @ApiModelProperty(value = "商品标签")
    private java.lang.String tags;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
}
