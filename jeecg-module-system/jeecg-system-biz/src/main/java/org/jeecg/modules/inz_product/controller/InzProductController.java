package org.jeecg.modules.inz_product.controller;

import com.alibaba.fastjson.JSON;
import com.alipay.api.domain.Product;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.aspect.annotation.PermissionData;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.api.product.entity.ProductActivity;
import org.jeecg.modules.auditRecord.entity.AuditRecord;
import org.jeecg.modules.auditRecord.service.IAuditRecordService;
import org.jeecg.modules.inz_points_products.service.impl.InzPointsProductsServiceImpl;
import org.jeecg.modules.inz_product.entity.InzProduct;
import org.jeecg.modules.inz_product.service.IInzProductService;
import org.jeecg.modules.inz_product_activity.entity.InzProductActivity;
import org.jeecg.modules.inz_product_activity.service.IInzProductActivityService;
import org.jeecg.modules.inz_store.entity.InzStore;
import org.jeecg.modules.inz_store.service.IInzStoreService;
import org.jeecg.modules.inz_users_fronts.service.IInzUsersFrontsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 商品表
 * @Author: jeecg-boot
 * @Date: 2025-06-15
 * @Version: V1.0
 */
/*@Api(tags="商品表")*/
@RestController
@RequestMapping("/inz_product/inzProduct")
@Slf4j
public class InzProductController extends JeecgController<InzProduct, IInzProductService> {
    @Autowired
    private IInzProductService inzProductService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IInzProductActivityService inzProductActivityService;
    @Autowired
    private IAuditRecordService auditRecordService;
    @Autowired
    private IInzStoreService inzStoreService;
    @Autowired
    private IInzUsersFrontsService inzUsersFrontsService;
    private InzPointsProductsServiceImpl inzPointsProductsServiceImpl;

    /**
     * 分页列表查询
     *
     * @param inzProduct 查询条件
     * @param pageNo 页码
     * @param pageSize 每页记录数
     * @param storeId 店铺ID
     * @param storeName 店铺名称
     * @param productName 商品名称
     * @param createTime 上架时间范围（格式：yyyy-MM-dd HH:mm:ss,yyyy-MM-dd HH:mm:ss）
     * @param filter 商品筛选分类（1-精选，2-热卖，3-篮球，4-足球，5-综合收藏，6-其他运动）
     * @param req HTTP请求
     * @return 分页结果
     */
    //@AutoLog(value = "商品表-分页列表查询")
    /*@ApiOperation(value="商品表-分页列表查询", notes="商品表-分页列表查询")*/
    @GetMapping(value = "/list")
    @PermissionData(pageComponent = "inz_product/InzProductList")
    public Result<IPage<InzProduct>> queryPageList(InzProduct inzProduct,
                                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                   @RequestParam(name = "storeId", required = false) String storeId,
                                                   @RequestParam(name = "storeName", required = false) String storeName,
                                                   @RequestParam(name = "productName", required = false) String productName,
                                                   @RequestParam(name = "createTime", required = false) String createTime,
                                                   @RequestParam(name = "filter", required = false) Integer filter,
                                                   HttpServletRequest req) {
        Page<InzProduct> page = new Page<InzProduct>(pageNo, pageSize);
        IPage<InzProduct> pageList;

        // 创建查询条件
        QueryWrapper<InzProduct> queryWrapper = QueryGenerator.initQueryWrapper(inzProduct, req.getParameterMap());
        
        // 处理店铺ID筛选
        if (StringUtils.isNotBlank(storeId)) {
            queryWrapper.eq("store_id", storeId);
        }
        
        // 处理店铺名称筛选
        if (StringUtils.isNotBlank(storeName)) {
            // 通过店铺名称获取店铺ID
            String storeIdFromName = inzStoreService.getStoreIdByName(storeName);
            if (StringUtils.isNotBlank(storeIdFromName)) {
                queryWrapper.eq("store_id", storeIdFromName);
            } else {
                // 如果找不到店铺，返回空结果
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }
        }
        
        // 处理商品名称筛选
        if (StringUtils.isNotBlank(productName)) {
            queryWrapper.like("name", productName);
        }
        
        // 处理上架时间范围筛选
        if (StringUtils.isNotBlank(createTime)) {
            String[] createTimeArr = createTime.split(",");
            if (createTimeArr.length == 2) {
                String startTime = createTimeArr[0].trim();
                String endTime = createTimeArr[1].trim();
                if (StringUtils.isNotBlank(startTime)) {
                    queryWrapper.ge("create_time", startTime);
                }
                if (StringUtils.isNotBlank(endTime)) {
                    queryWrapper.le("create_time", endTime);
                }
            }
        }

        // 处理商品筛选分类
        if (filter != null) {
            queryWrapper.eq("filter", filter);
        }
        
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null) {
            // 判断是否为管理员角色
            if (!isAdmin(sysUser)) {
                log.info("非管理员用户访问商品列表，进行数据权限过滤");
                
                // 获取用户手机号，用于查询前端用户
                String phone = sysUser.getPhone();
                
                // 通过手机号查找对应的前端用户
                String userId = null;
                if (StringUtils.isNotBlank(phone)) {
                    try {
                        // 尝试通过手机号查询前端用户ID
                        userId = inzUsersFrontsService.getUserIdByPhone(phone);
                        log.info("通过手机号[{}]查询到前端用户ID: {}", phone, userId);
                    } catch (Exception e) {
                        log.warn("通过手机号查询前端用户失败: {}", e.getMessage());
                    }
                } else {
                    log.warn("后台用户[{}]没有关联手机号", sysUser.getUsername());
                }
                
                // 如果找到了前端用户ID，查询其关联的店铺
                if (StringUtils.isNotBlank(userId)) {
                    // 查询用户关联的店铺
                    QueryWrapper<InzStore> storeQueryWrapper = new QueryWrapper<>();
                    storeQueryWrapper.eq("user_id", userId);
                    InzStore store = inzStoreService.getOne(storeQueryWrapper);
                    
                    if (store != null) {
                        // 用户有关联店铺，只能查看自己店铺的商品
                        log.info("用户[{}]关联店铺[{}]，只能查看该店铺商品", userId, store.getId());
                        queryWrapper.eq("store_id", store.getId());
                    } else {
                        log.warn("用户[{}]没有关联店铺，返回空结果", userId);
                        return Result.OK(new Page<>(pageNo, pageSize, 0));
                    }
                } else {
                    log.warn("未找到后台用户对应的前端用户，返回空结果");
                    return Result.OK(new Page<>(pageNo, pageSize, 0));
                }
            } else {
                log.info("管理员用户访问商品列表，不进行数据权限过滤");
            }
        }
        
        // 执行查询
        pageList = inzProductService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加商品
     *
     * @param requestMap 商品信息
     * @return 添加结果
     */
    @AutoLog(value = "商品表-添加")
    @ApiOperation(value="商品表-添加", notes="商品表-添加")
    @PostMapping(value = "/add")
    public Result<InzProduct> add(@RequestBody Map<String, Object> requestMap) {
        try {
            // 从请求中获取商品信息
            InzProduct inzProduct = new InzProduct();
            
            // 基本信息处理
            if (requestMap.containsKey("product")) {
                // 如果是通过product字段传递的商品信息
                String productJson = JSON.toJSONString(requestMap.get("product"));
                InzProduct productFromJson = JSON.parseObject(productJson, InzProduct.class);
                
                // 设置商品名称
                inzProduct.setName(productFromJson.getName());
                // 设置店铺ID
                inzProduct.setStoreId(productFromJson.getStoreId());
                // 设置商品价格
                inzProduct.setPrice(productFromJson.getPrice());
                // 设置商品原价
                inzProduct.setOriginalPrice(productFromJson.getOriginalPrice());
                // 设置商品库存
                inzProduct.setStock(productFromJson.getStock());
                // 设置商品描述
                inzProduct.setDescription(productFromJson.getDescription());
                // 设置商品详情
                inzProduct.setDetail(productFromJson.getDetail());
                // 设置商品图片
                inzProduct.setImages(productFromJson.getImages());
                inzProduct.setMainImage(productFromJson.getMainImage());
                // 设置商品规格
                inzProduct.setSpecifications(productFromJson.getSpecifications());
                // 设置商品分类
                inzProduct.setCategory(productFromJson.getCategory());
                // 设置商品标签
                inzProduct.setTags(productFromJson.getTags());
                
                // 如果tabType已经设置，则不覆盖
                if (inzProduct.getTabType() == null) {
                    inzProduct.setTabType(productFromJson.getTabType());
                }
                
                // 设置商品类型
                inzProduct.setType(productFromJson.getType());
                
                // 设置商品品牌
                inzProduct.setBrand(productFromJson.getBrand());
                
                // 设置商品系列
                inzProduct.setSeries(productFromJson.getSeries());
                
                // 设置商品卖点
                inzProduct.setSellingPoint(productFromJson.getSellingPoint());
                
                // 设置成本价
                inzProduct.setCostPrice(productFromJson.getCostPrice());
                
                // 设置运费相关
                inzProduct.setShippingType(productFromJson.getShippingType());
                inzProduct.setShippingFee(productFromJson.getShippingFee());
                inzProduct.setFreeShipping(productFromJson.getFreeShipping());
                inzProduct.setFreightTemplateId(productFromJson.getFreightTemplateId());
                
                // 设置售后服务
                inzProduct.setAfterSaleServices(productFromJson.getAfterSaleServices());
                inzProduct.setHasSfFreeShipping(productFromJson.getHasSfFreeShipping());
                inzProduct.setHasCompensation(productFromJson.getHasCompensation());
                inzProduct.setHasFastDelivery(productFromJson.getHasFastDelivery());
                
                // 设置开售时间相关
                inzProduct.setSaleTimeType(productFromJson.getSaleTimeType());
                inzProduct.setScheduledSaleTime(productFromJson.getScheduledSaleTime());
                inzProduct.setScheduledOnTime(productFromJson.getScheduledOnTime());
                
                // 设置定时下架相关
                inzProduct.setAutoOffShelf(productFromJson.getAutoOffShelf());
                inzProduct.setScheduledOffShelfTime(productFromJson.getScheduledOffShelfTime());
                inzProduct.setScheduledOffTime(productFromJson.getScheduledOffTime());
                
                // 设置预售相关
                inzProduct.setIsPresale(productFromJson.getIsPresale());
                inzProduct.setPresaleDeliveryTime(productFromJson.getPresaleDeliveryTime());
                inzProduct.setPresaleDaysAfterPayment(productFromJson.getPresaleDaysAfterPayment());
                
                // 设置限购相关
                inzProduct.setIsLimitPurchase(productFromJson.getIsLimitPurchase());
                inzProduct.setPurchaseLimitEnabled(productFromJson.getPurchaseLimitEnabled());
                inzProduct.setPurchaseLimitQty(productFromJson.getPurchaseLimitQty());
                inzProduct.setLimitPurchaseQuantity(productFromJson.getLimitPurchaseQuantity());
                
                // 设置起售数量
                inzProduct.setMinPurchaseQty(productFromJson.getMinPurchaseQty());
                inzProduct.setMinPurchaseQuantity(productFromJson.getMinPurchaseQuantity());
                
                // 设置支付类型
                inzProduct.setPaymentType(productFromJson.getPaymentType());
                inzProduct.setPointsPrice(productFromJson.getPointsPrice());
                
                // 设置卡片相关信息
                inzProduct.setYear(productFromJson.getYear());
                inzProduct.setTeam(productFromJson.getTeam());
                inzProduct.setPlayer(productFromJson.getPlayer());
                inzProduct.setGrade(productFromJson.getGrade());
                inzProduct.setRating(productFromJson.getRating());
                inzProduct.setLimitedNumber(productFromJson.getLimitedNumber());
                inzProduct.setHasSigned(productFromJson.getHasSigned());
                inzProduct.setHasJerseyPatch(productFromJson.getHasJerseyPatch());
                inzProduct.setCardType(productFromJson.getCardType());
                inzProduct.setCardSeries(productFromJson.getCardSeries());
                inzProduct.setCardNumber(productFromJson.getCardNumber());
                inzProduct.setGradingCompany(productFromJson.getGradingCompany());
                inzProduct.setGradingSerial(productFromJson.getGradingSerial());
                inzProduct.setMarketPriceRange(productFromJson.getMarketPriceRange());
                inzProduct.setRarityLevel(productFromJson.getRarityLevel());
                inzProduct.setCardFeatures(productFromJson.getCardFeatures());
                
                // 设置视频链接
                inzProduct.setVideoUrl(productFromJson.getVideoUrl());
                
                // 设置其他描述信息
                inzProduct.setNotice(productFromJson.getNotice());
                inzProduct.setPriceDescription(productFromJson.getPriceDescription());
                inzProduct.setHelpDescription(productFromJson.getHelpDescription());
                inzProduct.setSpecialTips(productFromJson.getSpecialTips());
                inzProduct.setPublisher(productFromJson.getPublisher());
                inzProduct.setFeatures(productFromJson.getFeatures());
                inzProduct.setConfiguration(productFromJson.getConfiguration());
                inzProduct.setDesignPreview(productFromJson.getDesignPreview());
                
                // 设置助力相关
                inzProduct.setCurrentSupport(productFromJson.getCurrentSupport());
                inzProduct.setTargetSupport(productFromJson.getTargetSupport());
                inzProduct.setSupportEndTime(productFromJson.getSupportEndTime());
                
                // 设置直播ID
                inzProduct.setLiveId(productFromJson.getLiveId());
                
                // 设置是否为特色商品
                inzProduct.setIsFeatured(productFromJson.getIsFeatured());
                
                // 设置排序
                inzProduct.setSort(productFromJson.getSort());
            } else {
                // 直接从requestMap中获取字段
                // 基本信息
                if (requestMap.containsKey("name")) inzProduct.setName((String) requestMap.get("name"));
                if (requestMap.containsKey("storeId")) inzProduct.setStoreId((String) requestMap.get("storeId"));
                if (requestMap.containsKey("price")) inzProduct.setPrice(new BigDecimal(requestMap.get("price").toString()));
                if (requestMap.containsKey("originalPrice")) inzProduct.setOriginalPrice(new BigDecimal(requestMap.get("originalPrice").toString()));
                if (requestMap.containsKey("stock")) inzProduct.setStock((Integer) requestMap.get("stock"));
                if (requestMap.containsKey("description")) inzProduct.setDescription((String) requestMap.get("description"));
                if (requestMap.containsKey("detail")) inzProduct.setDetail((String) requestMap.get("detail"));
                if (requestMap.containsKey("images")) inzProduct.setImages((String) requestMap.get("images"));
                if (requestMap.containsKey("mainImage")) inzProduct.setMainImage((String) requestMap.get("mainImage"));
                if (requestMap.containsKey("specifications")) inzProduct.setSpecifications((String) requestMap.get("specifications"));
                if (requestMap.containsKey("category")) inzProduct.setCategory((String) requestMap.get("category"));
                if (requestMap.containsKey("tags")) inzProduct.setTags((String) requestMap.get("tags"));
                if (requestMap.containsKey("tabType")) inzProduct.setTabType((Integer) requestMap.get("tabType"));
                if (requestMap.containsKey("type")) inzProduct.setType((Integer) requestMap.get("type"));
                
                // 商品品牌和系列
                if (requestMap.containsKey("brand")) inzProduct.setBrand((String) requestMap.get("brand"));
                if (requestMap.containsKey("series")) inzProduct.setSeries((String) requestMap.get("series"));
                if (requestMap.containsKey("sellingPoint")) inzProduct.setSellingPoint((String) requestMap.get("sellingPoint"));
                
                // 价格与库存
                if (requestMap.containsKey("costPrice")) inzProduct.setCostPrice(new BigDecimal(requestMap.get("costPrice").toString()));
                if (requestMap.containsKey("shippingType")) inzProduct.setShippingType((Integer) requestMap.get("shippingType"));
                if (requestMap.containsKey("shippingFee")) inzProduct.setShippingFee(new BigDecimal(requestMap.get("shippingFee").toString()));
                if (requestMap.containsKey("freeShipping")) inzProduct.setFreeShipping((Integer) requestMap.get("freeShipping"));
                if (requestMap.containsKey("freightTemplateId")) inzProduct.setFreightTemplateId((String) requestMap.get("freightTemplateId"));
                
                // 售后服务
                if (requestMap.containsKey("afterSaleServices")) inzProduct.setAfterSaleServices((String) requestMap.get("afterSaleServices"));
                if (requestMap.containsKey("hasSfFreeShipping")) inzProduct.setHasSfFreeShipping((Integer) requestMap.get("hasSfFreeShipping"));
                if (requestMap.containsKey("hasCompensation")) inzProduct.setHasCompensation((Integer) requestMap.get("hasCompensation"));
                if (requestMap.containsKey("hasFastDelivery")) inzProduct.setHasFastDelivery((Integer) requestMap.get("hasFastDelivery"));
                
                // 开售时间
                if (requestMap.containsKey("saleTimeType")) inzProduct.setSaleTimeType((Integer) requestMap.get("saleTimeType"));
                if (requestMap.containsKey("scheduledSaleTime")) {
                    String scheduledSaleTime = (String) requestMap.get("scheduledSaleTime");
                    inzProduct.setScheduledSaleTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(scheduledSaleTime));
                }
                if (requestMap.containsKey("scheduledOnTime")) {
                    String scheduledOnTime = (String) requestMap.get("scheduledOnTime");
                    inzProduct.setScheduledOnTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(scheduledOnTime));
                }
                
                // 定时下架
                if (requestMap.containsKey("autoOffShelf")) inzProduct.setAutoOffShelf((Integer) requestMap.get("autoOffShelf"));
                if (requestMap.containsKey("scheduledOffShelfTime")) {
                    String scheduledOffShelfTime = (String) requestMap.get("scheduledOffShelfTime");
                    inzProduct.setScheduledOffShelfTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(scheduledOffShelfTime));
                }
                if (requestMap.containsKey("scheduledOffTime")) {
                    String scheduledOffTime = (String) requestMap.get("scheduledOffTime");
                    inzProduct.setScheduledOffTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(scheduledOffTime));
                }
                
                // 预售
                if (requestMap.containsKey("isPresale")) inzProduct.setIsPresale((Integer) requestMap.get("isPresale"));
                if (requestMap.containsKey("presaleDeliveryTime")) {
                    String presaleDeliveryTime = (String) requestMap.get("presaleDeliveryTime");
                    inzProduct.setPresaleDeliveryTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(presaleDeliveryTime));
                }
                if (requestMap.containsKey("presaleDaysAfterPayment")) inzProduct.setPresaleDaysAfterPayment((Integer) requestMap.get("presaleDaysAfterPayment"));
                
                // 限购
                if (requestMap.containsKey("isLimitPurchase")) inzProduct.setIsLimitPurchase((Integer) requestMap.get("isLimitPurchase"));
                if (requestMap.containsKey("purchaseLimitEnabled")) inzProduct.setPurchaseLimitEnabled((Integer) requestMap.get("purchaseLimitEnabled"));
                if (requestMap.containsKey("purchaseLimitQty")) inzProduct.setPurchaseLimitQty((Integer) requestMap.get("purchaseLimitQty"));
                if (requestMap.containsKey("limitPurchaseQuantity")) inzProduct.setLimitPurchaseQuantity((Integer) requestMap.get("limitPurchaseQuantity"));
                
                // 起售数量
                if (requestMap.containsKey("minPurchaseQty")) inzProduct.setMinPurchaseQty((Integer) requestMap.get("minPurchaseQty"));
                if (requestMap.containsKey("minPurchaseQuantity")) inzProduct.setMinPurchaseQuantity((Integer) requestMap.get("minPurchaseQuantity"));
                
                // 支付类型
                if (requestMap.containsKey("paymentType")) inzProduct.setPaymentType((Integer) requestMap.get("paymentType"));
                if (requestMap.containsKey("pointsPrice")) inzProduct.setPointsPrice((Integer) requestMap.get("pointsPrice"));
                
                // 卡片相关信息
                if (requestMap.containsKey("year")) inzProduct.setYear((String) requestMap.get("year"));
                if (requestMap.containsKey("team")) inzProduct.setTeam((String) requestMap.get("team"));
                if (requestMap.containsKey("player")) inzProduct.setPlayer((String) requestMap.get("player"));
                if (requestMap.containsKey("grade")) inzProduct.setGrade((String) requestMap.get("grade"));
                if (requestMap.containsKey("rating")) inzProduct.setRating((Double) requestMap.get("rating"));
                if (requestMap.containsKey("limitedNumber")) inzProduct.setLimitedNumber((String) requestMap.get("limitedNumber"));
                if (requestMap.containsKey("hasSigned")) inzProduct.setHasSigned((Integer) requestMap.get("hasSigned"));
                if (requestMap.containsKey("hasJerseyPatch")) inzProduct.setHasJerseyPatch((Integer) requestMap.get("hasJerseyPatch"));
                if (requestMap.containsKey("cardType")) inzProduct.setCardType((String) requestMap.get("cardType"));
                if (requestMap.containsKey("cardSeries")) inzProduct.setCardSeries((String) requestMap.get("cardSeries"));
                if (requestMap.containsKey("cardNumber")) inzProduct.setCardNumber((String) requestMap.get("cardNumber"));
                if (requestMap.containsKey("gradingCompany")) inzProduct.setGradingCompany((String) requestMap.get("gradingCompany"));
                if (requestMap.containsKey("gradingSerial")) inzProduct.setGradingSerial((String) requestMap.get("gradingSerial"));
                if (requestMap.containsKey("marketPriceRange")) inzProduct.setMarketPriceRange((String) requestMap.get("marketPriceRange"));
                if (requestMap.containsKey("rarityLevel")) inzProduct.setRarityLevel((String) requestMap.get("rarityLevel"));
                if (requestMap.containsKey("cardFeatures")) inzProduct.setCardFeatures((String) requestMap.get("cardFeatures"));
                
                // 视频链接
                if (requestMap.containsKey("videoUrl")) inzProduct.setVideoUrl((String) requestMap.get("videoUrl"));
                
                // 其他描述信息
                if (requestMap.containsKey("notice")) inzProduct.setNotice((String) requestMap.get("notice"));
                if (requestMap.containsKey("priceDescription")) inzProduct.setPriceDescription((String) requestMap.get("priceDescription"));
                if (requestMap.containsKey("helpDescription")) inzProduct.setHelpDescription((String) requestMap.get("helpDescription"));
                if (requestMap.containsKey("specialTips")) inzProduct.setSpecialTips((String) requestMap.get("specialTips"));
                if (requestMap.containsKey("publisher")) inzProduct.setPublisher((String) requestMap.get("publisher"));
                if (requestMap.containsKey("features")) inzProduct.setFeatures((String) requestMap.get("features"));
                if (requestMap.containsKey("configuration")) inzProduct.setConfiguration((String) requestMap.get("configuration"));
                if (requestMap.containsKey("designPreview")) inzProduct.setDesignPreview((String) requestMap.get("designPreview"));
                
                // 助力相关
                if (requestMap.containsKey("currentSupport")) inzProduct.setCurrentSupport((Integer) requestMap.get("currentSupport"));
                if (requestMap.containsKey("targetSupport")) inzProduct.setTargetSupport((Integer) requestMap.get("targetSupport"));
                if (requestMap.containsKey("supportEndTime")) {
                    String supportEndTime = (String) requestMap.get("supportEndTime");
                    inzProduct.setSupportEndTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(supportEndTime));
                }
                
                // 直播ID
                if (requestMap.containsKey("liveId")) inzProduct.setLiveId((String) requestMap.get("liveId"));
                
                // 是否为特色商品
                if (requestMap.containsKey("isFeatured")) inzProduct.setIsFeatured((Integer) requestMap.get("isFeatured"));
                
                // 排序
                if (requestMap.containsKey("sort")) inzProduct.setSort((Integer) requestMap.get("sort"));
            }
            
            // 处理tabType
            if (inzProduct.getTabType() == null) {
                // 如果tabType为null，设置默认值
                inzProduct.setTabType(InzProduct.TAB_TYPE_FEATURED); // 默认为精选
            }
            
            // 设置商品编号
            if (StringUtils.isBlank(inzProduct.getProductNo())) {
                // 生成商品编号，格式：CP + 8位随机数字
                int randomNum = (int) (Math.random() * 90000000) + 10000000; // 生成10000000-99999999之间的随机数
                inzProduct.setProductNo("CP" + randomNum);
            }
            
            // 设置初始状态
            if (inzProduct.getStatus() == null) {
                inzProduct.setStatus(InzProduct.STATUS_OFFLINE); // 默认为下架状态
            }
            
            // 设置初始销量
            if (inzProduct.getSales() == null) {
                inzProduct.setSales(0);
            }
            
            // 保存商品
            boolean saveResult = inzProductService.save(inzProduct);
            
            if (saveResult) {
                // 获取是否自动提交审核的标志
                Object autoSubmitObj = requestMap.get("autoSubmitAudit");
                boolean autoSubmit = autoSubmitObj instanceof Boolean ? (Boolean) autoSubmitObj : true; // 默认为true
                
                // 如果需要自动提交审核
                if (autoSubmit) {
                    try {
                        // 提交商品上架审核
                        Result<AuditRecord> auditResult = auditRecordService.submitProductForAudit(inzProduct.getId());
                        if (auditResult.isSuccess()) {
                            // 更新商品状态为审核中
                            inzProduct.setStatus(InzProduct.STATUS_AUDITING); // 设置为审核中状态
                            inzProductService.updateById(inzProduct);
                            log.info("商品[{}]({})已自动提交审核", inzProduct.getName(), inzProduct.getId());
                            return Result.OK("添加成功并已提交审核！", inzProduct);
                        } else {
                            log.warn("商品[{}]({})自动提交审核失败: {}", inzProduct.getName(), inzProduct.getId(), auditResult.getMessage());
                            return Result.OK("商品添加成功，但提交审核失败: " + auditResult.getMessage(), inzProduct);
                        }
                    } catch (Exception e) {
                        log.error("商品[{}]({})自动提交审核出错", inzProduct.getName(), inzProduct.getId(), e);
                        return Result.OK("商品添加成功，但提交审核出错: " + e.getMessage(), inzProduct);
                    }
                }
            }
            
            return Result.OK("添加成功！", inzProduct);
        } catch (Exception e) {
            log.error("添加商品失败", e);
            return Result.error("添加失败：" + e.getMessage());
        }
    }

    /**
     * 编辑商品
     *
     * @param inzProduct 商品信息
     * @return 编辑结果
     */
    @AutoLog(value = "商品表-编辑")
    @ApiOperation(value="商品表-编辑", notes="商品表-编辑")
    @RequiresPermissions("inz_product:inz_product:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<InzProduct> edit(@RequestBody InzProduct inzProduct) {
        // 处理商品主图
        if (inzProduct.getImages() != null && !inzProduct.getImages().isEmpty() && 
            (inzProduct.getMainImage() == null || inzProduct.getMainImage().isEmpty())) {
            // 如果没有设置主图，则默认使用第一张图片作为主图
            String[] imageArray = inzProduct.getImages().split(",");
            if (imageArray.length > 0) {
                inzProduct.setMainImage(imageArray[0]);
            }
        }
        
        inzProductService.updateById(inzProduct);
        return Result.OK(inzProduct);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
	/*@AutoLog(value = "商品表-通过id删除")
	@ApiOperation(value="商品表-通过id删除", notes="商品表-通过id删除")*/
    @RequiresPermissions("inz_product:inz_product:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        inzProductService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
	/*@AutoLog(value = "商品表-批量删除")
	@ApiOperation(value="商品表-批量删除", notes="商品表-批量删除")*/
    @RequiresPermissions("inz_product:inz_product:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzProductService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "商品表-通过id查询")
    /*@ApiOperation(value="商品表-通过id查询", notes="商品表-通过id查询")*/
    @GetMapping(value = "/queryById")
    public Result<InzProduct> queryById(@RequestParam(name = "id") String id) {
        InzProduct inzProduct = inzProductService.getById(id);
        if (inzProduct == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzProduct);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param inzProduct
     */
    @RequiresPermissions("inz_product:inz_product:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzProduct inzProduct) {
        return super.exportXls(request, inzProduct, InzProduct.class, "商品表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("inz_product:inz_product:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzProduct.class);
    }

    @ApiOperation(value="商品表-提交上架审核")
    @GetMapping(value = "/sale")
    public Result<String> sale(@RequestParam(name="id") String id) {
        InzProduct result = inzProductService.getById(id);
        if(result == null) {
            return Result.error("商品不存在");
        }
        
        if(result.getStatus() == 1) {
            return Result.error("商品已上架");
        }
        
        if(result.getStatus() == 2) {
            return Result.error("商品正在审核中，请耐心等待");
        }
        
        // 提交商品上架审核
        Result<AuditRecord> auditResult = auditRecordService.submitProductForAudit(id);
        if (auditResult.isSuccess()) {
            // 更新商品状态为审核中
            result.setStatus(2); // 设置为审核中状态
            inzProductService.updateById(result);
            return Result.OK("商品已提交审核，请等待审核结果");
        } else {
            return Result.error(auditResult.getMessage());
        }
    }

    @ApiOperation(value="商品表-下架")
    @GetMapping(value = "/unsale")
    public Result<String> unsale(@RequestParam(name="id") String id) {
        InzProduct result = inzProductService.getById(id);
        if(result == null) {
            return Result.error("商品不存在");
        }
        
        if(result.getStatus() == 0) {
            return Result.error("商品已下架");
        }
        
        if(result.getStatus() == 2) {
            return Result.error("商品正在审核中，请先取消审核");
        }
        
        result.setStatus(0); // 设置为下架状态
        if(inzProductService.updateById(result)){
            return Result.OK("下架成功!");
        }
        return Result.error("下架失败!");
    }
    
    /**
     * 取消商品审核
     */
    @ApiOperation(value="取消商品审核")
    @GetMapping(value = "/cancelAudit")
    public Result<String> cancelAudit(@RequestParam(name="id") String id) {
        InzProduct product = inzProductService.getById(id);
        if(product == null) {
            return Result.error("商品不存在");
        }
        
        if(product.getStatus() != 2) {
            return Result.error("商品不在审核状态，无法取消审核");
        }
        
        // 调用审核服务的取消审核方法
        Result<String> result = auditRecordService.cancelProductAudit(id);
        if (result.isSuccess()) {
            // 更新商品状态为未上架
            product.setStatus(0); // 未上架状态
            inzProductService.updateById(product);
        }
        return result;
    }
    
    /**
     * 查询商品审核状态
     */
    @ApiOperation(value="查询商品审核状态")
    @GetMapping(value = "/checkAuditStatus")
    public Result<?> checkAuditStatus(@RequestParam(name="id",required=true) String id) {
        InzProduct product = inzProductService.getById(id);
        if(product == null) {
            return Result.error("商品不存在");
        }
        
        if(product.getStatus() != 2) {
            return Result.error("商品不在审核状态");
        }
        
        AuditRecord auditRecord = auditRecordService.getAuditRecordByProductId(id);
        if(auditRecord == null) {
            return Result.error("未找到审核记录");
        }
        
        return Result.OK(auditRecord);
    }

    /**
     * 创建商品活动
     */
    @AutoLog(value = "创建商品活动")
    @ApiOperation(value = "创建商品活动", notes = "创建商品活动")
    @PostMapping("/create")
    public Result<?> createActivity(@RequestBody InzProductActivity inzproductActivity) {

        try {
            boolean success = inzProductActivityService.createActivity(inzproductActivity);
            if (success) {
                return Result.OK("创建活动成功");
            } else {
                return Result.error("创建活动失败");
            }
        } catch (Exception e) {
            log.error("创建活动失败", e);
            return Result.error("创建活动失败: " + e.getMessage());
        }
    }

    /**
     * 通过店铺名称查询商品
     *
     * @param storeName 店铺名称
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @param req 请求
     * @return 商品列表
     */
    @ApiOperation(value="商品表-通过店铺名称查询", notes="商品表-通过店铺名称查询")
    @GetMapping(value = "/listByStoreName")
    public Result<IPage<InzProduct>> queryPageListByStoreName(
                                   @RequestParam(name="storeName") String storeName,
                                   @RequestParam(name="name", required=false) String productName,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                   @RequestParam(name="createTime", required=false) String createTime,
                                   HttpServletRequest req) {
        Page<InzProduct> page = new Page<>(pageNo, pageSize);
        IPage<InzProduct> pageList;
        
        try {
            // 创建查询条件
            QueryWrapper<InzProduct> queryWrapper = new QueryWrapper<>();
            
            // 通过店铺名称找到店铺ID
            String storeId = inzStoreService.getStoreIdByName(storeName);
            if (StringUtils.isBlank(storeId)) {
                return Result.error("未找到名称为 [" + storeName + "] 的店铺");
            }
            queryWrapper.eq("store_id", storeId);
            
            // 处理商品名称筛选
            if (StringUtils.isNotBlank(productName)) {
                queryWrapper.like("name", productName);
            }
            
            // 处理上架时间范围筛选
            if (StringUtils.isNotBlank(createTime)) {
                String[] createTimeArr = createTime.split(",");
                if (createTimeArr.length == 2) {
                    String startTime = createTimeArr[0].trim();
                    String endTime = createTimeArr[1].trim();
                    if (StringUtils.isNotBlank(startTime)) {
                        queryWrapper.ge("create_time", startTime);
                    }
                    if (StringUtils.isNotBlank(endTime)) {
                        queryWrapper.le("create_time", endTime);
                    }
                }
            }
            
            // 执行查询
            pageList = inzProductService.page(page, queryWrapper);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error("通过店铺名称查询商品失败", e);
            return Result.error("通过店铺名称查询商品失败: " + e.getMessage());
        }
    }
    
    /**
     * 判断用户是否为管理员
     * @param user 登录用户
     * @return 是否为管理员
     */
    private boolean isAdmin(LoginUser user) {
        if (user == null) {
            return false;
        }
        
        // 根据角色编码判断
        String roleCode = user.getRoleCode();
        return StringUtils.isNotBlank(roleCode) && 
               (roleCode.contains("admin") || roleCode.contains("ADMIN"));
    }

    /**
     * 获取商品标签类型列表
     * 
     * @return 标签类型列表
     */
    @AutoLog(value = "商品表-获取标签类型列表")
    @ApiOperation(value = "商品表-获取标签类型列表", notes = "获取系统中定义的所有标签类型")
    @GetMapping(value = "/tabTypes")
    public Result<List<Map<String, Object>>> getTabTypes() {
        List<Map<String, Object>> tabTypes = new ArrayList<>();
        
        // 添加精选标签
        Map<String, Object> featured = new HashMap<>();
        featured.put("code", InzProduct.TAB_TYPE_FEATURED);
        featured.put("name", "精选");
        tabTypes.add(featured);
        
        // 添加原箱标签
        Map<String, Object> originalBox = new HashMap<>();
        originalBox.put("code", InzProduct.TAB_TYPE_ORIGINAL_BOX);
        originalBox.put("name", "原箱");
        tabTypes.add(originalBox);
        
        // 添加单包标签
        Map<String, Object> singlePack = new HashMap<>();
        singlePack.put("code", InzProduct.TAB_TYPE_SINGLE_PACK);
        singlePack.put("name", "单包");
        tabTypes.add(singlePack);
        
        // 添加潮玩标签
        Map<String, Object> trendy = new HashMap<>();
        trendy.put("code", InzProduct.TAB_TYPE_TRENDY);
        trendy.put("name", "潮玩");
        tabTypes.add(trendy);
        
        // 添加周边标签
        Map<String, Object> peripheral = new HashMap<>();
        peripheral.put("code", InzProduct.TAB_TYPE_PERIPHERAL);
        peripheral.put("name", "周边");
        tabTypes.add(peripheral);
        
        return Result.OK(tabTypes);
    }

    /**
     * 获取商品类型列表
     * 
     * @return 商品类型列表
     */
    @AutoLog(value = "商品表-获取商品类型列表")
    @ApiOperation(value = "商品表-获取商品类型列表", notes = "获取系统中定义的所有商品类型")
    @GetMapping(value = "/productTypes")
    public Result<List<Map<String, Object>>> getProductTypes() {
        List<Map<String, Object>> productTypes = new ArrayList<>();

        // 添加随机卡种
        Map<String, Object> randomCard = new HashMap<>();
        randomCard.put("code", InzProduct.TYPE_RANDOM_CARD);
        randomCard.put("name", "随机卡种");
        productTypes.add(randomCard);

        // 添加随机球员
        Map<String, Object> randomPlayer = new HashMap<>();
        randomPlayer.put("code", InzProduct.TYPE_RANDOM_PLAYER);
        randomPlayer.put("name", "随机球员");
        productTypes.add(randomPlayer);

        // 添加随机球队
        Map<String, Object> randomTeam = new HashMap<>();
        randomTeam.put("code", InzProduct.TYPE_RANDOM_TEAM);
        randomTeam.put("name", "随机球队");
        productTypes.add(randomTeam);

        // 添加选队随机
        Map<String, Object> selectTeamRandom = new HashMap<>();
        selectTeamRandom.put("code", InzProduct.TYPE_SELECT_TEAM_RANDOM);
        selectTeamRandom.put("name", "选队随机");
        productTypes.add(selectTeamRandom);

        // 添加自选球队
        Map<String, Object> customTeam = new HashMap<>();
        customTeam.put("code", InzProduct.TYPE_CUSTOM_TEAM);
        customTeam.put("name", "自选球队");
        productTypes.add(customTeam);

        // 添加队伍随机
        Map<String, Object> teamRandom = new HashMap<>();
        teamRandom.put("code", InzProduct.TYPE_TEAM_RANDOM);
        teamRandom.put("name", "队伍随机");
        productTypes.add(teamRandom);

        return Result.OK(productTypes);
    }
    
    /**
     * 上传商品图片
     * 
     * @param request HTTP请求
     * @return 上传结果
     */
    @AutoLog(value = "商品表-上传图片")
    @ApiOperation(value = "商品表-上传图片", notes = "商品表-上传图片")
    @PostMapping(value = "/uploadImage")
    public Result<?> uploadImage(HttpServletRequest request) {
        try {
            // 调用公共的文件上传服务
            return Result.OK("上传成功");
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return Result.error("上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 上传商品视频
     * 
     * @param request HTTP请求
     * @return 上传结果
     */
    @AutoLog(value = "商品表-上传视频")
    @ApiOperation(value = "商品表-上传视频", notes = "商品表-上传视频")
    @PostMapping(value = "/uploadVideo")
    public Result<?> uploadVideo(HttpServletRequest request) {
        try {
            // 调用公共的文件上传服务
            return Result.OK("上传成功");
        } catch (Exception e) {
            log.error("视频上传失败", e);
            return Result.error("上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建商品属性
     * 
     * @param attributes 商品属性
     * @return 创建结果
     */
    @AutoLog(value = "商品表-创建属性")
    @ApiOperation(value = "商品表-创建属性", notes = "商品表-创建属性")
    @PostMapping(value = "/createAttributes")
    public Result<?> createAttributes(@RequestBody Map<String, Object> attributes) {
        try {
            String productId = (String) attributes.get("productId");
            List<Map<String, Object>> attrList = (List<Map<String, Object>>) attributes.get("attributes");
            
            if (productId == null || attrList == null) {
                return Result.error("参数错误");
            }
            
            // 获取商品信息
            InzProduct product = inzProductService.getById(productId);
            if (product == null) {
                return Result.error("商品不存在");
            }
            
            // 将属性列表转换为JSON字符串
            String specifications = JSON.toJSONString(attrList);
            
            // 更新商品规格
            product.setSpecifications(specifications);
            inzProductService.updateById(product);
            
            return Result.OK("属性创建成功");
        } catch (Exception e) {
            log.error("创建属性失败", e);
            return Result.error("创建属性失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取售后服务选项
     * 
     * @return 售后服务选项
     */
    @AutoLog(value = "商品表-获取售后服务选项")
    @ApiOperation(value = "商品表-获取售后服务选项", notes = "获取系统中定义的所有售后服务选项")
    @GetMapping(value = "/afterSaleOptions")
    public Result<List<Map<String, Object>>> getAfterSaleOptions() {
        List<Map<String, Object>> options = new ArrayList<>();
        
        // 顺丰包邮
        Map<String, Object> sfExpress = new HashMap<>();
        sfExpress.put("code", "sf_free_shipping");
        sfExpress.put("name", "顺丰包邮");
        options.add(sfExpress);
        
        // 假一赔十
        Map<String, Object> compensation = new HashMap<>();
        compensation.put("code", "compensation");
        compensation.put("name", "假一赔十");
        options.add(compensation);
        
        // 24小时发货
        Map<String, Object> fastDelivery = new HashMap<>();
        fastDelivery.put("code", "fast_delivery");
        fastDelivery.put("name", "24小时发货");
        options.add(fastDelivery);
        
        return Result.OK(options);
    }
    
    /**
     * 获取开售时间选项
     * 
     * @return 开售时间选项
     */
    @AutoLog(value = "商品表-获取开售时间选项")
    @ApiOperation(value = "商品表-获取开售时间选项", notes = "获取系统中定义的所有开售时间选项")
    @GetMapping(value = "/saleTimeOptions")
    public Result<List<Map<String, Object>>> getSaleTimeOptions() {
        List<Map<String, Object>> options = new ArrayList<>();
        
        // 立即开售
        Map<String, Object> immediate = new HashMap<>();
        immediate.put("code", "immediate");
        immediate.put("name", "立即开售");
        options.add(immediate);
        
        // 定时开售
        Map<String, Object> scheduled = new HashMap<>();
        scheduled.put("code", "scheduled");
        scheduled.put("name", "定时开售");
        options.add(scheduled);
        
        // 添加后手动上架
        Map<String, Object> manual = new HashMap<>();
        manual.put("code", "manual");
        manual.put("name", "添加后手动上架");
        options.add(manual);
        
        return Result.OK(options);
    }
    
    /**
     * 获取定时下架选项
     * 
     * @return 定时下架选项
     */
    @AutoLog(value = "商品表-获取定时下架选项")
    @ApiOperation(value = "商品表-获取定时下架选项", notes = "获取系统中定义的所有定时下架选项")
    @GetMapping(value = "/scheduledOffShelfOptions")
    public Result<List<Map<String, Object>>> getScheduledOffShelfOptions() {
        List<Map<String, Object>> options = new ArrayList<>();
        
        // 开启
        Map<String, Object> enabled = new HashMap<>();
        enabled.put("code", "enabled");
        enabled.put("name", "开启");
        options.add(enabled);
        
        // 关闭
        Map<String, Object> disabled = new HashMap<>();
        disabled.put("code", "disabled");
        disabled.put("name", "关闭");
        options.add(disabled);
        
        return Result.OK(options);
    }
    
    /**
     * 获取预售选项
     * 
     * @return 预售选项
     */
    @AutoLog(value = "商品表-获取预售选项")
    @ApiOperation(value = "商品表-获取预售选项", notes = "获取系统中定义的所有预售选项")
    @GetMapping(value = "/presaleOptions")
    public Result<List<Map<String, Object>>> getPresaleOptions() {
        List<Map<String, Object>> options = new ArrayList<>();
        
        // 下单后居民的时才能发货
        Map<String, Object> afterOrder = new HashMap<>();
        afterOrder.put("code", "after_order");
        afterOrder.put("name", "下单后居民的时才能发货");
        options.add(afterOrder);
        
        return Result.OK(options);
    }
    
    /**
     * 获取发货时间选项
     * 
     * @return 发货时间选项
     */
    @AutoLog(value = "商品表-获取发货时间选项")
    @ApiOperation(value = "商品表-获取发货时间选项", notes = "获取系统中定义的所有发货时间选项")
    @GetMapping(value = "/deliveryTimeOptions")
    public Result<List<Map<String, Object>>> getDeliveryTimeOptions() {
        List<Map<String, Object>> options = new ArrayList<>();
        
        // 开始发货
        Map<String, Object> startDelivery = new HashMap<>();
        startDelivery.put("code", "start_delivery");
        startDelivery.put("name", "开始发货");
        options.add(startDelivery);
        
        // 付款成功后天后发货
        Map<String, Object> afterPayment = new HashMap<>();
        afterPayment.put("code", "after_payment");
        afterPayment.put("name", "付款成功后天后发货");
        afterPayment.put("description", "适用于用户下单后进货的商品");
        options.add(afterPayment);
        
        return Result.OK(options);
    }
    
    /**
     * 获取限购选项
     * 
     * @return 限购选项
     */
    @AutoLog(value = "商品表-获取限购选项")
    @ApiOperation(value = "商品表-获取限购选项", notes = "获取系统中定义的所有限购选项")
    @GetMapping(value = "/purchaseLimitOptions")
    public Result<List<Map<String, Object>>> getPurchaseLimitOptions() {
        List<Map<String, Object>> options = new ArrayList<>();
        
        // 限制每人可购买数量
        Map<String, Object> limitPerPerson = new HashMap<>();
        limitPerPerson.put("code", "limit_per_person");
        limitPerPerson.put("name", "限制每人可购买数量");
        limitPerPerson.put("description", "一个实名信息只能购买设定的数量");
        options.add(limitPerPerson);
        
        // 按单限购
        Map<String, Object> limitPerOrder = new HashMap<>();
        limitPerOrder.put("code", "limit_per_order");
        limitPerOrder.put("name", "按单限购");
        options.add(limitPerOrder);
        
        return Result.OK(options);
    }
    
    /**
     * 快速添加商品详情内容
     * 
     * @param content 内容信息
     * @return 添加结果
     */
    @AutoLog(value = "商品表-快速添加详情内容")
    @ApiOperation(value = "商品表-快速添加详情内容", notes = "商品表-快速添加详情内容")
    @PostMapping(value = "/quickAddContent")
    public Result<?> quickAddContent(@RequestBody Map<String, Object> content) {
        try {
            String productId = (String) content.get("productId");
            String type = (String) content.get("type");
            String value = (String) content.get("value");
            
            if (productId == null || type == null || value == null) {
                return Result.error("参数错误");
            }
            
            // 获取商品信息
            InzProduct product = inzProductService.getById(productId);
            if (product == null) {
                return Result.error("商品不存在");
            }
            
            // 根据类型处理内容
            String detail = product.getDetail() != null ? product.getDetail() : "";
            
            if ("image".equals(type)) {
                // 添加图片
                detail += "<img src='" + value + "' alt='商品图片' style='max-width:100%;'/>";
            } else if ("text".equals(type)) {
                // 添加文本
                detail += "<p>" + value + "</p>";
            }
            
            // 更新商品详情
            product.setDetail(detail);
            inzProductService.updateById(product);
            
            return Result.OK("内容添加成功");
        } catch (Exception e) {
            log.error("添加内容失败", e);
            return Result.error("添加内容失败: " + e.getMessage());
        }
    }

    /**
     * 设置商品定时上架
     * 
     * @param id 商品ID
     * @param scheduledTime 定时上架时间
     * @return 操作结果
     */
    @AutoLog(value = "商品表-设置定时上架")
    @ApiOperation(value = "商品表-设置定时上架", notes = "商品表-设置定时上架")
    @PostMapping(value = "/setScheduledOnTime")
    public Result<?> setScheduledOnTime(@RequestParam(name = "id") String id, 
                                        @RequestParam(name = "scheduledTime") String scheduledTime) {
        try {
            if (StringUtils.isBlank(id)) {
                return Result.error("商品ID不能为空");
            }
            
            InzProduct product = inzProductService.getById(id);
            if (product == null) {
                return Result.error("商品不存在");
            }
            
            // 解析定时上架时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date scheduledDate = sdf.parse(scheduledTime);
            
            // 设置定时上架时间
            product.setScheduledOnTime(scheduledDate);
            
            // 如果当前是上架状态，先下架，等待定时上架
            if (InzProduct.STATUS_ONLINE == product.getStatus()) {
                product.setStatus(InzProduct.STATUS_OFFLINE);
            }
            
            inzProductService.updateById(product);
            
            return Result.OK("设置定时上架成功");
        } catch (ParseException e) {
            log.error("解析定时上架时间失败", e);
            return Result.error("时间格式错误，请使用yyyy-MM-dd HH:mm:ss格式");
        } catch (Exception e) {
            log.error("设置定时上架失败", e);
            return Result.error("设置定时上架失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置商品定时下架
     * 
     * @param id 商品ID
     * @param scheduledTime 定时下架时间
     * @return 操作结果
     */
    @AutoLog(value = "商品表-设置定时下架")
    @ApiOperation(value = "商品表-设置定时下架", notes = "商品表-设置定时下架")
    @PostMapping(value = "/setScheduledOffTime")
    public Result<?> setScheduledOffTime(@RequestParam(name = "id") String id, 
                                         @RequestParam(name = "scheduledTime") String scheduledTime) {
        try {
            if (StringUtils.isBlank(id)) {
                return Result.error("商品ID不能为空");
            }
            
            InzProduct product = inzProductService.getById(id);
            if (product == null) {
                return Result.error("商品不存在");
            }
            
            // 解析定时下架时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date scheduledDate = sdf.parse(scheduledTime);
            
            // 设置定时下架时间
            product.setScheduledOffTime(scheduledDate);
            inzProductService.updateById(product);
            
            return Result.OK("设置定时下架成功");
        } catch (ParseException e) {
            log.error("解析定时下架时间失败", e);
            return Result.error("时间格式错误，请使用yyyy-MM-dd HH:mm:ss格式");
        } catch (Exception e) {
            log.error("设置定时下架失败", e);
            return Result.error("设置定时下架失败: " + e.getMessage());
        }
    }
    
    /**
     * 取消商品定时上架
     * 
     * @param id 商品ID
     * @return 操作结果
     */
    @AutoLog(value = "商品表-取消定时上架")
    @ApiOperation(value = "商品表-取消定时上架", notes = "商品表-取消定时上架")
    @PostMapping(value = "/cancelScheduledOnTime")
    public Result<?> cancelScheduledOnTime(@RequestParam(name = "id") String id) {
        try {
            if (StringUtils.isBlank(id)) {
                return Result.error("商品ID不能为空");
            }
            
            InzProduct product = inzProductService.getById(id);
            if (product == null) {
                return Result.error("商品不存在");
            }
            
            // 取消定时上架
            product.setScheduledOnTime(null);
            inzProductService.updateById(product);
            
            return Result.OK("取消定时上架成功");
        } catch (Exception e) {
            log.error("取消定时上架失败", e);
            return Result.error("取消定时上架失败: " + e.getMessage());
        }
    }
    
    /**
     * 取消商品定时下架
     * 
     * @param id 商品ID
     * @return 操作结果
     */
    @AutoLog(value = "商品表-取消定时下架")
    @ApiOperation(value = "商品表-取消定时下架", notes = "商品表-取消定时下架")
    @PostMapping(value = "/cancelScheduledOffTime")
    public Result<?> cancelScheduledOffTime(@RequestParam(name = "id") String id) {
        try {
            if (StringUtils.isBlank(id)) {
                return Result.error("商品ID不能为空");
            }
            
            InzProduct product = inzProductService.getById(id);
            if (product == null) {
                return Result.error("商品不存在");
            }
            
            // 取消定时下架
            product.setScheduledOffTime(null);
            inzProductService.updateById(product);
            
            return Result.OK("取消定时下架成功");
        } catch (Exception e) {
            log.error("取消定时下架失败", e);
            return Result.error("取消定时下架失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置商品售后服务
     * 
     * @param id 商品ID
     * @param hasSfFreeShipping 是否顺丰包邮
     * @param hasCompensation 是否假一赔十
     * @param hasFastDelivery 是否24小时发货
     * @return 操作结果
     */
    @AutoLog(value = "商品表-设置售后服务")
    @ApiOperation(value = "商品表-设置售后服务", notes = "商品表-设置售后服务")
    @PostMapping(value = "/setAfterSaleService")
    public Result<?> setAfterSaleService(@RequestParam(name = "id") String id, 
                                        @RequestParam(name = "hasSfFreeShipping", required = false, defaultValue = "0") Integer hasSfFreeShipping,
                                        @RequestParam(name = "hasCompensation", required = false, defaultValue = "0") Integer hasCompensation,
                                        @RequestParam(name = "hasFastDelivery", required = false, defaultValue = "0") Integer hasFastDelivery) {
        try {
            if (StringUtils.isBlank(id)) {
                return Result.error("商品ID不能为空");
            }
            
            InzProduct product = inzProductService.getById(id);
            if (product == null) {
                return Result.error("商品不存在");
            }
            
            // 设置售后服务
            product.setHasSfFreeShipping(hasSfFreeShipping);
            product.setHasCompensation(hasCompensation);
            product.setHasFastDelivery(hasFastDelivery);
            
            inzProductService.updateById(product);
            
            return Result.OK("设置售后服务成功");
        } catch (Exception e) {
            log.error("设置售后服务失败", e);
            return Result.error("设置售后服务失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置商品预售信息
     * 
     * @param id 商品ID
     * @param isPresale 是否预售商品
     * @param presaleDeliveryTime 预售发货时间
     * @param presaleDaysAfterPayment 付款后发货天数
     * @return 操作结果
     */
    @AutoLog(value = "商品表-设置预售信息")
    @ApiOperation(value = "商品表-设置预售信息", notes = "商品表-设置预售信息")
    @PostMapping(value = "/setPresaleInfo")
    public Result<?> setPresaleInfo(@RequestParam(name = "id") String id, 
                                   @RequestParam(name = "isPresale", required = false, defaultValue = "0") Integer isPresale,
                                   @RequestParam(name = "presaleDeliveryTime", required = false) String presaleDeliveryTime,
                                   @RequestParam(name = "presaleDaysAfterPayment", required = false) Integer presaleDaysAfterPayment) {
        try {
            if (StringUtils.isBlank(id)) {
                return Result.error("商品ID不能为空");
            }
            
            InzProduct product = inzProductService.getById(id);
            if (product == null) {
                return Result.error("商品不存在");
            }
            
            // 设置预售信息
            product.setIsPresale(isPresale);
            
            if (presaleDaysAfterPayment != null) {
                product.setPresaleDaysAfterPayment(presaleDaysAfterPayment);
                product.setPresaleDeliveryTime(null); // 如果设置了付款后发货天数，则清空预售发货时间
            } else if (StringUtils.isNotBlank(presaleDeliveryTime)) {
                // 解析预售发货时间
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date deliveryDate = sdf.parse(presaleDeliveryTime);
                
                product.setPresaleDeliveryTime(deliveryDate);
                product.setPresaleDaysAfterPayment(null); // 如果设置了预售发货时间，则清空付款后发货天数
            }
            
            inzProductService.updateById(product);
            
            return Result.OK("设置预售信息成功");
        } catch (ParseException e) {
            log.error("解析预售发货时间失败", e);
            return Result.error("时间格式错误，请使用yyyy-MM-dd HH:mm:ss格式");
        } catch (Exception e) {
            log.error("设置预售信息失败", e);
            return Result.error("设置预售信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置商品限购信息
     * 
     * @param id 商品ID
     * @param purchaseLimitEnabled 是否启用限购
     * @param purchaseLimitQty 每人限购数量
     * @return 操作结果
     */
    @AutoLog(value = "商品表-设置限购信息")
    @ApiOperation(value = "商品表-设置限购信息", notes = "商品表-设置限购信息")
    @PostMapping(value = "/setPurchaseLimit")
    public Result<?> setPurchaseLimit(@RequestParam(name = "id") String id, 
                                     @RequestParam(name = "purchaseLimitEnabled", required = false, defaultValue = "0") Integer purchaseLimitEnabled,
                                     @RequestParam(name = "purchaseLimitQty", required = false) Integer purchaseLimitQty) {
        try {
            if (StringUtils.isBlank(id)) {
                return Result.error("商品ID不能为空");
            }
            
            InzProduct product = inzProductService.getById(id);
            if (product == null) {
                return Result.error("商品不存在");
            }
            
            // 设置限购信息
            product.setPurchaseLimitEnabled(purchaseLimitEnabled);
            
            if (purchaseLimitEnabled != null && purchaseLimitEnabled == 1 && purchaseLimitQty != null) {
                product.setPurchaseLimitQty(purchaseLimitQty);
            } else {
                product.setPurchaseLimitQty(null);
            }
            
            inzProductService.updateById(product);
            
            return Result.OK("设置限购信息成功");
        } catch (Exception e) {
            log.error("设置限购信息失败", e);
            return Result.error("设置限购信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置商品起售数量
     * 
     * @param id 商品ID
     * @param minPurchaseQty 起售数量
     * @return 操作结果
     */
    @AutoLog(value = "商品表-设置起售数量")
    @ApiOperation(value = "商品表-设置起售数量", notes = "商品表-设置起售数量")
    @PostMapping(value = "/setMinPurchaseQty")
    public Result<?> setMinPurchaseQty(@RequestParam(name = "id") String id, 
                                      @RequestParam(name = "minPurchaseQty") Integer minPurchaseQty) {
        try {
            if (StringUtils.isBlank(id)) {
                return Result.error("商品ID不能为空");
            }
            
            if (minPurchaseQty == null || minPurchaseQty < 1) {
                return Result.error("起售数量必须大于等于1");
            }
            
            InzProduct product = inzProductService.getById(id);
            if (product == null) {
                return Result.error("商品不存在");
            }
            
            // 设置起售数量
            product.setMinPurchaseQty(minPurchaseQty);
            inzProductService.updateById(product);
            
            return Result.OK("设置起售数量成功");
        } catch (Exception e) {
            log.error("设置起售数量失败", e);
            return Result.error("设置起售数量失败: " + e.getMessage());
        }
    }

    /**
     * 获取商品筛选分类列表
     *
     * @return 商品筛选分类列表
     */
    @AutoLog(value = "商品表-获取商品筛选分类列表")
    @ApiOperation(value = "商品表-获取商品筛选分类列表", notes = "获取系统中定义的所有商品筛选分类")
    @GetMapping(value = "/filterTypes")
    public Result<List<Map<String, Object>>> getFilterTypes() {
        List<Map<String, Object>> filterTypes = new ArrayList<>();

        // 添加精选
        Map<String, Object> featured = new HashMap<>();
        featured.put("code", InzProduct.FILTER_FEATURED);
        featured.put("name", "精选");
        filterTypes.add(featured);

        // 添加热卖
        Map<String, Object> hotSale = new HashMap<>();
        hotSale.put("code", InzProduct.FILTER_HOT_SALE);
        hotSale.put("name", "热卖");
        filterTypes.add(hotSale);

        // 添加篮球
        Map<String, Object> basketball = new HashMap<>();
        basketball.put("code", InzProduct.FILTER_BASKETBALL);
        basketball.put("name", "篮球");
        filterTypes.add(basketball);

        // 添加足球
        Map<String, Object> football = new HashMap<>();
        football.put("code", InzProduct.FILTER_FOOTBALL);
        football.put("name", "足球");
        filterTypes.add(football);

        // 添加综合收藏
        Map<String, Object> comprehensive = new HashMap<>();
        comprehensive.put("code", InzProduct.FILTER_COMPREHENSIVE);
        comprehensive.put("name", "综合收藏");
        filterTypes.add(comprehensive);

        // 添加其他运动
        Map<String, Object> otherSports = new HashMap<>();
        otherSports.put("code", InzProduct.FILTER_OTHER_SPORTS);
        otherSports.put("name", "其他运动");
        filterTypes.add(otherSports);

        return Result.OK(filterTypes);
    }
}

