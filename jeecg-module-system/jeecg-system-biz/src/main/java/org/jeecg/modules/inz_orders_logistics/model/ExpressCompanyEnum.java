package org.jeecg.modules.inz_orders_logistics.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 快递公司编码枚举
 * 按照快递100API要求的编码
 */
@Getter
@AllArgsConstructor
public enum ExpressCompanyEnum {

    SHUNFENG("shunfeng", "顺丰速运"),
    ZHONGTONG("zhongtong", "中通快递"),
    YUANTONG("yuantong", "圆通速递"),
    SHENTONG("shentong", "申通快递"),
    YUNDA("yunda", "韵达快递"),
    JD("jd", "京东物流"),
    TIANTIAN("tiantian", "天天快递"),
    EMS("ems", "EMS"),
    YOUZHENG("youzhengguonei", "邮政快递包裹"),
    DEBANG("debangwuliu", "德邦"),
    BAISHI("baishiwuliu", "百世快递"),
    JINGDONG("jd", "京东物流"),
    JTEXPRESS("jtexpress", "极兔速递"),
    ZHAIJISONG("zhaijisong", "宅急送"),
    GUOTONG("guotongkuaidi", "国通快递"),
    LIANBANG("lianbangkuaidi", "联邦快递"),
    UNKNOWN("unknown", "未知");

    /**
     * 编码
     */
    private final String code;
    
    /**
     * 名称
     */
    private final String name;

    /**
     * 根据编码获取枚举
     * 
     * @param code 编码
     * @return 枚举值
     */
    public static ExpressCompanyEnum getByCode(String code) {
        if (code == null || code.isEmpty()) {
            return UNKNOWN;
        }
        
        for (ExpressCompanyEnum company : values()) {
            if (company.getCode().equals(code)) {
                return company;
            }
        }
        return UNKNOWN;
    }

    /**
     * 根据名称获取编码
     * 
     * @param name 名称
     * @return 编码
     */
    public static String getCodeByName(String name) {
        if (name == null || name.isEmpty()) {
            return UNKNOWN.getCode();
        }
        
        for (ExpressCompanyEnum company : values()) {
            if (company.getName().contains(name) || name.contains(company.getName())) {
                return company.getCode();
            }
        }
        return UNKNOWN.getCode();
    }
} 