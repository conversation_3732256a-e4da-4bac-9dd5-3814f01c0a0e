-- 登录后自动消息配置SQL
-- 确保inz_message表存在
CREATE TABLE IF NOT EXISTS `inz_message` (
    `id` varchar(32) NOT NULL COMMENT '主键',
    `user_id` varchar(32) DEFAULT NULL COMMENT '用户ID',
    `type` tinyint(4) DEFAULT NULL COMMENT '消息类型（1：平台通知，2：交易动态）',
    `title` varchar(200) DEFAULT NULL COMMENT '消息标题',
    `content` text COMMENT '消息内容',
    `relation_id` varchar(32) DEFAULT NULL COMMENT '关联ID（订单ID等）',
    `status` tinyint(4) DEFAULT '0' COMMENT '消息状态（0：未读，1：已读）',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息中心';

-- 创建登录消息模板配置表
CREATE TABLE IF NOT EXISTS `inz_login_message_template` (
    `id` varchar(32) NOT NULL COMMENT '主键',
    `template_code` varchar(50) NOT NULL COMMENT '模板编码',
    `template_name` varchar(100) NOT NULL COMMENT '模板名称',
    `title` varchar(200) NOT NULL COMMENT '消息标题',
    `content` text NOT NULL COMMENT '消息内容',
    `message_type` tinyint(4) DEFAULT '1' COMMENT '消息类型（1：平台通知，2：交易动态）',
    `is_enabled` tinyint(4) DEFAULT '1' COMMENT '是否启用（0：禁用，1：启用）',
    `send_condition` varchar(100) DEFAULT NULL COMMENT '发送条件（first_login：首次登录，every_login：每次登录，register：注册时）',
    `cache_hours` int(11) DEFAULT '24' COMMENT '缓存时间（小时），防止重复发送',
    `priority` int(11) DEFAULT '0' COMMENT '优先级，数字越大优先级越高',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_template_code` (`template_code`),
    KEY `idx_is_enabled` (`is_enabled`),
    KEY `idx_send_condition` (`send_condition`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录消息模板配置';

-- 插入登录消息模板数据
INSERT INTO `inz_login_message_template` 
(`id`, `template_code`, `template_name`, `title`, `content`, `message_type`, `is_enabled`, `send_condition`, `cache_hours`, `priority`, `create_time`, `create_by`) 
VALUES
-- 欢迎消息模板
('lmt001', 'WELCOME_MESSAGE', '欢迎消息', '欢迎加入卡片交易平台！', 
'🎉 欢迎加入我们的卡片交易平台！

在这里您可以：
• 买卖各种珍稀卡片
• 参与卡片拆包直播
• 与其他收藏爱好者交流
• 享受专业的卡片评级服务

祝您交易愉快！', 1, 1, 'every_login', 24, 100, NOW(), 'system'),

-- 首次登录欢迎消息
('lmt002', 'FIRST_LOGIN_WELCOME', '首次登录欢迎', '欢迎回来！', 
'🎉 欢迎回来！

感谢您再次使用我们的平台，希望您能在这里找到心仪的卡片！

祝您交易愉快！', 1, 1, 'first_login', 24, 90, NOW(), 'system'),

-- 新手指南
('lmt003', 'NEW_USER_GUIDE', '新手指南', '新手指南', 
'📖 新手指南

作为新用户，建议您：
1. 完善个人资料，提升账户安全性
2. 浏览热门卡片，了解市场行情
3. 关注感兴趣的店铺和卡片
4. 参与社区讨论，学习收藏知识
5. 阅读平台规则，确保交易安全

如有疑问，请随时联系客服！', 1, 1, 'register', 720, 80, NOW(), 'system'),

-- 平台公告
('lmt004', 'PLATFORM_ANNOUNCEMENT', '平台公告', '平台最新公告', 
'📢 平台最新公告

• 平台持续优化用户体验
• 新增卡片评级服务
• 加强交易安全保障
• 推出积分商城功能

更多详情请查看公告中心！', 1, 1, 'every_login', 168, 70, NOW(), 'system'),

-- 举报有奖
('lmt005', 'REPORT_REWARD', '举报有奖', '举报有奖活动', 
'🎁 举报有奖活动

为维护平台交易环境，我们推出举报有奖活动：

举报以下行为可获得奖励：
• 虚假商品信息 - 奖励50积分
• 恶意刷单行为 - 奖励100积分
• 违规交易行为 - 奖励200积分
• 其他违规行为 - 奖励30积分

举报方式：联系在线客服
感谢您为平台建设贡献力量！', 1, 1, 'every_login', 720, 60, NOW(), 'system'),

-- 平台规则提醒
('lmt006', 'PLATFORM_RULES', '平台规则', '平台交易规则提醒', 
'⚠️ 平台交易规则提醒

为保障您的交易安全，请遵守以下规则：

1. 禁止发布虚假商品信息
2. 禁止恶意刷单和虚假交易
3. 禁止线下私下交易
4. 禁止发布违法违规内容
5. 尊重其他用户，文明交流

违规行为将面临警告、限制或封号处理。
详细规则请查看用户协议！', 1, 1, 'every_login', 360, 50, NOW(), 'system'),

-- 安全提醒
('lmt007', 'SECURITY_REMINDER', '安全提醒', '账户安全提醒', 
'🔒 账户安全提醒

为保障您的账户安全，请注意：

• 定期修改登录密码
• 不要向他人透露账户信息
• 发现异常登录及时联系客服
• 绑定手机号和邮箱
• 开启登录验证功能

如发现账户异常，请立即联系客服！', 1, 1, 'every_login', 168, 40, NOW(), 'system');

-- 插入一些示例消息数据（可选）
INSERT INTO `inz_message` 
(`id`, `user_id`, `type`, `title`, `content`, `relation_id`, `status`, `create_time`, `create_by`) 
VALUES
-- 系统欢迎消息示例
('msg_welcome_001', 'demo_user_001', 1, '欢迎加入卡片交易平台！', 
'🎉 欢迎加入我们的卡片交易平台！

在这里您可以：
• 买卖各种珍稀卡片
• 参与卡片拆包直播
• 与其他收藏爱好者交流
• 享受专业的卡片评级服务

祝您交易愉快！', NULL, 0, NOW(), 'system'),

-- 举报有奖消息示例
('msg_report_001', 'demo_user_001', 1, '举报有奖活动', 
'🎁 举报有奖活动

为维护平台交易环境，我们推出举报有奖活动：

举报以下行为可获得奖励：
• 虚假商品信息 - 奖励50积分
• 恶意刷单行为 - 奖励100积分
• 违规交易行为 - 奖励200积分
• 其他违规行为 - 奖励30积分

举报方式：联系在线客服
感谢您为平台建设贡献力量！', NULL, 0, NOW(), 'system'),

-- 安全提醒消息示例
('msg_security_001', 'demo_user_001', 1, '账户安全提醒', 
'🔒 账户安全提醒

为保障您的账户安全，请注意：

• 定期修改登录密码
• 不要向他人透露账户信息
• 发现异常登录及时联系客服
• 绑定手机号和邮箱
• 开启登录验证功能

如发现账户异常，请立即联系客服！', NULL, 0, NOW(), 'system');

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS `idx_inz_message_user_type` ON `inz_message` (`user_id`, `type`);
CREATE INDEX IF NOT EXISTS `idx_inz_message_status_time` ON `inz_message` (`status`, `create_time`);

-- 创建登录消息发送记录表（用于追踪消息发送历史）
CREATE TABLE IF NOT EXISTS `inz_login_message_log` (
    `id` varchar(32) NOT NULL COMMENT '主键',
    `user_id` varchar(32) NOT NULL COMMENT '用户ID',
    `template_code` varchar(50) NOT NULL COMMENT '模板编码',
    `message_id` varchar(32) DEFAULT NULL COMMENT '消息ID',
    `send_time` datetime NOT NULL COMMENT '发送时间',
    `send_result` tinyint(4) DEFAULT '1' COMMENT '发送结果（0：失败，1：成功）',
    `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_template` (`user_id`, `template_code`),
    KEY `idx_send_time` (`send_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录消息发送记录';

-- 插入配置项（控制消息发送开关）
INSERT IGNORE INTO `inz_config` (`id`, `name`, `code`, `value`, `create_time`, `create_by`) VALUES
('config_login_msg_001', '登录消息发送开关', 'LOGIN_MESSAGE_ENABLED', '1', NOW(), 'system'),
('config_login_msg_002', '新用户消息发送开关', 'NEW_USER_MESSAGE_ENABLED', '1', NOW(), 'system'),
('config_login_msg_003', '消息发送异步处理', 'MESSAGE_ASYNC_ENABLED', '1', NOW(), 'system'),
('config_login_msg_004', '消息发送失败重试次数', 'MESSAGE_RETRY_COUNT', '3', NOW(), 'system');
