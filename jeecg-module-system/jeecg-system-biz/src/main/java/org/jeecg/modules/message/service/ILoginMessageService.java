package org.jeecg.modules.message.service;

/**
 * @Description: 登录后消息发送服务
 * @Author: jeecg-boot
 * @Date: 2025-01-24
 * @Version: V1.0
 */
public interface ILoginMessageService {

    /**
     * 发送用户登录后的欢迎消息
     * @param userId 用户ID
     * @param username 用户名
     * @param isFirstLogin 是否首次登录
     */
    void sendWelcomeMessage(String userId, String username, boolean isFirstLogin);

    /**
     * 发送新用户注册后的欢迎消息
     * @param userId 用户ID
     * @param username 用户名
     */
    void sendNewUserWelcomeMessage(String userId, String username);

    /**
     * 发送平台公告消息
     * @param userId 用户ID
     * @param username 用户名
     */
    void sendAnnouncementMessage(String userId, String username);

    /**
     * 发送举报有奖消息
     * @param userId 用户ID
     * @param username 用户名
     */
    void sendReportRewardMessage(String userId, String username);

    /**
     * 发送平台规则消息
     * @param userId 用户ID
     * @param username 用户名
     */
    void sendPlatformRulesMessage(String userId, String username);

    /**
     * 发送安全提醒消息
     * @param userId 用户ID
     * @param username 用户名
     */
    void sendSecurityReminderMessage(String userId, String username);

    /**
     * 批量发送登录后的所有自动消息
     * @param userId 用户ID
     * @param username 用户名
     * @param isFirstLogin 是否首次登录
     */
    void sendAllLoginMessages(String userId, String username, boolean isFirstLogin);
}
