package org.jeecg.modules.inz_card_live.service;

import org.jeecg.modules.inz_card_live.entity.InzCardLive;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * @Description: 直播表
 * @Author: jeecg-boot
 * @Date:   2025-06-20
 * @Version: V1.0
 */
public interface IInzCardLiveService extends IService<InzCardLive> {
    
    /**
     * 通过店铺ID查询直播信息
     * @param page 分页参数
     * @param storeId 店铺ID
     * @return 分页结果
     */
    IPage<InzCardLive> queryCardLivesByStoreId(Page<InzCardLive> page, String storeId);
}
