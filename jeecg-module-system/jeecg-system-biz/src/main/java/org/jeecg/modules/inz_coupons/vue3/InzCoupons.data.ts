import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '优惠券名称',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '优惠券类型（1：固定金额，2：折扣）',
    align:"center",
    dataIndex: 'type'
   },
   {
    title: '优惠金额/折扣率',
    align:"center",
    dataIndex: 'amount'
   },
   {
    title: '最低使用金额',
    align:"center",
    dataIndex: 'minAmount'
   },
   {
    title: '店铺ID',
    align:"center",
    dataIndex: 'storeId'
   },
   {
    title: '发行总量',
    align:"center",
    dataIndex: 'totalQuantity'
   },
   {
    title: '剩余数量',
    align:"center",
    dataIndex: 'remainingQuantity'
   },
   {
    title: '生效时间',
    align:"center",
    dataIndex: 'startTime'
   },
   {
    title: '失效时间',
    align:"center",
    dataIndex: 'endTime'
   },
   {
    title: '使用说明',
    align:"center",
    dataIndex: 'description'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '优惠券名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入优惠券名称!'},
          ];
     },
  },
  {
    label: '优惠券类型（1：固定金额，2：折扣）',
    field: 'type',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入优惠券类型（1：固定金额，2：折扣）!'},
          ];
     },
  },
  {
    label: '优惠金额/折扣率',
    field: 'amount',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入优惠金额/折扣率!'},
          ];
     },
  },
  {
    label: '最低使用金额',
    field: 'minAmount',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入最低使用金额!'},
          ];
     },
  },
  {
    label: '店铺ID',
    field: 'storeId',
    component: 'Input',
  },
  {
    label: '发行总量',
    field: 'totalQuantity',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入发行总量!'},
          ];
     },
  },
  {
    label: '剩余数量',
    field: 'remainingQuantity',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入剩余数量!'},
          ];
     },
  },
  {
    label: '生效时间',
    field: 'startTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入生效时间!'},
          ];
     },
  },
  {
    label: '失效时间',
    field: 'endTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入失效时间!'},
          ];
     },
  },
  {
    label: '使用说明',
    field: 'description',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '优惠券名称',order: 0,view: 'text', type: 'string',},
  type: {title: '优惠券类型（1：固定金额，2：折扣）',order: 1,view: 'number', type: 'number',},
  amount: {title: '优惠金额/折扣率',order: 2,view: 'number', type: 'number',},
  minAmount: {title: '最低使用金额',order: 3,view: 'number', type: 'number',},
  storeId: {title: '店铺ID',order: 4,view: 'text', type: 'string',},
  totalQuantity: {title: '发行总量',order: 5,view: 'number', type: 'number',},
  remainingQuantity: {title: '剩余数量',order: 6,view: 'number', type: 'number',},
  startTime: {title: '生效时间',order: 7,view: 'datetime', type: 'string',},
  endTime: {title: '失效时间',order: 8,view: 'datetime', type: 'string',},
  description: {title: '使用说明',order: 9,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}