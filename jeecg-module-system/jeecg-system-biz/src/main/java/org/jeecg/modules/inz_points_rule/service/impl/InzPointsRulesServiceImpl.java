package org.jeecg.modules.inz_points_rule.service.impl;

import org.jeecg.modules.inz_points_rule.entity.InzPointsRules;
import org.jeecg.modules.inz_points_rule.mapper.InzPointsRulesMapper;
import org.jeecg.modules.inz_points_rule.service.IInzPointsRulesService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 积分规则表
 * @Author: jeecg-boot
 * @Date:   2025-06-15
 * @Version: V1.0
 */
@Service
public class InzPointsRulesServiceImpl extends ServiceImpl<InzPointsRulesMapper, InzPointsRules> implements IInzPointsRulesService {

}
