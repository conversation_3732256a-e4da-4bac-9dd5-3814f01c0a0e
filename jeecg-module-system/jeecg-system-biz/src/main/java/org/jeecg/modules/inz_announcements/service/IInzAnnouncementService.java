package org.jeecg.modules.inz_announcements.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.inz_announcements.entity.InzAnnouncement;

import java.util.List;

/**
 * @Description: 平台公告管理
 * @Author: jeecg-boot
 * @Date: 2025-01-24
 * @Version: V1.0
 */
public interface IInzAnnouncementService extends IService<InzAnnouncement> {

    /**
     * 发布公告
     * @param id 公告ID
     * @return 是否成功
     */
    boolean publishAnnouncement(String id);

    /**
     * 下线公告
     * @param id 公告ID
     * @return 是否成功
     */
    boolean offlineAnnouncement(String id);

    /**
     * 批量发布公告
     * @param ids 公告ID列表
     * @return 是否成功
     */
    boolean batchPublish(List<String> ids);

    /**
     * 批量下线公告
     * @param ids 公告ID列表
     * @return 是否成功
     */
    boolean batchOffline(List<String> ids);

    /**
     * 置顶/取消置顶
     * @param id 公告ID
     * @param isTop 是否置顶
     * @return 是否成功
     */
    boolean toggleTop(String id, Integer isTop);

    /**
     * 获取有效公告列表（前端展示用）
     * @param page 分页参数
     * @param type 公告类型
     * @param targetUserType 目标用户类型
     * @return 分页结果
     */
    IPage<InzAnnouncement> getValidAnnouncements(Page<InzAnnouncement> page, Integer type, Integer targetUserType);

    /**
     * 获取置顶公告
     * @param targetUserType 目标用户类型
     * @return 置顶公告列表
     */
    List<InzAnnouncement> getTopAnnouncements(Integer targetUserType);

    /**
     * 获取最新公告
     * @param limit 限制数量
     * @param targetUserType 目标用户类型
     * @return 最新公告列表
     */
    List<InzAnnouncement> getLatestAnnouncements(Integer limit, Integer targetUserType);

    /**
     * 获取公告详情（增加阅读次数）
     * @param id 公告ID
     * @return 公告详情
     */
    InzAnnouncement getAnnouncementDetail(String id);

    /**
     * 统计各类型公告数量
     * @return 统计结果
     */
    List<Object> countByType();

    /**
     * 增加公告阅读次数
     * @param id 公告ID
     * @return 当前阅读次数
     */
    Integer incrementReadCount(String id);

}
