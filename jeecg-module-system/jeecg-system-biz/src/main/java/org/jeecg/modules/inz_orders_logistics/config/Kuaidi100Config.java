package org.jeecg.modules.inz_orders_logistics.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "kuaidi100")
public class Kuaidi100Config {
    /**
     * 查询接口URL
     */
    private String apiUrl = "https://poll.kuaidi100.com/poll/query.do";
    
    /**
     * 订阅接口URL
     */
    private String subscribeUrl = "https://poll.kuaidi100.com/poll";
    
    /**
     * 授权key
     */
    private String customer;
    
    /**
     * 授权密钥
     */
    private String key;
    
    /**
     * 回调地址，系统接收物流推送的接口地址
     */
    private String callbackUrl;
    
    /**
     * 接收短信模板ID
     */
    private String smsTemplateId;

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }
    
    public String getSubscribeUrl() {
        return subscribeUrl;
    }
    
    public void setSubscribeUrl(String subscribeUrl) {
        this.subscribeUrl = subscribeUrl;
    }

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }
    
    public String getSmsTemplateId() {
        return smsTemplateId;
    }
    
    public void setSmsTemplateId(String smsTemplateId) {
        this.smsTemplateId = smsTemplateId;
    }
} 