package org.jeecg.modules.after_sale.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.Query;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.after_sale.entity.AfterSale;
import org.jeecg.modules.after_sale.mapper.AfterSaleMapper;
import org.jeecg.modules.after_sale.service.IAfterSaleService;
import org.jeecg.modules.inz_store.entity.InzStore;
import org.jeecg.modules.inz_store.service.IInzStoreService;
import org.jeecg.modules.inz_users_fronts.service.IInzUsersFrontsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 售后表
 * @Author: jeecg-boot
 * @Date:   2025-06-20
 * @Version: V1.0
 */
@Service
public class AfterSaleServiceImpl extends ServiceImpl<AfterSaleMapper, AfterSale> implements IAfterSaleService {

    @Autowired
    private IInzUsersFrontsService inzUsersFrontsService;

    @Autowired
    private IInzStoreService inzStoreService;

    @Override
    public List<AfterSale> getAfterSalesByUsername(String username) {
        // 通过用户名获取用户ID
        String userId = inzUsersFrontsService.getUserIdByUsername(username);
        if (userId == null) {
            return null;
        }
        
        LambdaQueryWrapper<AfterSale> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AfterSale::getUserId, userId);
        return this.list(queryWrapper);
    }

    @Override
    public List<AfterSale> getAfterSalesByStoreName(String storeName) {

        QueryWrapper<InzStore> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("name", storeName);
        InzStore store = inzStoreService.getOne(queryWrapper1);
        
        // 检查店铺是否存在
        if (store == null) {
            return null;
        }
        
        String storeId = store.getId();
        // 通过店铺名称获取店铺ID
        if (storeId == null) {
            return null;
        }

        LambdaQueryWrapper<AfterSale> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AfterSale::getStoreId, storeId);
        return this.list(queryWrapper);
    }

    @Override
    public List<AfterSale> getAfterSalesByStoreId(String storeId) {
        if (org.apache.commons.lang3.StringUtils.isBlank(storeId)) {
            return java.util.Collections.emptyList();
        }
        com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<AfterSale> queryWrapper = new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
        queryWrapper.eq("store_id", storeId);
        return this.list(queryWrapper);
    }
    
    @Override
    public AfterSale getAfterSaleByOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            return null;
        }
        
        LambdaQueryWrapper<AfterSale> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AfterSale::getOrderNo, orderNo);
        return this.getOne(queryWrapper);
    }
}
