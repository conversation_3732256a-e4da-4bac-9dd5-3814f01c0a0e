import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '店主用户id',
    align:"center",
    dataIndex: 'userId'
   },
   {
    title: '店铺名称',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '店铺头像',
    align:"center",
    dataIndex: 'avatar'
   },
   {
    title: '店铺banner',
    align:"center",
    dataIndex: 'banner'
   },
   {
    title: '店铺描述',
    align:"center",
    dataIndex: 'description'
   },
   {
    title: '店铺公告',
    align:"center",
    dataIndex: 'announcement'
   },
   {
    title: '营业执照',
    align:"center",
    dataIndex: 'businessLicense'
   },
   {
    title: '联系电话',
    align:"center",
    dataIndex: 'phone'
   },
   {
    title: '省份',
    align:"center",
    dataIndex: 'province'
   },
   {
    title: '城市',
    align:"center",
    dataIndex: 'city'
   },
   {
    title: '区县',
    align:"center",
    dataIndex: 'district'
   },
   {
    title: '详细地址',
    align:"center",
    dataIndex: 'address'
   },
   {
    title: '店铺分类标签，逗号分隔',
    align:"center",
    dataIndex: 'tags'
   },
   {
    title: '是否开通直播（0-未开通，1-已开通）',
    align:"center",
    dataIndex: 'liveEnabled'
   },
   {
    title: '店铺等级',
    align:"center",
    dataIndex: 'level'
   },
   {
    title: '店铺积分',
    align:"center",
    dataIndex: 'points'
   },
   {
    title: '状态(0-关闭,1-营业中)',
    align:"center",
    dataIndex: 'status'
   },
   {
    title: '店铺类型（1-个人，2-企业）',
    align:"center",
    dataIndex: 'type'
   },
   {
    title: '认证状态（0-未认证，1-已认证）',
    align:"center",
    dataIndex: 'verified'
   },
   {
    title: '粉丝数',
    align:"center",
    dataIndex: 'fanscount'
   },
   {
    title: '商品数量',
    align:"center",
    dataIndex: 'productsCount'
   },
   {
    title: '总销量',
    align:"center",
    dataIndex: 'salesCount'
   },
   {
    title: '店铺评分',
    align:"center",
    dataIndex: 'rating'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '店主用户id',
    field: 'userId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入店主用户id!'},
          ];
     },
  },
  {
    label: '店铺名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入店铺名称!'},
          ];
     },
  },
  {
    label: '店铺头像',
    field: 'avatar',
    component: 'Input',
  },
  {
    label: '店铺banner',
    field: 'banner',
    component: 'Input',
  },
  {
    label: '店铺描述',
    field: 'description',
    component: 'InputTextArea',
  },
  {
    label: '店铺公告',
    field: 'announcement',
    component: 'Input',
  },
  {
    label: '营业执照',
    field: 'businessLicense',
    component: 'Input',
  },
  {
    label: '联系电话',
    field: 'phone',
    component: 'Input',
  },
  {
    label: '省份',
    field: 'province',
    component: 'Input',
  },
  {
    label: '城市',
    field: 'city',
    component: 'Input',
  },
  {
    label: '区县',
    field: 'district',
    component: 'Input',
  },
  {
    label: '详细地址',
    field: 'address',
    component: 'Input',
  },
  {
    label: '店铺分类标签，逗号分隔',
    field: 'tags',
    component: 'Input',
  },
  {
    label: '是否开通直播（0-未开通，1-已开通）',
    field: 'liveEnabled',
    component: 'InputNumber',
  },
  {
    label: '店铺等级',
    field: 'level',
    component: 'InputNumber',
  },
  {
    label: '店铺积分',
    field: 'points',
    component: 'InputNumber',
  },
  {
    label: '状态(0-关闭,1-营业中)',
    field: 'status',
    component: 'InputNumber',
  },
  {
    label: '店铺类型（1-个人，2-企业）',
    field: 'type',
    component: 'InputNumber',
  },
  {
    label: '认证状态（0-未认证，1-已认证）',
    field: 'verified',
    component: 'InputNumber',
  },
  {
    label: '粉丝数',
    field: 'fanscount',
    component: 'InputNumber',
  },
  {
    label: '商品数量',
    field: 'productsCount',
    component: 'InputNumber',
  },
  {
    label: '总销量',
    field: 'salesCount',
    component: 'InputNumber',
  },
  {
    label: '店铺评分',
    field: 'rating',
    component: 'InputNumber',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  userId: {title: '店主用户id',order: 0,view: 'text', type: 'string',},
  name: {title: '店铺名称',order: 1,view: 'text', type: 'string',},
  avatar: {title: '店铺头像',order: 2,view: 'text', type: 'string',},
  banner: {title: '店铺banner',order: 3,view: 'text', type: 'string',},
  description: {title: '店铺描述',order: 4,view: 'textarea', type: 'string',},
  announcement: {title: '店铺公告',order: 5,view: 'text', type: 'string',},
  businessLicense: {title: '营业执照',order: 6,view: 'text', type: 'string',},
  phone: {title: '联系电话',order: 7,view: 'text', type: 'string',},
  province: {title: '省份',order: 8,view: 'text', type: 'string',},
  city: {title: '城市',order: 9,view: 'text', type: 'string',},
  district: {title: '区县',order: 10,view: 'text', type: 'string',},
  address: {title: '详细地址',order: 11,view: 'text', type: 'string',},
  tags: {title: '店铺分类标签，逗号分隔',order: 12,view: 'text', type: 'string',},
  liveEnabled: {title: '是否开通直播（0-未开通，1-已开通）',order: 13,view: 'number', type: 'number',},
  level: {title: '店铺等级',order: 14,view: 'number', type: 'number',},
  points: {title: '店铺积分',order: 15,view: 'number', type: 'number',},
  status: {title: '状态(0-关闭,1-营业中)',order: 16,view: 'number', type: 'number',},
  type: {title: '店铺类型（1-个人，2-企业）',order: 17,view: 'number', type: 'number',},
  verified: {title: '认证状态（0-未认证，1-已认证）',order: 18,view: 'number', type: 'number',},
  fanscount: {title: '粉丝数',order: 19,view: 'number', type: 'number',},
  productsCount: {title: '商品数量',order: 20,view: 'number', type: 'number',},
  salesCount: {title: '总销量',order: 21,view: 'number', type: 'number',},
  rating: {title: '店铺评分',order: 22,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}