package org.jeecg.modules.after_sale.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.after_sale.entity.AfterSale;
import org.jeecg.modules.after_sale.service.IAfterSaleService;
import org.jeecg.modules.auditRecord.entity.AuditRecord;
import org.jeecg.modules.auditRecord.service.IAuditRecordService;
import org.jeecg.modules.inz_store.entity.InzStore;
import org.jeecg.modules.inz_store.service.IInzStoreService;
import org.jeecg.modules.inz_users_fronts.service.IInzUsersFrontsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * @Description: 售后表
 * @Author: jeecg-boot
 * @Date: 2025-06-20
 * @Version: V1.0
 */
@Api(tags = "售后表")
@RestController
@RequestMapping("/after_sale/afterSale")
@Slf4j
public class AfterSaleController extends JeecgController<AfterSale, IAfterSaleService> {
    @Autowired
    private IAfterSaleService afterSaleService;

    @Autowired
    private IAuditRecordService auditRecordService;

    @Autowired
    private IInzUsersFrontsService inzUsersFrontsService;

    /**
     * 分页列表查询
     *
     * @param afterSale
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "售后表-分页列表查询")
    @ApiOperation(value = "售后表-分页列表查询", notes = "售后表-分页列表查询")
    @GetMapping(value = "/list")
    @org.jeecg.common.aspect.annotation.PermissionData(pageComponent = "after_sale/AfterSaleList")
    public Result<IPage<AfterSale>> queryPageList(AfterSale afterSale,
                                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                  @RequestParam(name = "storeName", required = false) String storeName,
                                                  @RequestParam(name = "userName", required = false) String username,
                                                  @RequestParam(name = "orderNo", required = false) String orderNo,
                                                  @RequestParam(name = "createTime", required = false) String createTime,
                                                  HttpServletRequest req) {
        Page<AfterSale> page = new Page<AfterSale>(pageNo, pageSize);
        IPage<AfterSale> pageList;

        // 创建查询条件
        QueryWrapper<AfterSale> queryWrapper = QueryGenerator.initQueryWrapper(afterSale, req.getParameterMap());

        // 处理店铺名称筛选
        if (StringUtils.isNotBlank(storeName)) {
            // 通过店铺名称获取售后数据
            List<AfterSale> afterSaleList = afterSaleService.getAfterSalesByStoreName(storeName);
            if (afterSaleList == null || afterSaleList.isEmpty()) {
                // 如果找不到售后数据，返回空结果
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }

            // 获取所有售后ID
            List<String> afterSaleIds = new ArrayList<>();
            for (AfterSale sale : afterSaleList) {
                afterSaleIds.add(sale.getId());
            }

            // 添加ID条件
            queryWrapper.in("id", afterSaleIds);
        }

        // 处理用户名称筛选
        if (StringUtils.isNotBlank(username)) {
            String userId = inzUsersFrontsService.getUserIdByUsername(username);
            if (StringUtils.isNotBlank(userId)) {
                queryWrapper.eq("user_id", userId);
            } else {
                // 如果找不到用户，返回空结果
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }
        }

        // 处理订单编号筛选
        if (StringUtils.isNotBlank(orderNo)) {
            queryWrapper.eq("order_no", orderNo);
        }

        // 处理创建时间范围筛选
        if (StringUtils.isNotBlank(createTime)) {
            String[] createTimeArr = createTime.split(",");
            if (createTimeArr.length == 2) {
                String startTime = createTimeArr[0].trim();
                String endTime = createTimeArr[1].trim();
                if (StringUtils.isNotBlank(startTime)) {
                    queryWrapper.ge("create_time", startTime);
                }
                if (StringUtils.isNotBlank(endTime)) {
                    queryWrapper.le("create_time", endTime);
                }
            }
        }

        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        if (sysUser != null) {
            // 判断是否为管理员角色
            if (!isAdmin(sysUser)) {
                log.info("非管理员用户访问售后列表，进行数据权限过滤");

                // 获取用户手机号，用于查询前端用户
                String phone = sysUser.getPhone();

                // 通过手机号查找对应的前端用户
                String userId = null;
                if (StringUtils.isNotBlank(phone)) {
                    try {
                        // 尝试通过手机号查询前端用户ID
                        userId = inzUsersFrontsService.getUserIdByPhone(phone);
                        log.info("通过手机号[{}]查询到前端用户ID: {}", phone, userId);
                    } catch (Exception e) {
                        log.warn("通过手机号查询前端用户失败: {}", e.getMessage());
                    }
                } else {
                    log.warn("后台用户[{}]没有关联手机号", sysUser.getUsername());
                }

                // 如果找到了前端用户ID，查询其关联的店铺
                if (StringUtils.isNotBlank(userId)) {
                    // 查询用户关联的店铺
                    IInzStoreService inzStoreService =
                            (IInzStoreService) SpringContextUtils.getBean(IInzStoreService.class);
                    InzStore store = inzStoreService.getOne(
                            new QueryWrapper<InzStore>().eq("user_id", userId));

                    if (store != null) {
                        // 用户有关联店铺，只能查看自己店铺的售后
                        log.info("用户[{}]关联店铺[{}]，只能查看该店铺售后", userId, store.getId());
                        queryWrapper.eq("store_id", store.getId());
                    } else {
                        log.warn("用户[{}]没有关联店铺，返回空结果", userId);
                        return Result.OK(new Page<>(pageNo, pageSize, 0));
                    }
                } else {
                    log.warn("未找到后台用户[{}]对应的前端用户，返回空结果", sysUser.getUsername());
                    return Result.OK(new Page<>(pageNo, pageSize, 0));
                }
            } else {
                log.info("管理员用户访问售后列表，不进行数据权限过滤");
                // 管理员可以看到所有售后记录，无需额外过滤
            }
        }

        // 执行查询
        pageList = afterSaleService.page(page, queryWrapper);

        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param afterSale
     * @return
     */
    @AutoLog(value = "售后表-添加")
    @ApiOperation(value = "售后表-添加", notes = "售后表-添加")
    @RequiresPermissions("after_sale:inz_after_sale:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody AfterSale afterSale) {
        // 保存售后申请
        afterSale.setStatus(1); // 设置初始状态为待审核
        afterSaleService.save(afterSale);

        // 自动提交审核
        Result<AuditRecord> auditResult = auditRecordService.submitAftersaleForAudit(afterSale.getId());
        if (auditResult.isSuccess()) {
            return Result.OK("售后申请已提交，等待审核");
        } else {
            return Result.OK("售后申请已保存，但提交审核失败：" + auditResult.getMessage());
        }
    }

    /**
     * 编辑
     *
     * @param afterSale
     * @return
     */
    @AutoLog(value = "售后表-编辑")
    @ApiOperation(value = "售后表-编辑", notes = "售后表-编辑")
    @RequiresPermissions("after_sale:inz_after_sale:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody AfterSale afterSale) {
        // 获取原始售后信息
        AfterSale oldAfterSale = afterSaleService.getById(afterSale.getId());
        if (oldAfterSale == null) {
            return Result.error("售后申请不存在");
        }

        // 检查售后状态
        if (oldAfterSale.getStatus() != 1) { // 假设1是待审核状态
            return Result.error("只能编辑待审核的售后申请");
        }

        // 更新售后信息
        afterSaleService.updateById(afterSale);

        // 重新提交审核
        Result<AuditRecord> auditResult = auditRecordService.submitAftersaleForAudit(afterSale.getId());
        if (auditResult.isSuccess()) {
            return Result.OK("售后申请已更新，重新提交审核");
        } else {
            return Result.OK("售后申请已更新，但重新提交审核失败：" + auditResult.getMessage());
        }
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "售后表-通过id删除")
    @ApiOperation(value = "售后表-通过id删除", notes = "售后表-通过id删除")
    @RequiresPermissions("after_sale:inz_after_sale:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        // 获取售后信息
        AfterSale afterSale = afterSaleService.getById(id);
        if (afterSale == null) {
            return Result.error("售后申请不存在");
        }

        // 检查售后状态
        if (afterSale.getStatus() != 1) { // 假设1是待审核状态
            return Result.error("只能删除待审核的售后申请");
        }

        QueryWrapper<AuditRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("aftersale_id", id);
        auditRecordService.remove(queryWrapper);

        // 删除售后申请
        afterSaleService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "售后表-批量删除")
    @ApiOperation(value = "售后表-批量删除", notes = "售后表-批量删除")
    @RequiresPermissions("after_sale:inz_after_sale:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        // TODO: 检查售后状态和删除关联的审核记录
        this.afterSaleService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "售后表-通过id查询")
    @ApiOperation(value = "售后表-通过id查询", notes = "售后表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<AfterSale> queryById(@RequestParam(name = "id", required = true) String id) {
        AfterSale afterSale = afterSaleService.getById(id);
        if (afterSale == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(afterSale);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param afterSale
     */
    @RequiresPermissions("after_sale:inz_after_sale:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AfterSale afterSale) {
        return super.exportXls(request, afterSale, AfterSale.class, "售后表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("after_sale:inz_after_sale:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AfterSale.class);
    }

    /**
     * 取消售后申请
     */
    @ApiOperation(value = "取消售后申请")
    @GetMapping(value = "/cancel")
    public Result<String> cancel(@RequestParam(name = "id", required = true) String id) {
        // 获取售后信息
        AfterSale afterSale = afterSaleService.getById(id);
        if (afterSale == null) {
            return Result.error("售后申请不存在");
        }

        // 检查售后状态
        if (afterSale.getStatus() != 1) { // 假设1是待审核状态
            return Result.error("只能取消待审核的售后申请");
        }

        // 调用审核服务的取消审核方法
        return auditRecordService.cancelAftersaleAudit(id);
    }

    /**
     * 查询售后审核状态
     */
    @ApiOperation(value = "查询售后审核状态")
    @GetMapping(value = "/checkAuditStatus")
    public Result<?> checkAuditStatus(@RequestParam(name = "id", required = true) String id) {
        // 获取售后信息
        AfterSale afterSale = afterSaleService.getById(id);
        if (afterSale == null) {
            return Result.error("售后申请不存在");
        }

        // 获取审核记录
        AuditRecord auditRecord = auditRecordService.getAuditRecordByAftersaleId(id);
        if (auditRecord == null) {
            return Result.error("未找到审核记录");
        }

        return Result.OK(auditRecord);
    }

    /**
     * 通过用户名称查询售后订单
     *
     * @param username 用户名称
     * @param pageNo   页码
     * @param pageSize 每页数量
     * @param req      请求
     * @return 售后订单列表
     */
    @ApiOperation(value = "售后表-通过用户名称查询", notes = "售后表-通过用户名称查询")
    @GetMapping(value = "/listByUserName")
    public Result<IPage<AfterSale>> queryPageListByUsername(
            @RequestParam(name = "userName") String username,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            HttpServletRequest req) {
        // 查询售后订单
        List<AfterSale> afterSales = afterSaleService.getAfterSalesByUsername(username);
        if (afterSales == null || afterSales.isEmpty()) {
            return Result.error("未找到该用户的售后订单: " + username);
        }

        // 设置分页
        Page<AfterSale> page = new Page<>(pageNo, pageSize);
        IPage<AfterSale> pageList = new Page<>(pageNo, pageSize);
        pageList.setRecords(afterSales);
        pageList.setTotal(afterSales.size()); // 设置总数
        return Result.OK(pageList);
    }

    @ApiOperation(value = "售后表-通过店铺名称查询", notes = "售后表-通过店铺名称查询")
    @GetMapping(value = "/listByStoreName")
    public Result<IPage<AfterSale>> queryPageListByStoreName(
            @RequestParam(name = "storeName") String storeName,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            HttpServletRequest req) {
        // 判断名称是否为空
        if (StringUtils.isBlank(storeName)) {
            return Result.error("店铺名称不能为空");
        }

        try {
            // 创建分页对象
            Page<AfterSale> page = new Page<>(pageNo, pageSize);

            // 调用服务查询售后数据
            List<AfterSale> afterSaleList = afterSaleService.getAfterSalesByStoreName(storeName);

            if (afterSaleList == null) {
                return Result.error("未找到该店铺的售后数据");
            }

            // 手动分页处理
            IPage<AfterSale> pageList = new Page<>(pageNo, pageSize);

            // 计算开始和结束索引
            int total = afterSaleList.size();
            int start = (pageNo - 1) * pageSize;
            int end = Math.min(start + pageSize, total);

            // 如果开始索引超过总数，返回空列表
            if (start >= total) {
                pageList.setRecords(new ArrayList<>());
            } else {
                // 截取当前页的数据
                pageList.setRecords(afterSaleList.subList(start, end));
            }

            // 设置分页信息
            pageList.setTotal(total);
            pageList.setCurrent(pageNo);
            pageList.setSize(pageSize);

            return Result.OK(pageList);
        } catch (Exception e) {
            log.error("查询售后数据失败", e);
            return Result.error("查询售后数据失败: " + e.getMessage());
        }
    }

    /**
     * 判断用户是否为管理员
     *
     * @param user 登录用户
     * @return 是否为管理员
     */
    private boolean isAdmin(org.jeecg.common.system.vo.LoginUser user) {
        if (user == null) {
            return false;
        }
        String roleCode = user.getRoleCode();
        // 判断角色编码中是否包含admin关键字（不区分大小写）
        return StringUtils.isNotBlank(roleCode) &&
                (roleCode.toLowerCase().contains("admin") || "admin".equalsIgnoreCase(user.getUsername()));
    }

    @Data
    public static class AfterSaleDTO {
        private String id;
        private String handleNote;
    }

    /**
     * 商家处理售后申请（同意）
     *
     * @return 处理结果
     */
    @ApiOperation(value = "商家同意售后申请", notes = "商家同意售后申请并填写处理备注")
    @PostMapping(value = "/approve")
    public Result<?> approveAfterSale(@RequestBody AfterSaleDTO dto) {
        // 获取售后信息
        AfterSale afterSale = afterSaleService.getById(dto.getId());
        if (afterSale == null) {
            return Result.error("售后申请不存在");
        }

        // 检查售后状态
        if (afterSale.getStatus() != 1) {
            return Result.error("只能处理待审核状态的售后申请");
        }

        // 更新售后状态为审核通过
        afterSale.setStatus(2); // 审核通过状态
        afterSale.setHandleNote(dto.getHandleNote());
        afterSale.setAuditTime(new Date());
        afterSale.setUpdateTime(new Date());
        afterSale.setUpdateBy(getLoginUser().getUsername());

        // 保存更新
        boolean success = afterSaleService.updateById(afterSale);
        if (!success) {
            return Result.error("处理售后申请失败");
        }

        // 同时更新审核记录
        AuditRecord auditRecord = auditRecordService.getAuditRecordByAftersaleId(dto.id);
        if (auditRecord != null) {
            return auditRecordService.performAudit(auditRecord.getId(), 1, dto.handleNote);
        } else {
            log.warn("售后申请[{}]没有关联的审核记录", dto.id);
            return Result.OK("售后申请已通过，但未找到关联的审核记录");
        }
    }

    /**
     * 商家处理售后申请（拒绝）
     *
     * @param id         售后申请ID
     * @param handleNote 处理备注（拒绝原因）
     * @return 处理结果
     */
    @ApiOperation(value = "商家拒绝售后申请", notes = "商家拒绝售后申请并填写拒绝原因")
    @PostMapping(value = "/reject")
    public Result<?> rejectAfterSale(@RequestBody AfterSaleDTO dto) {
        // 获取售后信息
        AfterSale afterSale = afterSaleService.getById(dto.id);
        if (afterSale == null) {
            return Result.error("售后申请不存在");
        }

        // 检查售后状态
        if (afterSale.getStatus() != 1) {
            return Result.error("只能处理待审核状态的售后申请");
        }

        // 拒绝原因必填
        if (StringUtils.isBlank(dto.handleNote)) {
            return Result.error("拒绝售后申请时，必须填写拒绝原因");
        }

        // 更新售后状态为审核拒绝
        afterSale.setStatus(3); // 审核拒绝状态
        afterSale.setHandleNote(dto.handleNote);
        afterSale.setAuditTime(new Date());
        afterSale.setUpdateTime(new Date());
        afterSale.setUpdateBy(getLoginUser().getUsername());

        // 保存更新
        boolean success = afterSaleService.updateById(afterSale);
        if (!success) {
            return Result.error("处理售后申请失败");
        }

        // 同时更新审核记录
        AuditRecord auditRecord = auditRecordService.getAuditRecordByAftersaleId(dto.id);
        if (auditRecord != null) {
            return auditRecordService.performAudit(auditRecord.getId(), 2, dto.handleNote);
        } else {
            log.warn("售后申请[{}]没有关联的审核记录", dto.id);
            return Result.OK("售后申请已拒绝，但未找到关联的审核记录");
        }
    }

    /**
     * 获取当前登录用户
     *
     * @return 登录用户
     */
    private org.jeecg.common.system.vo.LoginUser getLoginUser() {
        return (org.jeecg.common.system.vo.LoginUser) org.apache.shiro.SecurityUtils.getSubject().getPrincipal();
    }

    /**
     * 商家处理售后申请（批量处理）
     *
     * @param idList     售后申请ID列表
     * @param action     处理动作（1:同意,2:拒绝）
     * @param handleNote 处理备注
     * @return 处理结果
     */
    @ApiOperation(value = "批量处理售后申请", notes = "批量同意或拒绝售后申请")
    @PostMapping(value = "/batchProcess")
    public Result<?> batchProcessAfterSale(
            @RequestParam(name = "idList", required = true) String idList,
            @RequestParam(name = "action", required = true) Integer action,
            @RequestParam(name = "handleNote", required = false) String handleNote) {

        // 验证处理动作参数
        if (action != 1 && action != 2) {
            return Result.error("处理动作参数无效，请使用1(同意)或2(拒绝)");
        }

        // 拒绝时备注必填
        if (action == 2 && StringUtils.isBlank(handleNote)) {
            return Result.error("拒绝售后申请时，必须填写拒绝原因");
        }

        // 分割ID列表
        String[] ids = idList.split(",");
        List<String> successIds = new ArrayList<>();
        List<String> failIds = new ArrayList<>();

        for (String id : ids) {
            try {
                // 获取售后信息
                AfterSale afterSale = afterSaleService.getById(id);
                if (afterSale == null) {
                    failIds.add(id);
                    log.error("售后ID[{}]对应的申请不存在", id);
                    continue;
                }

                // 检查售后状态
                if (afterSale.getStatus() != 1) {
                    failIds.add(id);
                    log.error("售后ID[{}]的状态不是待处理状态，当前状态：{}", id, afterSale.getStatus());
                    continue;
                }

                // 更新售后状态
                afterSale.setHandleNote(handleNote);
                afterSale.setAuditTime(new Date());
                afterSale.setUpdateTime(new Date());
                afterSale.setUpdateBy(getLoginUser().getUsername());

                if (action == 1) {
                    // 同意售后申请
                    afterSale.setStatus(2); // 审核通过状态
                } else {
                    // 拒绝售后申请
                    afterSale.setStatus(3); // 审核拒绝状态
                }

                // 保存更新
                boolean success = afterSaleService.updateById(afterSale);
                if (success) {
                    successIds.add(id);

                    // 更新审核记录
                    AuditRecord auditRecord = auditRecordService.getAuditRecordByAftersaleId(id);
                    if (auditRecord != null) {
                        auditRecordService.performAudit(auditRecord.getId(), action, handleNote);
                    }
                } else {
                    failIds.add(id);
                    log.error("售后ID[{}]状态更新失败", id);
                }

            } catch (Exception e) {
                failIds.add(id);
                log.error("处理售后ID[{}]时发生异常：{}", id, e.getMessage());
            }
        }

        // 构建处理结果
        Map<String, Object> result = new HashMap<>();
        result.put("successCount", successIds.size());
        result.put("failCount", failIds.size());
        result.put("successIds", successIds);
        result.put("failIds", failIds);

        return Result.OK(result);
    }

    /**
     * 处理售后退款流程
     *
     * @param id                 售后申请ID
     * @param refundNo           退款流水号
     * @param actualRefundAmount 实际退款金额
     * @param refundNote         退款备注
     * @return 处理结果
     */
    @ApiOperation(value = "处理售后退款", notes = "处理已批准售后申请的退款")
    @PostMapping(value = "/processRefund")
    public Result<?> processAfterSaleRefund(
            @RequestParam(name = "id", required = true) String id,
            @RequestParam(name = "refundNo", required = true) String refundNo,
            @RequestParam(name = "actualRefundAmount", required = true) BigDecimal actualRefundAmount,
            @RequestParam(name = "refundNote", required = false) String refundNote) {

        // 获取售后信息
        AfterSale afterSale = afterSaleService.getById(id);
        if (afterSale == null) {
            return Result.error("售后申请不存在");
        }

        // 检查售后状态，只能处理已批准的售后
        if (afterSale.getStatus() != 2) {
            return Result.error("只能处理已批准状态的售后申请，当前状态：" + afterSale.getStatus());
        }

        // 验证退款金额
        if (actualRefundAmount == null || actualRefundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return Result.error("退款金额必须大于0");
        }

        // 更新售后状态为退款中
        afterSale.setStatus(4); // 退款中状态
        afterSale.setRefundNo(refundNo);
        afterSale.setRefundTime(new Date());
        afterSale.setUpdateTime(new Date());
        afterSale.setUpdateBy(getLoginUser().getUsername());

        // 如果提供了退款备注，则附加到处理备注中
        if (StringUtils.isNotBlank(refundNote)) {
            String handleNote = afterSale.getHandleNote();
            if (StringUtils.isNotBlank(handleNote)) {
                afterSale.setHandleNote(handleNote + "\n退款备注：" + refundNote);
            } else {
                afterSale.setHandleNote("退款备注：" + refundNote);
            }
        }

        // 保存更新
        boolean success = afterSaleService.updateById(afterSale);
        if (!success) {
            return Result.error("更新售后退款状态失败");
        }

        // 构建结果对象
        Map<String, Object> result = new HashMap<>();
        result.put("id", id);
        result.put("status", afterSale.getStatus());
        result.put("statusText", getStatusText(afterSale.getStatus()));
        result.put("refundNo", refundNo);
        result.put("refundTime", afterSale.getRefundTime());

        return Result.OK(result);
    }

    /**
     * 完成售后处理
     *
     * @param id         售后申请ID
     * @param finishNote 完成备注
     * @return 处理结果
     */
    @ApiOperation(value = "完成售后处理", notes = "将售后申请标记为完成状态")
    @PostMapping(value = "/complete")
    public Result<?> completeAfterSale(
            @RequestParam(name = "id", required = true) String id,
            @RequestParam(name = "finishNote", required = false) String finishNote) {

        // 获取售后信息
        AfterSale afterSale = afterSaleService.getById(id);
        if (afterSale == null) {
            return Result.error("售后申请不存在");
        }

        // 检查售后状态，只能完成已批准或退款中的售后
        if (afterSale.getStatus() != 2 && afterSale.getStatus() != 4) {
            return Result.error("只能完成已批准或退款中状态的售后申请，当前状态：" + afterSale.getStatus());
        }

        // 更新售后状态为已完成
        afterSale.setStatus(5); // 已完成状态
        afterSale.setFinishTime(new Date());
        afterSale.setUpdateTime(new Date());
        afterSale.setUpdateBy(getLoginUser().getUsername());

        // 如果提供了完成备注，则附加到处理备注中
        if (StringUtils.isNotBlank(finishNote)) {
            String handleNote = afterSale.getHandleNote();
            if (StringUtils.isNotBlank(handleNote)) {
                afterSale.setHandleNote(handleNote + "\n完成备注：" + finishNote);
            } else {
                afterSale.setHandleNote("完成备注：" + finishNote);
            }
        }

        // 保存更新
        boolean success = afterSaleService.updateById(afterSale);
        if (!success) {
            return Result.error("更新售后完成状态失败");
        }

        return Result.OK("售后申请已成功完成");
    }

    /**
     * 获取售后状态文本描述
     *
     * @param status 状态码
     * @return 状态文本
     */
    private String getStatusText(Integer status) {
        if (status == null) {
            return "未知状态";
        }

        switch (status) {
            case 1:
                return "待处理";
            case 2:
                return "已同意";
            case 3:
                return "已拒绝";
            case 4:
                return "退款中";
            case 5:
                return "已完成";
            case 6:
                return "已取消";
            default:
                return "未知状态(" + status + ")";
        }
    }

    /**
     * 审核通过售后申请
     *
     * @return 处理结果
     */
    @AutoLog(value = "售后表-审核通过")
    @ApiOperation(value = "售后表-审核通过", notes = "审核通过售后申请")
    @PostMapping(value = "/approveAudit")
    public Result<?> approveAudit(@RequestBody AfterSaleDTO dto) {
        
        // 获取售后申请
        AfterSale afterSale = afterSaleService.getById(dto.getId());
        if (afterSale == null) {
            return Result.error("未找到对应的售后申请");
        }
        
        // 检查售后申请状态是否为待审核
        if (afterSale.getStatus() != 1) {
            return Result.error("该售后申请不处于待审核状态，无法进行审核");
        }
        
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            return Result.error("未获取到登录用户信息");
        }
        
        // 更新售后申请信息
        afterSale.setStatus(2); // 设置为审核通过状态
        afterSale.setHandleNote(dto.getHandleNote());
        afterSale.setAuditTime(new Date());
        afterSale.setUpdateBy(loginUser.getUsername());
        afterSale.setUpdateTime(new Date());
        
        // 保存售后申请
        afterSaleService.updateById(afterSale);
        
        // 查找并更新审核记录
        AuditRecord auditRecord = auditRecordService.getAuditRecordByAftersaleId(dto.getId());
        if (auditRecord != null) {
            auditRecord.setStatus(1); // 审核通过
            auditRecord.setComments(dto.getHandleNote());
            auditRecord.setAuditorId(loginUser.getId());
            auditRecord.setAuditTime(new Date());
            auditRecordService.updateById(auditRecord);
        }
        
        return Result.OK("售后审核通过成功");
    }

    /**
     * 审核驳回售后申请
     *
     * @return 处理结果
     */
    @AutoLog(value = "售后表-审核驳回")
    @ApiOperation(value = "售后表-审核驳回", notes = "审核驳回售后申请")
    @PostMapping(value = "/rejectAudit")
    public Result<?> rejectAudit(@RequestBody AfterSaleDTO dto) {
        
        // 获取售后申请
        AfterSale afterSale = afterSaleService.getById(dto.getId());
        if (afterSale == null) {
            return Result.error("未找到对应的售后申请");
        }
        
        // 检查售后申请状态是否为待审核
        if (afterSale.getStatus() != 1) {
            return Result.error("该售后申请不处于待审核状态，无法进行审核");
        }
        
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            return Result.error("未获取到登录用户信息");
        }
        
        // 拒绝原因必填
        if (StringUtils.isBlank(dto.getHandleNote())) {
            return Result.error("拒绝售后申请时，必须填写拒绝原因");
        }
        
        // 更新售后申请信息
        afterSale.setStatus(3); // 设置为审核拒绝状态
        afterSale.setHandleNote(dto.getHandleNote());
        afterSale.setAuditTime(new Date());
        afterSale.setUpdateBy(loginUser.getUsername());
        afterSale.setUpdateTime(new Date());
        
        // 保存售后申请
        afterSaleService.updateById(afterSale);
        
        // 查找并更新审核记录
        AuditRecord auditRecord = auditRecordService.getAuditRecordByAftersaleId(dto.getId());
        if (auditRecord != null) {
            auditRecord.setStatus(2); // 审核驳回
            auditRecord.setComments(dto.getHandleNote());
            auditRecord.setAuditorId(loginUser.getId());
            auditRecord.setAuditTime(new Date());
            auditRecordService.updateById(auditRecord);
        }
        
        return Result.OK("售后审核驳回成功");
    }

    /**
     * 根据订单编号获取售后申请详情
     *
     * @param orderNo 订单编号
     * @return 售后申请详情
     */
    @AutoLog(value = "售后表-根据订单号查询")
    @ApiOperation(value = "售后表-根据订单号查询", notes = "根据订单编号获取售后申请详情")
    @GetMapping(value = "/getByOrderNo")
    public Result<AfterSale> getByOrderNo(@RequestParam(name = "orderNo", required = true) String orderNo) {
        // 根据订单编号查找售后申请
        AfterSale afterSale = afterSaleService.getAfterSaleByOrderNo(orderNo);
        if (afterSale == null) {
            return Result.error("未找到对应订单的售后申请");
        }

        return Result.OK(afterSale);
    }

}
