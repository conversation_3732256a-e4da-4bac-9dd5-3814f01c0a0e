package org.jeecg.modules.inz_orders_logistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.jeecg.modules.inz_orders_logistics.config.Kuaidi100Config;
import org.jeecg.modules.inz_orders_logistics.entity.InzOrdersLogistics;
import org.jeecg.modules.inz_orders_logistics.entity.ShippingRecord;
import org.jeecg.modules.inz_orders_logistics.mapper.ShippingRecordMapper;
import org.jeecg.modules.inz_orders_logistics.model.ExpressCompanyEnum;
import org.jeecg.modules.inz_orders_logistics.model.ExpressTrackDTO;
import org.jeecg.modules.inz_orders_logistics.model.Kuaidi100Request;
import org.jeecg.modules.inz_orders_logistics.service.IExpressTrackingService;
import org.jeecg.modules.inz_orders_logistics.service.IInzOrdersLogisticsService;
import org.jeecg.modules.inz_orders_logistics.util.HttpClientUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 快递物流查询服务实现类
 */
@Service
public class ExpressTrackingServiceImpl implements IExpressTrackingService {
    
    private static final Logger log = LoggerFactory.getLogger(ExpressTrackingServiceImpl.class);
    
    // 用于异步批量查询的线程池
    private static final ExecutorService QUERY_EXECUTOR = Executors.newFixedThreadPool(5);
    
    @Autowired
    private Kuaidi100Config kuaidi100Config;
    
    @Autowired
    private IInzOrdersLogisticsService inzOrdersLogisticsService;
    
    @Autowired
    private ShippingRecordMapper shippingRecordMapper;
    
    @Override
    public ExpressTrackDTO queryTrack(String expressNo, String expressCompany) {
        if (StringUtils.isBlank(expressNo) || StringUtils.isBlank(expressCompany)) {
            log.error("快递单号或快递公司编码不能为空");
            return null;
        }
        
        try {
            // 转换为快递100的编码格式
            String kuaidi100Code = expressCompany;
            if (!expressCompany.startsWith("shunfeng") && !expressCompany.startsWith("yuantong") && 
                !expressCompany.startsWith("zhongtong") && !expressCompany.startsWith("yunda")) {
                // 如果不是标准快递100编码，尝试转换
                kuaidi100Code = ExpressCompanyEnum.getCodeByName(expressCompany);
            }
            
            // 构建请求参数
            Kuaidi100Request.Param param = new Kuaidi100Request.Param();
            param.setCom(kuaidi100Code);
            param.setNum(expressNo);
            
            String paramJson = JSON.toJSONString(param);
            
            // 生成签名
            String signSource = paramJson + kuaidi100Config.getKey() + kuaidi100Config.getCustomer();
            String sign = DigestUtils.md5Hex(signSource).toUpperCase();
            
            // 构建请求对象
            Kuaidi100Request request = new Kuaidi100Request();
            request.setCustomer(kuaidi100Config.getCustomer());
            request.setSign(sign);
            request.setParam(paramJson);
            
            // 发送请求
            String responseStr = HttpClientUtil.doPostJson(kuaidi100Config.getApiUrl(), JSON.toJSONString(request));
            log.info("快递100接口返回数据: {}", responseStr);
            
            // 解析响应
            return JSON.parseObject(responseStr, ExpressTrackDTO.class);
        } catch (Exception e) {
            log.error("查询物流信息异常: {}", e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public ExpressTrackDTO queryTrackByLogisticsId(String logisticsId) {
        // 查询物流信息
        InzOrdersLogistics logistics = inzOrdersLogisticsService.getById(logisticsId);
        if (logistics == null) {
            log.error("找不到物流记录, ID: {}", logisticsId);
            return null;
        }
        
        // 获取快递单号和快递公司编码
        String trackingNo = logistics.getTrackingNo();
        String expressCode = logistics.getExpressCode();
        
        // 如果系统中的快递公司编码是名称，则尝试从名称中获取编码
        if (logistics.getExpressCompany() != null && !logistics.getExpressCompany().isEmpty() && 
            (StringUtils.isBlank(expressCode) || expressCode.equals("unknown"))) {
            expressCode = ExpressCompanyEnum.getCodeByName(logistics.getExpressCompany());
        }
        
        // 调用查询接口
        return queryTrack(trackingNo, expressCode);
    }
    
    @Override
    public boolean subscribeExpressTrack(String expressNo, String expressCompany, String phone, String orderNo) {
        if (StringUtils.isBlank(expressNo) || StringUtils.isBlank(expressCompany)) {
            log.error("快递单号或快递公司编码不能为空");
            return false;
        }
        
        try {
            // 转换为快递100的编码格式
            String kuaidi100Code = ExpressCompanyEnum.getCodeByName(expressCompany);
            
            // 构建参数对象
            Kuaidi100Request.Param param = new Kuaidi100Request.Param();
            param.setCom(kuaidi100Code);
            param.setNum(expressNo);
            param.setPhone(phone);
            param.setCallbackurl(kuaidi100Config.getCallbackUrl());
            // 将订单号作为附加参数，回调时会原样返回
            param.setSalt(orderNo);
            // 签收时需要回调
            param.setFinishCallBack(1);
            
            String paramJson = JSON.toJSONString(param);
            
            // 生成签名
            String signSource = paramJson + kuaidi100Config.getKey() + kuaidi100Config.getCustomer();
            String sign = DigestUtils.md5Hex(signSource).toUpperCase();
            
            // 构建请求对象
            Kuaidi100Request request = new Kuaidi100Request();
            request.setCustomer(kuaidi100Config.getCustomer());
            request.setSign(sign);
            request.setParam(paramJson);
            request.setSchema("json");  // 返回json格式
            
            // 发送订阅请求
            String responseStr = HttpClientUtil.doPostJson(kuaidi100Config.getSubscribeUrl(), JSON.toJSONString(request));
            log.info("快递100订阅接口返回数据: {}", responseStr);
            
            // 解析响应
            JSONObject responseJson = JSON.parseObject(responseStr);
            boolean success = responseJson.getBooleanValue("result");
            String message = responseJson.getString("message");
            
            if (success) {
                log.info("物流订阅成功: {}", expressNo);
                return true;
            } else {
                log.error("物流订阅失败: {}, 原因: {}", expressNo, message);
                return false;
            }
        } catch (Exception e) {
            log.error("订阅物流信息异常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public Map<String, ExpressTrackDTO> batchQueryTrack(List<Map<String, String>> expressInfoList) {
        if (expressInfoList == null || expressInfoList.isEmpty()) {
            return new HashMap<>();
        }
        
        Map<String, ExpressTrackDTO> resultMap = new HashMap<>(expressInfoList.size());
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        // 使用CompletableFuture并行查询
        for (Map<String, String> expressInfo : expressInfoList) {
            String expressNo = expressInfo.get("expressNo");
            String expressCompany = expressInfo.get("expressCompany");
            
            if (StringUtils.isBlank(expressNo) || StringUtils.isBlank(expressCompany)) {
                continue;
            }
            
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    ExpressTrackDTO trackInfo = queryTrack(expressNo, expressCompany);
                    if (trackInfo != null) {
                        synchronized (resultMap) {
                            resultMap.put(expressNo, trackInfo);
                        }
                    }
                } catch (Exception e) {
                    log.error("批量查询物流信息异常, 单号: {}, 异常: {}", expressNo, e.getMessage());
                }
            }, QUERY_EXECUTOR);
            
            futures.add(future);
        }
        
        // 等待所有查询完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
        } catch (Exception e) {
            log.error("等待批量查询完成时发生异常: {}", e.getMessage(), e);
        }
        
        return resultMap;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processCallbackData(String callbackData) {
        if (StringUtils.isBlank(callbackData)) {
            log.error("回调数据为空");
            return false;
        }
        
        try {
            // 解析回调数据
            Kuaidi100Request.CallbackData data = JSON.parseObject(callbackData, Kuaidi100Request.CallbackData.class);
            
            if (data == null || StringUtils.isBlank(data.getNu())) {
                log.error("回调数据格式不正确: {}", callbackData);
                return false;
            }
            
            String expressNo = data.getNu();
            String orderNo = data.getSalt();
            String status = data.getStatus();
            
            log.info("收到物流回调, 单号: {}, 订单号: {}, 状态: {}", expressNo, orderNo, status);
            
            // 查询关联的物流记录
            com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<ShippingRecord> queryWrapper = 
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
            queryWrapper.eq("tracking_no", expressNo);
            
            ShippingRecord record = shippingRecordMapper.selectOne(queryWrapper);
            if (record == null) {
                log.error("找不到对应的发货记录, 单号: {}", expressNo);
                return false;
            }
            
            // 更新物流状态
            boolean statusUpdated = false;
            if ("3".equals(status)) {
                // 签收状态
                record.setStatus(2); // 已签收
                statusUpdated = true;
            } else if ("2".equals(status)) {
                // 异常状态
                record.setStatus(3); // 异常
                statusUpdated = true;
            }
            
            // 如果有轨迹信息，更新最新物流信息
            if (data.getData() != null && data.getData().getData() != null && !data.getData().getData().isEmpty()) {
                ExpressTrackDTO.ExpressTrackItemDTO latestItem = data.getData().getData().get(0);
                record.setLatestLogistics(latestItem.getContext());
                record.setUpdateTime(new Date());
                
                shippingRecordMapper.updateById(record);
                return true;
            } else if (statusUpdated) {
                record.setUpdateTime(new Date());
                shippingRecordMapper.updateById(record);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("处理物流回调数据异常: {}", e.getMessage(), e);
            return false;
        }
    }
} 