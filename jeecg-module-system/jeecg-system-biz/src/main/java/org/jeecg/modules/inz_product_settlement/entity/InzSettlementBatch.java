package org.jeecg.modules.inz_product_settlement.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Description: 结算批次
 * @Author: jeecg-boot
 * @Date: 2025-06-26
 * @Version: V1.0
 */
@Data
@TableName("inz_settlement_batch")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_settlement_batch对象", description="结算批次")
public class InzSettlementBatch implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 批次状态枚举
     */
    public static final int STATUS_PROCESSING = 1;  // 进行中
    public static final int STATUS_COMPLETED = 2;   // 已完成
    public static final int STATUS_CANCELLED = 3;   // 已取消

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**批次号*/
    @Excel(name = "批次号", width = 15)
    @ApiModelProperty(value = "批次号")
    private String batchNo;

    /**批次名称*/
    @Excel(name = "批次名称", width = 15)
    @ApiModelProperty(value = "批次名称")
    private String batchName;

    /**结算记录数*/
    @Excel(name = "结算记录数", width = 15)
    @ApiModelProperty(value = "结算记录数")
    private Integer settlementCount;

    /**总结算金额*/
    @Excel(name = "总结算金额", width = 15)
    @ApiModelProperty(value = "总结算金额")
    private BigDecimal totalAmount;

    /**总平台佣金*/
    @Excel(name = "总平台佣金", width = 15)
    @ApiModelProperty(value = "总平台佣金")
    private BigDecimal totalCommission;

    /**总店铺收入*/
    @Excel(name = "总店铺收入", width = 15)
    @ApiModelProperty(value = "总店铺收入")
    private BigDecimal totalStoreIncome;

    /**批次状态（1-进行中，2-已完成，3-已取消）*/
    @Excel(name = "批次状态", width = 15, dicCode = "batch_status")
    @ApiModelProperty(value = "批次状态（1-进行中，2-已完成，3-已取消）")
    private Integer batchStatus;

    /**开始时间*/
    @Excel(name = "开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /**结束时间*/
    @Excel(name = "结束时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    /**备注*/
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 获取批次状态描述
     */
    public String getBatchStatusText() {
        switch (this.batchStatus) {
            case STATUS_PROCESSING:
                return "进行中";
            case STATUS_COMPLETED:
                return "已完成";
            case STATUS_CANCELLED:
                return "已取消";
            default:
                return "未知状态";
        }
    }

    /**
     * 生成批次号
     */
    public static String generateBatchNo() {
        return "BATCH" + System.currentTimeMillis();
    }

    /**
     * 计算平均佣金率
     */
    public BigDecimal getAverageCommissionRate() {
        if (totalAmount == null || totalAmount.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        if (totalCommission == null) {
            return BigDecimal.ZERO;
        }
        return totalCommission.divide(totalAmount, 4, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 更新批次统计信息
     */
    public void updateStatistics(Integer count, BigDecimal amount, BigDecimal commission, BigDecimal storeIncome) {
        this.settlementCount = count;
        this.totalAmount = amount;
        this.totalCommission = commission;
        this.totalStoreIncome = storeIncome;
    }
}
