<template>
    <view>
        <!--标题和返回-->
		<cu-custom :bgColor="NavBarColor" isBack :backRouterName="backRouteName">
			<block slot="backText">返回</block>
			<block slot="content">订单表</block>
		</cu-custom>
		 <!--表单区域-->
		<view>
			<form>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">订单编号：</text></view>
                  <input  placeholder="请输入订单编号" v-model="model.orderNo"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">用户ID：</text></view>
                  <input  placeholder="请输入用户ID" v-model="model.userId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">店铺ID：</text></view>
                  <input  placeholder="请输入店铺ID" v-model="model.storeId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">订单类型(1:金钱订单,2:积分订单,3:混合支付)：</text></view>
                  <input type="number" placeholder="请输入订单类型(1:金钱订单,2:积分订单,3:混合支付)" v-model="model.orderType"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">订单总金额：</text></view>
                  <input type="number" placeholder="请输入订单总金额" v-model="model.totalPrice"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">实付金额：</text></view>
                  <input type="number" placeholder="请输入实付金额" v-model="model.payPrice"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">使用的积分数量：</text></view>
                  <input type="number" placeholder="请输入使用的积分数量" v-model="model.pointAmount"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">优惠金额：</text></view>
                  <input type="number" placeholder="请输入优惠金额" v-model="model.discountAmount"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">运费：</text></view>
                  <input type="number" placeholder="请输入运费" v-model="model.freightAmount"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">订单状态(1:未支付,2:已支付待发货,3:已发货待收货,4:已完成,5:已取消,6:已退款)：</text></view>
                  <input type="number" placeholder="请输入订单状态(1:未支付,2:已支付待发货,3:已发货待收货,4:已完成,5:已取消,6:已退款)" v-model="model.orderStatus"/>
                </view>
              </view>
              <my-date label="支付时间：" v-model="model.payTime" placeholder="请输入支付时间"></my-date>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">支付方式(1-支付宝,2-微信)：</text></view>
                  <input type="number" placeholder="请输入支付方式(1-支付宝,2-微信)" v-model="model.payType"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">支付流水号：</text></view>
                  <input  placeholder="请输入支付流水号" v-model="model.transactionId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">收货人姓名：</text></view>
                  <input  placeholder="请输入收货人姓名" v-model="model.receiverName"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">收货人电话：</text></view>
                  <input  placeholder="请输入收货人电话" v-model="model.receiverPhone"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">收货地址：</text></view>
                  <input  placeholder="请输入收货地址" v-model="model.receiverAddress"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">订单备注：</text></view>
                  <input  placeholder="请输入订单备注" v-model="model.remark"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">退款状态(1-未操作退款,2-用户已提交，未退款,3-退款中,4-退款成功,5-退款失败,6-退款驳回)：</text></view>
                  <input type="number" placeholder="请输入退款状态(1-未操作退款,2-用户已提交，未退款,3-退款中,4-退款成功,5-退款失败,6-退款驳回)" v-model="model.refundStatus"/>
                </view>
              </view>
              <my-date label="退款时间：" v-model="model.refundTime" placeholder="请输入退款时间"></my-date>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">支付prepay_id：</text></view>
                  <input  placeholder="请输入支付prepay_id" v-model="model.prepayId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">物流单号：</text></view>
                  <input  placeholder="请输入物流单号" v-model="model.trackingNo"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">物流公司：</text></view>
                  <input  placeholder="请输入物流公司" v-model="model.expressCompany"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">物流公司编码：</text></view>
                  <input  placeholder="请输入物流公司编码" v-model="model.expressCode"/>
                </view>
              </view>
              <my-date label="发货时间：" v-model="model.shipTime" placeholder="请输入发货时间"></my-date>
              <my-date label="完成时间：" v-model="model.finishTime" placeholder="请输入完成时间"></my-date>
				<view class="padding">
					<button class="cu-btn block bg-blue margin-tb-sm lg" @click="onSubmit">
						<text v-if="loading" class="cuIcon-loading2 cuIconfont-spin"></text>提交
					</button>
				</view>
			</form>
		</view>
    </view>
</template>

<script>
    import myDate from '@/components/my-componets/my-date.vue'

    export default {
        name: "InzOrdersForm",
        components:{ myDate },
        props:{
          formData:{
              type:Object,
              default:()=>{},
              required:false
          }
        },
        data(){
            return {
				CustomBar: this.CustomBar,
				NavBarColor: this.NavBarColor,
				loading:false,
                model: {},
                backRouteName:'index',
                url: {
                  queryById: "/inz_orders/inzOrders/queryById",
                  add: "/inz_orders/inzOrders/add",
                  edit: "/inz_orders/inzOrders/edit",
                },
            }
        },
        created(){
             this.initFormData();
        },
        methods:{
           initFormData(){
               if(this.formData){
                    let dataId = this.formData.dataId;
                    this.$http.get(this.url.queryById,{params:{id:dataId}}).then((res)=>{
                        if(res.data.success){
                            console.log("表单数据",res);
                            this.model = res.data.result;
                        }
                    })
                }
            },
            onSubmit() {
                let myForm = {...this.model};
                this.loading = true;
                let url = myForm.id?this.url.edit:this.url.add;
				this.$http.post(url,myForm).then(res=>{
				   console.log("res",res)
				   this.loading = false
				   this.$Router.push({name:this.backRouteName})
				}).catch(()=>{
					this.loading = false
				});
            }
        }
    }
</script>
