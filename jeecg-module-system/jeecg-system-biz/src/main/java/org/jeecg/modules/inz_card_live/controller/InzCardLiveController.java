package org.jeecg.modules.inz_card_live.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_card_live.entity.InzCardLive;
import org.jeecg.modules.inz_card_live.service.IInzCardLiveService;
import org.jeecg.modules.inz_store.service.IInzStoreService;
import org.jeecg.modules.inz_users_fronts.service.IInzUsersFrontsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.Arrays;

/**
 * @Description: 直播表
 * @Author: jeecg-boot
 * @Date: 2025-06-20
 * @Version: V1.0
 */
@Api(tags = "直播表")
@RestController
@RequestMapping("/inz_card_live/inzCardLive")
@Slf4j
@Validated
public class InzCardLiveController extends JeecgController<InzCardLive, IInzCardLiveService> {
    @Autowired
    private IInzCardLiveService inzCardLiveService;

    @Autowired
    private IInzStoreService inzStoreService;

    @Autowired
    private IInzUsersFrontsService inzUsersFrontsService;

    /**
     * 分页列表查询
     *
     * @param inzCardLive
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "直播表-分页列表查询")
    @ApiOperation(value = "直播表-分页列表查询", notes = "直播表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<InzCardLive>> queryPageList(InzCardLive inzCardLive,
                                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                    @RequestParam(name = "storeName", required = false) String storeName,
                                                    @RequestParam(name = "userName", required = false) String username,
                                                    @RequestParam(name = "liveTitle", required = false) String liveTitle,
                                                    @RequestParam(name = "liveStatus", required = false) String liveStatus,
                                                    HttpServletRequest req) {
        QueryWrapper<InzCardLive> queryWrapper = QueryGenerator.initQueryWrapper(inzCardLive, req.getParameterMap());

        // 处理店铺名称筛选
        if (StringUtils.isNotBlank(storeName)) {
            // 通过店铺名称获取店铺ID
            String storeId = inzStoreService.getStoreIdByName(storeName);
            if (StringUtils.isNotBlank(storeId)) {
                queryWrapper.eq("store_id", storeId);
            } else {
                // 如果找不到店铺，返回空结果
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }
        }

        // 处理用户名筛选
        if (StringUtils.isNotBlank(username)) {
            // 假设直播表中有user_id字段，通过用户名获取用户ID
            String userId = inzUsersFrontsService.getUserIdByUsername(username);
            if (StringUtils.isNotBlank(userId)) {
                queryWrapper.eq("user_id", userId);
            } else {
                // 如果找不到用户，返回空结果
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }
        }

        // 处理直播标题筛选
        if (StringUtils.isNotBlank(liveTitle)) {
            queryWrapper.like("title", liveTitle);
        }

        if (StringUtils.isNotBlank(liveStatus)) {
            queryWrapper.eq("status", liveStatus);
        }

        // ====== 数据权限控制逻辑开始 ======
        org.jeecg.common.system.vo.LoginUser sysUser = (org.jeecg.common.system.vo.LoginUser) org.apache.shiro.SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null) {
            if (!isAdmin(sysUser)) {
                log.info("非管理员用户访问直播列表，进行数据权限过滤");
                String phone = sysUser.getPhone();
                String userId = null;
                if (StringUtils.isNotBlank(phone)) {
                    try {
                        userId = inzUsersFrontsService.getUserIdByPhone(phone);
                        log.info("通过手机号[{}]查询到前端用户ID: {}", phone, userId);
                    } catch (Exception e) {
                        log.warn("通过手机号查询前端用户失败: {}", e.getMessage());
                    }
                } else {
                    log.warn("后台用户[{}]没有关联手机号", sysUser.getUsername());
                }
                if (StringUtils.isNotBlank(userId)) {
                    QueryWrapper<org.jeecg.modules.inz_store.entity.InzStore> storeQueryWrapper = new QueryWrapper<>();
                    storeQueryWrapper.eq("user_id", userId);
                    org.jeecg.modules.inz_store.entity.InzStore store = inzStoreService.getOne(storeQueryWrapper);
                    if (store != null) {
                        log.info("用户[{}]关联店铺[{}]，只能查看该店铺直播", userId, store.getId());
                        queryWrapper.eq("store_id", store.getId());
                    } else {
                        log.warn("用户[{}]没有关联店铺，返回空结果", userId);
                        return Result.OK(new Page<>(pageNo, pageSize, 0));
                    }
                } else {
                    log.warn("未找到后台用户[{}]对应的前端用户，返回空结果", sysUser.getUsername());
                    return Result.OK(new Page<>(pageNo, pageSize, 0));
                }
            } else {
                log.info("管理员用户访问直播列表，不进行数据权限过滤");
            }
        }
        // ====== 数据权限控制逻辑结束 ======

        Page<InzCardLive> page = new Page<InzCardLive>(pageNo, pageSize);
        IPage<InzCardLive> pageList = inzCardLiveService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 判断用户是否为管理员
     *
     * @param user 登录用户
     * @return 是否为管理员
     */
    private boolean isAdmin(org.jeecg.common.system.vo.LoginUser user) {
        if (user == null) {
            return false;
        }
        String roleCode = user.getRoleCode();
        return StringUtils.isNotBlank(roleCode) && (roleCode.contains("admin") || roleCode.contains("ADMIN"));
    }

    /**
     * 通过店铺名称查询直播信息
     *
     * @param storeName 店铺名称
     * @param pageNo    页码
     * @param pageSize  每页记录数
     * @return 直播信息分页结果
     */
    @ApiOperation(value = "通过店铺名称查询直播信息", notes = "根据店铺名称查询对应的直播信息")
    @GetMapping(value = "/listByStoreName")
    public Result<IPage<InzCardLive>> queryCardLivesByStoreName(
            @RequestParam(name = "storeName") String storeName,
            @RequestParam(name = "pageNo", defaultValue = "1") @Min(value = 1, message = "页码不能小于1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") @Min(value = 1, message = "每页记录数不能小于1") @Max(value = 100, message = "每页记录数不能超过100") Integer pageSize) {
        try {
            if (StringUtils.isBlank(storeName)) {
                return Result.error("店铺名称不能为空");
            }

            // 通过店铺名称获取店铺ID
            String storeId = inzStoreService.getStoreIdByName(storeName);
            if (StringUtils.isBlank(storeId)) {
                return Result.error("未找到名为 '" + storeName + "' 的店铺");
            }

            // 创建分页对象
            Page<InzCardLive> page = new Page<>(pageNo, pageSize);

            // 查询直播信息
            IPage<InzCardLive> pageList = inzCardLiveService.queryCardLivesByStoreId(page, storeId);

            return Result.OK(pageList);
        } catch (Exception e) {
            log.error("查询直播信息失败", e);
            return Result.error("查询直播信息失败: " + e.getMessage());
        }
    }

    /**
     * 添加
     *
     * @param inzCardLive
     * @return
     */
    @AutoLog(value = "直播表-添加")
    @ApiOperation(value = "直播表-添加", notes = "直播表-添加")
    @RequiresPermissions("inz_card_live:card_live:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InzCardLive inzCardLive) {
        inzCardLiveService.save(inzCardLive);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param inzCardLive
     * @return
     */
    @AutoLog(value = "直播表-编辑")
    @ApiOperation(value = "直播表-编辑", notes = "直播表-编辑")
    @RequiresPermissions("inz_card_live:card_live:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InzCardLive inzCardLive) {
        inzCardLiveService.updateById(inzCardLive);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "直播表-通过id删除")
    @ApiOperation(value = "直播表-通过id删除", notes = "直播表-通过id删除")
    @RequiresPermissions("inz_card_live:card_live:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        inzCardLiveService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "直播表-批量删除")
    @ApiOperation(value = "直播表-批量删除", notes = "直播表-批量删除")
    @RequiresPermissions("inz_card_live:card_live:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzCardLiveService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "直播表-通过id查询")
    @ApiOperation(value = "直播表-通过id查询", notes = "直播表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<InzCardLive> queryById(@RequestParam(name = "id", required = true) String id) {
        InzCardLive inzCardLive = inzCardLiveService.getById(id);
        if (inzCardLive == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzCardLive);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param inzCardLive
     */
    @RequiresPermissions("inz_card_live:card_live:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzCardLive inzCardLive) {
        return super.exportXls(request, inzCardLive, InzCardLive.class, "直播表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("inz_card_live:card_live:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzCardLive.class);
    }

}
