package org.jeecg.modules.inz_card_live.service.impl;

import org.jeecg.modules.inz_card_live.entity.InzCardLive;
import org.jeecg.modules.inz_card_live.mapper.InzCardLiveMapper;
import org.jeecg.modules.inz_card_live.service.IInzCardLiveService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 直播表
 * @Author: jeecg-boot
 * @Date:   2025-06-20
 * @Version: V1.0
 */
@Service
public class InzCardLiveServiceImpl extends ServiceImpl<InzCardLiveMapper, InzCardLive> implements IInzCardLiveService {
    
    @Override
    public IPage<InzCardLive> queryCardLivesByStoreId(Page<InzCardLive> page, String storeId) {
        if (StringUtils.isBlank(storeId)) {
            return new Page<>(page.getCurrent(), page.getSize());
        }
        
        // 构建查询条件
        QueryWrapper<InzCardLive> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("store_id", storeId);
        
        // 按直播开始时间倒序排序，最新的直播排在前面
        queryWrapper.orderByDesc("start_time");
        
        // 执行分页查询
        return this.page(page, queryWrapper);
    }
}
