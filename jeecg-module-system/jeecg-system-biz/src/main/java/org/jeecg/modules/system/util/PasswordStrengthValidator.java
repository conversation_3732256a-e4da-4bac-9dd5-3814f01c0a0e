package org.jeecg.modules.system.util;

import java.util.regex.Pattern;

/**
 * @Description: 密码强度验证工具类
 * @Author: jeecg-boot
 * @Date: 2025-01-24
 * @Version: V1.0
 */
public class PasswordStrengthValidator {

    // 密码最小长度
    private static final int MIN_LENGTH = 8;
    
    // 密码最大长度
    private static final int MAX_LENGTH = 20;
    
    // 至少包含一个数字
    private static final Pattern DIGIT_PATTERN = Pattern.compile(".*\\d.*");
    
    // 至少包含一个小写字母
    private static final Pattern LOWERCASE_PATTERN = Pattern.compile(".*[a-z].*");
    
    // 至少包含一个大写字母
    private static final Pattern UPPERCASE_PATTERN = Pattern.compile(".*[A-Z].*");
    
    // 至少包含一个特殊字符
    private static final Pattern SPECIAL_CHAR_PATTERN = Pattern.compile(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*");
    
    // 弱密码黑名单
    private static final String[] WEAK_PASSWORDS = {
        "123456", "password", "123456789", "12345678", "12345", "1234567", "1234567890",
        "qwerty", "abc123", "111111", "123123", "admin", "letmein", "welcome", "monkey",
        "dragon", "pass", "master", "hello", "freedom", "whatever", "qazwsx", "trustno1"
    };
    
    /**
     * 验证密码强度
     * @param password 密码
     * @return 验证结果消息，null表示验证通过
     */
    public static String validatePassword(String password) {
        if (password == null || password.trim().isEmpty()) {
            return "密码不能为空";
        }
        
        // 长度检查
        if (password.length() < MIN_LENGTH) {
            return "密码长度不能少于" + MIN_LENGTH + "位";
        }
        
        if (password.length() > MAX_LENGTH) {
            return "密码长度不能超过" + MAX_LENGTH + "位";
        }
        
        // 检查是否为弱密码
        String lowerPassword = password.toLowerCase();
        for (String weakPassword : WEAK_PASSWORDS) {
            if (lowerPassword.equals(weakPassword) || lowerPassword.contains(weakPassword)) {
                return "密码过于简单，请使用更复杂的密码";
            }
        }
        
        // 复杂度检查
        int complexityScore = 0;
        StringBuilder requirements = new StringBuilder();
        
        if (!DIGIT_PATTERN.matcher(password).matches()) {
            requirements.append("数字、");
        } else {
            complexityScore++;
        }
        
        if (!LOWERCASE_PATTERN.matcher(password).matches()) {
            requirements.append("小写字母、");
        } else {
            complexityScore++;
        }
        
        if (!UPPERCASE_PATTERN.matcher(password).matches()) {
            requirements.append("大写字母、");
        } else {
            complexityScore++;
        }
        
        if (!SPECIAL_CHAR_PATTERN.matcher(password).matches()) {
            requirements.append("特殊字符、");
        } else {
            complexityScore++;
        }
        
        // 至少需要满足3种复杂度要求
        if (complexityScore < 3) {
            String missing = requirements.toString();
            if (missing.endsWith("、")) {
                missing = missing.substring(0, missing.length() - 1);
            }
            return "密码必须包含以下至少3种类型：数字、大小写字母、特殊字符。当前缺少：" + missing;
        }
        
        // 检查连续字符
        if (hasConsecutiveChars(password)) {
            return "密码不能包含连续的字符（如123、abc）";
        }
        
        // 检查重复字符
        if (hasRepeatingChars(password)) {
            return "密码不能包含过多重复字符";
        }
        
        return null; // 验证通过
    }
    
    /**
     * 检查是否包含连续字符
     */
    private static boolean hasConsecutiveChars(String password) {
        for (int i = 0; i < password.length() - 2; i++) {
            char c1 = password.charAt(i);
            char c2 = password.charAt(i + 1);
            char c3 = password.charAt(i + 2);
            
            // 检查连续递增或递减
            if ((c1 + 1 == c2 && c2 + 1 == c3) || (c1 - 1 == c2 && c2 - 1 == c3)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查是否包含过多重复字符
     */
    private static boolean hasRepeatingChars(String password) {
        for (int i = 0; i < password.length() - 2; i++) {
            char c = password.charAt(i);
            if (password.charAt(i + 1) == c && password.charAt(i + 2) == c) {
                return true; // 连续3个相同字符
            }
        }
        return false;
    }
    
    /**
     * 生成强密码
     * @param length 密码长度
     * @return 强密码
     */
    public static String generateStrongPassword(int length) {
        if (length < MIN_LENGTH) {
            length = MIN_LENGTH;
        }
        
        String digits = "0123456789";
        String lowercase = "abcdefghijklmnopqrstuvwxyz";
        String uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";
        
        StringBuilder password = new StringBuilder();
        
        // 确保至少包含每种类型的字符
        password.append(digits.charAt((int) (Math.random() * digits.length())));
        password.append(lowercase.charAt((int) (Math.random() * lowercase.length())));
        password.append(uppercase.charAt((int) (Math.random() * uppercase.length())));
        password.append(specialChars.charAt((int) (Math.random() * specialChars.length())));
        
        // 填充剩余长度
        String allChars = digits + lowercase + uppercase + specialChars;
        for (int i = 4; i < length; i++) {
            password.append(allChars.charAt((int) (Math.random() * allChars.length())));
        }
        
        // 打乱字符顺序
        return shuffleString(password.toString());
    }
    
    /**
     * 打乱字符串
     */
    private static String shuffleString(String str) {
        char[] chars = str.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            int randomIndex = (int) (Math.random() * chars.length);
            char temp = chars[i];
            chars[i] = chars[randomIndex];
            chars[randomIndex] = temp;
        }
        return new String(chars);
    }
    
    /**
     * 获取密码强度等级
     * @param password 密码
     * @return 强度等级：1-弱，2-中，3-强
     */
    public static int getPasswordStrength(String password) {
        if (validatePassword(password) != null) {
            return 1; // 弱
        }
        
        int score = 0;
        
        // 长度加分
        if (password.length() >= 12) score += 2;
        else if (password.length() >= 10) score += 1;
        
        // 复杂度加分
        if (DIGIT_PATTERN.matcher(password).matches()) score++;
        if (LOWERCASE_PATTERN.matcher(password).matches()) score++;
        if (UPPERCASE_PATTERN.matcher(password).matches()) score++;
        if (SPECIAL_CHAR_PATTERN.matcher(password).matches()) score++;
        
        if (score >= 6) return 3; // 强
        else if (score >= 4) return 2; // 中
        else return 1; // 弱
    }
}
