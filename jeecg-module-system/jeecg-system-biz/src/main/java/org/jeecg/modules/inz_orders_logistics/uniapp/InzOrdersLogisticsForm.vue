<template>
    <view>
        <!--标题和返回-->
		<cu-custom :bgColor="NavBarColor" isBack :backRouterName="backRouteName">
			<block slot="backText">返回</block>
			<block slot="content">订单物流信息表</block>
		</cu-custom>
		 <!--表单区域-->
		<view>
			<form>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">订单ID：</text></view>
                  <input  placeholder="请输入订单ID" v-model="model.orderId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">订单编号：</text></view>
                  <input  placeholder="请输入订单编号" v-model="model.orderNo"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">业务类型(1:普通订单,2:积分订单)：</text></view>
                  <input type="number" placeholder="请输入业务类型(1:普通订单,2:积分订单)" v-model="model.bizType"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">物流单号：</text></view>
                  <input  placeholder="请输入物流单号" v-model="model.trackingNo"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">物流公司名称：</text></view>
                  <input  placeholder="请输入物流公司名称" v-model="model.expressCompany"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">物流公司编码：</text></view>
                  <input  placeholder="请输入物流公司编码" v-model="model.expressCode"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">物流状态(1:已发货,2:运输中,3:已签收,4:异常)：</text></view>
                  <input type="number" placeholder="请输入物流状态(1:已发货,2:运输中,3:已签收,4:异常)" v-model="model.logisticsStatus"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">最新物流信息：</text></view>
                  <input  placeholder="请输入最新物流信息" v-model="model.latestInfo"/>
                </view>
              </view>
              <my-date label="发货时间：" v-model="model.shipTime" placeholder="请输入发货时间"></my-date>
              <my-date label="完成时间：" v-model="model.finishTime" placeholder="请输入完成时间"></my-date>
				<view class="padding">
					<button class="cu-btn block bg-blue margin-tb-sm lg" @click="onSubmit">
						<text v-if="loading" class="cuIcon-loading2 cuIconfont-spin"></text>提交
					</button>
				</view>
			</form>
		</view>
    </view>
</template>

<script>
    import myDate from '@/components/my-componets/my-date.vue'

    export default {
        name: "InzOrdersLogisticsForm",
        components:{ myDate },
        props:{
          formData:{
              type:Object,
              default:()=>{},
              required:false
          }
        },
        data(){
            return {
				CustomBar: this.CustomBar,
				NavBarColor: this.NavBarColor,
				loading:false,
                model: {},
                backRouteName:'index',
                url: {
                  queryById: "/inz_orders_logistics/inzOrdersLogistics/queryById",
                  add: "/inz_orders_logistics/inzOrdersLogistics/add",
                  edit: "/inz_orders_logistics/inzOrdersLogistics/edit",
                },
            }
        },
        created(){
             this.initFormData();
        },
        methods:{
           initFormData(){
               if(this.formData){
                    let dataId = this.formData.dataId;
                    this.$http.get(this.url.queryById,{params:{id:dataId}}).then((res)=>{
                        if(res.data.success){
                            console.log("表单数据",res);
                            this.model = res.data.result;
                        }
                    })
                }
            },
            onSubmit() {
                let myForm = {...this.model};
                this.loading = true;
                let url = myForm.id?this.url.edit:this.url.add;
				this.$http.post(url,myForm).then(res=>{
				   console.log("res",res)
				   this.loading = false
				   this.$Router.push({name:this.backRouteName})
				}).catch(()=>{
					this.loading = false
				});
            }
        }
    }
</script>
