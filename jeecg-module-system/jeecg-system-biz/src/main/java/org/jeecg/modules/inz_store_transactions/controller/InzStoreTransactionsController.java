package org.jeecg.modules.inz_store_transactions.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Calendar;
import java.util.Date;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.inz_store_transactions.entity.InzStoreTransactions;
import org.jeecg.modules.inz_store_transactions.service.IInzStoreTransactionsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.format.annotation.DateTimeFormat;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

 /**
 * @Description: 店铺资金流水管理
 * @Author: jeecg-boot
 * @Date:   2025-06-26
 * @Version: V1.0
 */
@Api(tags="店铺资金流水管理")
@RestController
@RequestMapping("/inz_store_transactions/inzStoreTransactions")
@Slf4j
public class InzStoreTransactionsController extends JeecgController<InzStoreTransactions, IInzStoreTransactionsService> {
	@Autowired
	private IInzStoreTransactionsService inzStoreTransactionsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzStoreTransactions
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "店铺资金流水-分页列表查询")
	@ApiOperation(value="店铺资金流水-分页列表查询", notes="店铺资金流水-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<InzStoreTransactions>> queryPageList(InzStoreTransactions inzStoreTransactions,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<InzStoreTransactions> queryWrapper = QueryGenerator.initQueryWrapper(inzStoreTransactions, req.getParameterMap());
		Page<InzStoreTransactions> page = new Page<InzStoreTransactions>(pageNo, pageSize);
		IPage<InzStoreTransactions> pageList = inzStoreTransactionsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 * 根据店铺ID查询资金流水
	 */
	@AutoLog(value = "根据店铺ID查询资金流水")
	@ApiOperation(value = "根据店铺ID查询资金流水", notes = "根据店铺ID查询资金流水")
	@GetMapping("/store/{storeId}")
	public Result<IPage<InzStoreTransactions>> getTransactionsByStore(
			@ApiParam(value = "店铺ID", required = true) @PathVariable String storeId,
			@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
			@RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
			@RequestParam(name = "transactionType", required = false) String transactionType,
			@RequestParam(name = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
			@RequestParam(name = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {

		try {
			QueryWrapper<InzStoreTransactions> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("store_id", storeId);

			// 交易类型筛选
			if (StringUtils.isNotBlank(transactionType)) {
				queryWrapper.eq("transaction_type", transactionType);
			}

			// 日期范围筛选
			if (startDate != null) {
				queryWrapper.ge("transaction_time", startDate);
			}
			if (endDate != null) {
				// 结束日期包含当天，所以加一天
				Calendar calendar = Calendar.getInstance();
				calendar.setTime(endDate);
				calendar.add(Calendar.DAY_OF_MONTH, 1);
				queryWrapper.lt("transaction_time", calendar.getTime());
			}

			// 按时间倒序排列
			queryWrapper.orderByDesc("transaction_time");

			Page<InzStoreTransactions> page = new Page<>(pageNo, pageSize);
			IPage<InzStoreTransactions> pageList = inzStoreTransactionsService.page(page, queryWrapper);

			return Result.OK(pageList);
		} catch (Exception e) {
			log.error("查询店铺资金流水失败", e);
			return Result.error("查询失败: " + e.getMessage());
		}
	}

	/**
	 * 获取店铺资金统计
	 */
	@AutoLog(value = "获取店铺资金统计")
	@ApiOperation(value = "获取店铺资金统计", notes = "获取店铺的收入、支出、余额等统计信息")
	@GetMapping("/store/{storeId}/statistics")
	public Result<Map<String, Object>> getStoreStatistics(
			@ApiParam(value = "店铺ID", required = true) @PathVariable String storeId,
			@RequestParam(name = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
			@RequestParam(name = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {

		try {
			QueryWrapper<InzStoreTransactions> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("store_id", storeId);

			// 日期范围筛选
			if (startDate != null) {
				queryWrapper.ge("transaction_time", startDate);
			}
			if (endDate != null) {
				Calendar calendar = Calendar.getInstance();
				calendar.setTime(endDate);
				calendar.add(Calendar.DAY_OF_MONTH, 1);
				queryWrapper.lt("transaction_time", calendar.getTime());
			}

			List<InzStoreTransactions> transactions = inzStoreTransactionsService.list(queryWrapper);

			// 计算统计数据
			BigDecimal totalIncome = BigDecimal.ZERO;
			BigDecimal totalExpense = BigDecimal.ZERO;
			BigDecimal currentBalance = BigDecimal.ZERO;

			for (InzStoreTransactions transaction : transactions) {
				BigDecimal amount = transaction.getAmount() != null ? transaction.getAmount() : BigDecimal.ZERO;

				if ("INCOME".equals(transaction.getTransactionType()) ||
					"REFUND_CANCEL".equals(transaction.getTransactionType())) {
					totalIncome = totalIncome.add(amount);
				} else if ("EXPENSE".equals(transaction.getTransactionType()) ||
						   "REFUND".equals(transaction.getTransactionType()) ||
						   "WITHDRAWAL".equals(transaction.getTransactionType())) {
					totalExpense = totalExpense.add(amount);
				}
			}

			// 获取当前余额（最新的余额记录）
			QueryWrapper<InzStoreTransactions> balanceWrapper = new QueryWrapper<>();
			balanceWrapper.eq("store_id", storeId)
						  .orderByDesc("transaction_time")
						  .last("LIMIT 1");
			InzStoreTransactions latestTransaction = inzStoreTransactionsService.getOne(balanceWrapper);
			if (latestTransaction != null && latestTransaction.getBalanceAfter() != null) {
				currentBalance = latestTransaction.getBalanceAfter();
			}

			Map<String, Object> statistics = new HashMap<>();
			statistics.put("totalIncome", totalIncome);
			statistics.put("totalExpense", totalExpense);
			statistics.put("netIncome", totalIncome.subtract(totalExpense));
			statistics.put("currentBalance", currentBalance);
			statistics.put("transactionCount", transactions.size());

			return Result.OK(statistics);
		} catch (Exception e) {
			log.error("获取店铺资金统计失败", e);
			return Result.error("获取统计失败: " + e.getMessage());
		}
	}
	
	/**
	 * 创建资金流水记录
	 */
	@AutoLog(value = "创建资金流水记录")
	@ApiOperation(value = "创建资金流水记录", notes = "创建新的资金流水记录")
	@RequiresPermissions("inz_store_transactions:inz_store_transaction:add")
	@PostMapping(value = "/create")
	public Result<InzStoreTransactions> createTransaction(@RequestBody InzStoreTransactions transaction) {
		try {
			// 设置创建时间
			transaction.setTransactionTime(new Date());

			// 保存交易记录
			inzStoreTransactionsService.save(transaction);

			log.info("创建资金流水记录成功，交易ID: {}, 店铺ID: {}, 金额: {}",
					transaction.getId(), transaction.getStoreId(), transaction.getAmount());

			return Result.OK("创建成功", transaction);
		} catch (Exception e) {
			log.error("创建资金流水记录失败", e);
			return Result.error("创建失败: " + e.getMessage());
		}
	}

	/**
	 *   添加
	 *
	 * @param inzStoreTransactions
	 * @return
	 */
	@AutoLog(value = "店铺资金流水-添加")
	@ApiOperation(value="店铺资金流水-添加", notes="店铺资金流水-添加")
	@RequiresPermissions("inz_store_transactions:inz_store_transaction:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InzStoreTransactions inzStoreTransactions) {
		inzStoreTransactionsService.save(inzStoreTransactions);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param inzStoreTransactions
	 * @return
	 */
	@AutoLog(value = "店铺资金流水-编辑")
	@ApiOperation(value="店铺资金流水-编辑", notes="店铺资金流水-编辑")
	@RequiresPermissions("inz_store_transactions:inz_store_transaction:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InzStoreTransactions inzStoreTransactions) {
		inzStoreTransactionsService.updateById(inzStoreTransactions);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "店铺资金流水-通过id删除")
	@ApiOperation(value="店铺资金流水-通过id删除", notes="店铺资金流水-通过id删除")
	@RequiresPermissions("inz_store_transactions:inz_store_transaction:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzStoreTransactionsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "店铺资金流水-批量删除")
	@ApiOperation(value="店铺资金流水-批量删除", notes="店铺资金流水-批量删除")
	@RequiresPermissions("inz_store_transactions:inz_store_transaction:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzStoreTransactionsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "店铺资金流水-通过id查询")
	@ApiOperation(value="店铺资金流水-通过id查询", notes="店铺资金流水-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<InzStoreTransactions> queryById(@RequestParam(name="id",required=true) String id) {
		InzStoreTransactions inzStoreTransactions = inzStoreTransactionsService.getById(id);
		if(inzStoreTransactions==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzStoreTransactions);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param inzStoreTransactions
    */
    @RequiresPermissions("inz_store_transactions:inz_store_transaction:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzStoreTransactions inzStoreTransactions) {
        return super.exportXls(request, inzStoreTransactions, InzStoreTransactions.class, "inz_store_transaction");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("inz_store_transactions:inz_store_transaction:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzStoreTransactions.class);
    }

    /**
     * 获取店铺交易数据概览
     *
     * @return 包含总交易数量、总交易金额、合作门店数量、月增长率的数据
     */
    @AutoLog(value = "获取店铺交易数据概览")
    @ApiOperation(value = "获取店铺交易数据概览", notes = "获取店铺交易数据概览信息")
    @GetMapping(value = "/summary")
    public Result<Map<String, Object>> getTransactionSummary() {
        try {
            Map<String, Object> summary = inzStoreTransactionsService.getTransactionSummary();
            return Result.OK(summary);
        } catch (Exception e) {
            log.error("获取店铺交易数据概览失败", e);
            return Result.error("获取数据失败: " + e.getMessage());
        }
    }
}
