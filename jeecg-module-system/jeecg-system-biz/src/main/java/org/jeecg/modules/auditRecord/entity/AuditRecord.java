package org.jeecg.modules.auditRecord.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 审核表
 * @Author: jeecg-boot
 * @Date:   2025-06-21
 * @Version: V1.0
 */
@Data
@TableName("audit_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="audit_record对象", description="审核表")
public class AuditRecord implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**业务ID*/
	@Excel(name = "业务ID", width = 15)
    @ApiModelProperty(value = "业务ID")
    private java.lang.String businessId;
	/**审核类型（1：商品上架，2：售后申请，3：店铺审核）*/
	@Excel(name = "审核类型（1：商品上架，2：售后申请，3：店铺审核）", width = 15, dicCode = "audit_type")
	@Dict(dicCode = "audit_type")
    @ApiModelProperty(value = "审核类型（1：商品上架，2：售后申请，3：店铺审核）")
    private java.lang.Integer auditType;
	/**审核状态（0：待审核，1：审核通过，2：审核驳回）*/
	@Excel(name = "审核状态（0：待审核，1：审核通过，2：审核驳回）", width = 15)
    @ApiModelProperty(value = "审核状态（0：待审核，1：审核通过，2：审核驳回）")
    private java.lang.Integer status;
	/**审核意见*/
	@Excel(name = "审核意见", width = 15)
    @ApiModelProperty(value = "审核意见")
    private java.lang.String comments;
	/**审核人ID*/
	@Excel(name = "审核人ID", width = 15)
    @ApiModelProperty(value = "审核人ID")
    private java.lang.String auditorId;
    /**提交人*/
    @Excel(name = "提交人", width = 15)
    @ApiModelProperty(value = "提交人")
    private java.lang.String submitter;
    /**提交人ID*/
    @Excel(name = "提交人ID", width = 15)
    @ApiModelProperty(value = "提交人ID")
    private java.lang.String submitterId;
    /**商品ID*/
    @Excel(name = "商品ID", width = 15)
    @ApiModelProperty(value = "商品ID")
    private java.lang.String productId;
    /**商品名称*/
    @Excel(name = "商品名称", width = 15)
    @ApiModelProperty(value = "商品名称")
    private java.lang.String productName;
    /**订单ID*/
    @Excel(name = "订单ID", width = 15)
    @ApiModelProperty(value = "订单ID")
    private java.lang.String orderId;
    /**售后原因*/
    @Excel(name = "售后原因", width = 15)
    @ApiModelProperty(value = "售后原因")
    private java.lang.String aftersaleReason;
    /**业务类型*/
    @Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型")
    private java.lang.String businessType;
    /**店铺ID*/
    @Excel(name = "店铺ID", width = 15)
    @ApiModelProperty(value = "店铺ID")
    private java.lang.String storeId;
    /**店铺名称*/
    @Excel(name = "店铺名称", width = 15)
    @ApiModelProperty(value = "店铺名称")
    private java.lang.String storeName;
	/**审核时间*/
	@Excel(name = "审核时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private java.util.Date auditTime;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
}
