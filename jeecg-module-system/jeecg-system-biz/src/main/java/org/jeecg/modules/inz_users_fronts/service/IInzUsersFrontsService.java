package org.jeecg.modules.inz_users_fronts.service;

import org.jeecg.modules.inz_users_fronts.entity.InzUsersFronts;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 用户表
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
public interface IInzUsersFrontsService extends IService<InzUsersFronts> {

    /**
     * 通过用户名称查询用户ID
     * @param username 用户名称
     * @return 用户ID，如果不存在则返回null
     */
    String getUserIdByUsername(String username);
    
    /**
     * 通过用户昵称查询用户ID
     * @param nickname 用户昵称
     * @return 用户ID，如果不存在则返回null
     */
    String getUserIdByNickname(String nickname);
    
    /**
     * 通过手机号查询用户ID
     * @param phone 手机号
     * @return 用户ID，如果不存在则返回null
     */
    String getUserIdByPhone(String phone);
}
