version: '2'
services:
  jeecg-boot-mysql:
    build:
      context: ./db
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_ROOT_HOST: '%'
      TZ: Asia/Shanghai
    restart: always
    container_name: jeecg-boot-mysql
    image: jeecg-boot-mysql
    command:
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
      --max_allowed_packet=128M
      --default-authentication-plugin=caching_sha2_password
    ports:
      - 3306:3306
    networks:
      - jeecg-boot

  jeecg-boot-redis:
    image: registry.cn-hangzhou.aliyuncs.com/jeecgdocker/redis:5.0
    ports:
      - 6379:6379
    restart: always
    hostname: jeecg-boot-redis
    container_name: jeecg-boot-redis
    networks:
      - jeecg-boot

  jeecg-boot-system:
    build:
      context: ./jeecg-module-system/jeecg-system-start
    restart: on-failure
    depends_on:
      - jeecg-boot-mysql
      - jeecg-boot-redis
    container_name: jeecg-boot-system
    image: jeecg-boot-system
    hostname: jeecg-boot-system
    ports:
      - 8080:8080
    networks:
      - jeecg-boot

networks:
  jeecg-boot:
    name: jeecg_boot
