package org.jeecg.modules.api.user_front.service;

import org.jeecg.modules.api.user_front.entity.Coupon;
import org.jeecg.modules.api.user_front.entity.UserCoupon;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 优惠券验证服务接口
 * @Author: jeecg-boot
 * @Date: 2024-01-24
 * @Version: V1.0
 */
public interface ICouponValidationService {
    
    /**
     * 检查优惠券是否在有效期内
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return boolean
     */
    boolean isWithinValidPeriod(Date startTime, Date endTime);

    /**
     * 检查优惠券是否有剩余数量
     *
     * @param remainingQuantity 剩余数量
     * @return boolean
     */
    boolean hasRemainingQuantity(Integer remainingQuantity);

    /**
     * 检查订单金额是否满足优惠券使用条件
     *
     * @param totalPrice 订单金额
     * @param minAmount 最低使用金额
     * @return boolean
     */
    boolean meetsMinimumAmount(BigDecimal totalPrice, BigDecimal minAmount);

    /**
     * 计算优惠后金额
     *
     * @param totalPrice 订单原始金额
     * @param couponAmount 优惠券金额/折扣
     * @param couponType 优惠券类型（1：固定金额，2：折扣）
     * @return 优惠后金额
     */
    BigDecimal calculateDiscountedAmount(BigDecimal totalPrice, BigDecimal couponAmount, Integer couponType);
    
    /**
     * 验证优惠券信息（有效性、数量等）
     *
     * @param coupon 优惠券信息
     * @return 验证结果信息，null表示验证通过
     */
    String validateCoupon(Coupon coupon);
    
    /**
     * 验证用户优惠券信息（是否存在、是否已使用等）
     *
     * @param userCoupon 用户优惠券信息
     * @return 验证结果信息，null表示验证通过
     */
    String validateUserCoupon(UserCoupon userCoupon);
    
    /**
     * 验证用户优惠券使用条件（优惠券有效性、订单金额等）
     *
     * @param coupon 优惠券信息
     * @param userCoupon 用户优惠券信息
     * @param totalPrice 订单金额
     * @return 验证结果信息，null表示验证通过
     */
    String validateCouponUsage(Coupon coupon, UserCoupon userCoupon, BigDecimal totalPrice);
} 