package org.jeecg.modules.api.user_front.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.api.after_sale.service.IAfterSaleService;
import org.jeecg.modules.api.store.service.IStoreService;
import org.jeecg.modules.api.user_front.entity.Order;
import org.jeecg.modules.api.user_front.entity.OrderItem;
import org.jeecg.modules.api.user_front.service.IOrderItemService;
import org.jeecg.modules.api.user_front.service.IOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: 订单控制器
 * @Author: jeecg-boot
 * @Date: 2024-01-24
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "订单接口")
@RestController
@RequestMapping("/api/user/order")
public class OrderController {

    @Autowired
    private IOrderService orderService;

    @Autowired
    private IOrderItemService orderItemService;

    @Autowired
    private IStoreService storeService;

    @Autowired
    @Qualifier("frontAfterSaleService")
    private IAfterSaleService afterSaleService;

    @AutoLog(value = "创建订单")
    @ApiOperation(value = "创建订单", notes = "创建订单")
    @PostMapping("/create")
    public Result<Map<String, Object>> createOrder(@RequestBody Order order, HttpServletRequest request) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            String orderId = orderService.createOrder(
                    userId,
                    order.getStoreId(),
                    order.getOrderItems(),
                    null, // 不使用优惠券
                    order.getReceiverName(),
                    order.getReceiverPhone(),
                    order.getReceiverAddress(),
                    order.getRemark()
            );

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("orderId", orderId);

            return Result.OK("订单创建成功", resultMap);
        } catch (Exception e) {
            log.error("创建订单失败", e);
            return Result.error("创建订单失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "创建订单（使用优惠券）")
    @ApiOperation(value = "创建订单（使用优惠券）", notes = "创建订单并使用优惠券")
    @PostMapping("/create/coupon")
    public Result<Map<String, Object>> createOrderWithCoupon(
            @RequestBody Order order,
            @RequestParam String couponId,
            HttpServletRequest request) {
        try {
            if (StringUtils.isBlank(couponId)) {
                return Result.error("优惠券ID不能为空");
            }

            String userId = CommonUtils.getUserIdByToken();
            String orderId = orderService.createOrder(
                    userId,
                    order.getStoreId(),
                    order.getOrderItems(),
                    couponId,
                    order.getReceiverName(),
                    order.getReceiverPhone(),
                    order.getReceiverAddress(),
                    order.getRemark()
            );

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("orderId", orderId);

            return Result.OK("订单创建成功并使用了优惠券", resultMap);
        } catch (Exception e) {
            log.error("创建订单失败", e);
            return Result.error("创建订单失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "支付订单")
    @ApiOperation(value = "支付订单", notes = "支付订单")
    @PostMapping("/pay/{orderId}")
    public Result<Map<String, Object>> payOrder(@PathVariable String orderId, HttpServletRequest request) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            boolean success = orderService.payOrder(orderId, userId);

            if (success) {
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("orderId", orderId);
                resultMap.put("status", 1);

                return Result.OK("订单支付成功", resultMap);
            } else {
                return Result.error("订单支付失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("支付订单失败", e);
            return Result.error("支付订单失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "取消订单")
    @ApiOperation(value = "取消订单", notes = "取消订单")
    @PostMapping("/cancel/{orderId}")
    public Result<Map<String, Object>> cancelOrder(@PathVariable String orderId, HttpServletRequest request) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            boolean success = orderService.cancelOrder(orderId, userId);

            if (success) {
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("orderId", orderId);
                resultMap.put("status", 4);

                return Result.OK("订单取消成功", resultMap);
            } else {
                return Result.error("订单取消失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("取消订单失败", e);
            return Result.error("取消订单失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "确认收货")
    @ApiOperation(value = "确认收货", notes = "确认收货")
    @PostMapping("/confirm/{orderId}")
    public Result<Map<String, Object>> confirmOrder(@PathVariable String orderId, HttpServletRequest request) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            boolean success = orderService.confirmOrder(orderId, userId);

            if (success) {
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("orderId", orderId);
                resultMap.put("status", 3);

                return Result.OK("确认收货成功", resultMap);
            } else {
                return Result.error("确认收货失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("确认收货失败", e);
            return Result.error("确认收货失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "发货")
    @ApiOperation(value = "发货", notes = "发货")
    @PostMapping("/ship/{orderId}")
    @SuppressWarnings("deprecation")
    public Result<Map<String, Object>> shipOrder(@PathVariable String orderId, HttpServletRequest request) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            String storeId = (String) request.getAttribute("storeId");
            if (StringUtils.isBlank(storeId)) {
                return Result.error("店铺ID不能为空");
            }

            boolean success = orderService.shipOrder(orderId, storeId);

            if (success) {
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("orderId", orderId);
                resultMap.put("status", 2);

                return Result.OK("订单发货成功", resultMap);
            } else {
                return Result.error("订单发货失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("订单发货失败", e);
            return Result.error("订单发货失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "获取订单详情")
    @ApiOperation(value = "获取订单详情", notes = "获取订单详情")
    @GetMapping("/detail/{orderId}")
    public Result<Order> getOrderDetail(@PathVariable String orderId, HttpServletRequest request) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            Order order = orderService.getOrderDetail(orderId, userId);

            if (order != null) {
                List<OrderItem> orderItems = orderItemService.getOrderItems(orderId);
                order.setOrderItems(orderItems);
                return Result.OK("获取订单详情成功", order);
            } else {
                return Result.error("订单不存在或无权限查看");
            }
        } catch (Exception e) {
            log.error("获取订单详情失败", e);
            return Result.error("获取订单详情失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "获取用户订单列表")
    @ApiOperation(value = "获取用户订单列表", notes = "获取用户订单列表")
    @GetMapping("/list")
    public Result<IPage<Order>> getUserOrders(
            @RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String status,  // 支持单个数字或逗号分隔的多个数字
            @RequestParam(required = false) String orderType,  // 支持单个数字或逗号分隔的多个数字
            HttpServletRequest request) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            Page<Order> page = new Page<>(pageNo, pageSize);

            // 处理status参数，支持单个状态值或多个状态值（如 "1,2,3"）
            List<Integer> statusList = new ArrayList<>();
            if (StringUtils.isNotBlank(status)) {
                if (status.contains(",")) {
                    // 多个状态值的情况
                    String[] statusArray = status.split(",");
                    for (String s : statusArray) {
                        if (StringUtils.isNotBlank(s)) {
                            try {
                                statusList.add(Integer.parseInt(s.trim()));
                            } catch (NumberFormatException e) {
                                log.error("状态值格式错误: " + s);
                            }
                        }
                    }
                } else {
                    // 单个状态值的情况
                    try {
                        statusList.add(Integer.parseInt(status.trim()));
                    } catch (NumberFormatException e) {
                        log.error("状态值格式错误: " + status);
                    }
                }
            }

            // 处理orderType参数，支持单个类型或多个类型
            List<Integer> orderTypeList = new ArrayList<>();
            if (StringUtils.isNotBlank(orderType)) {
                if (orderType.contains(",")) {
                    // 多个订单类型的情况
                    String[] typeArray = orderType.split(",");
                    for (String t : typeArray) {
                        if (StringUtils.isNotBlank(t)) {
                            try {
                                orderTypeList.add(Integer.parseInt(t.trim()));
                            } catch (NumberFormatException e) {
                                log.error("订单类型格式错误: " + t);
                            }
                        }
                    }
                } else {
                    // 单个订单类型的情况
                    try {
                        orderTypeList.add(Integer.parseInt(orderType.trim()));
                    } catch (NumberFormatException e) {
                        log.error("订单类型格式错误: " + orderType);
                    }
                }
            }

            // 使用多状态和多类型查询
            IPage<Order> orderPage;
            if (!statusList.isEmpty() && !orderTypeList.isEmpty()) {
                // 状态和订单类型都有指定
                orderPage = orderService.getUserOrdersWithMultiParams(page, userId, statusList, orderTypeList);
            } else if (!statusList.isEmpty()) {
                // 只指定了状态
                orderPage = orderService.getUserOrdersWithMultiStatus(page, userId, statusList, null);
            } else if (!orderTypeList.isEmpty()) {
                // 只指定了订单类型
                orderPage = orderService.getUserOrdersWithMultiParams(page, userId, null, orderTypeList);
            } else {
                // 没有指定状态和订单类型
                orderPage = orderService.getUserOrders(page, userId, null, null);
            }

            return Result.OK("获取订单列表成功", orderPage);
        } catch (Exception e) {
            log.error("获取订单列表失败", e);
            return Result.error("获取订单列表失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "获取店铺订单列表")
    @ApiOperation(value = "获取店铺订单列表", notes = "获取店铺订单列表")
    @GetMapping("/store/list")
    public Result<IPage<Order>> getStoreOrders(
            @RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String status,  // 支持单个数字或逗号分隔的多个数字
            @RequestParam(required = true) String storeId,
            @RequestParam(required = false) String orderType) {  // 支持单个数字或逗号分隔的多个数字
        try {
            Page<Order> page = new Page<>(pageNo, pageSize);

            // 处理status参数，支持单个状态值或多个状态值（如 "1,2,3"）
            List<Integer> statusList = new ArrayList<>();
            if (StringUtils.isNotBlank(status)) {
                if (status.contains(",")) {
                    // 多个状态值的情况
                    String[] statusArray = status.split(",");
                    for (String s : statusArray) {
                        if (StringUtils.isNotBlank(s)) {
                            try {
                                statusList.add(Integer.parseInt(s.trim()));
                            } catch (NumberFormatException e) {
                                log.error("状态值格式错误: " + s);
                            }
                        }
                    }
                } else {
                    // 单个状态值的情况
                    try {
                        statusList.add(Integer.parseInt(status.trim()));
                    } catch (NumberFormatException e) {
                        log.error("状态值格式错误: " + status);
                    }
                }
            }

            // 处理orderType参数，支持单个类型或多个类型
            List<Integer> orderTypeList = new ArrayList<>();
            if (StringUtils.isNotBlank(orderType)) {
                if (orderType.contains(",")) {
                    // 多个订单类型的情况
                    String[] typeArray = orderType.split(",");
                    for (String t : typeArray) {
                        if (StringUtils.isNotBlank(t)) {
                            try {
                                orderTypeList.add(Integer.parseInt(t.trim()));
                            } catch (NumberFormatException e) {
                                log.error("订单类型格式错误: " + t);
                            }
                        }
                    }
                } else {
                    // 单个订单类型的情况
                    try {
                        orderTypeList.add(Integer.parseInt(orderType.trim()));
                    } catch (NumberFormatException e) {
                        log.error("订单类型格式错误: " + orderType);
                    }
                }
            }

            // 使用多状态和多类型查询
            IPage<Order> orderPage;
            if (!statusList.isEmpty() && !orderTypeList.isEmpty()) {
                // 状态和订单类型都有指定
                orderPage = orderService.getStoreOrdersWithMultiParams(page, storeId, statusList, orderTypeList);
            } else if (!statusList.isEmpty()) {
                // 只指定了状态
                orderPage = orderService.getStoreOrdersWithMultiParams(page, storeId, statusList, null);
            } else if (!orderTypeList.isEmpty()) {
                // 只指定了订单类型
                orderPage = orderService.getStoreOrdersWithMultiParams(page, storeId, null, orderTypeList);
            } else {
                // 没有指定状态和订单类型
                orderPage = orderService.getStoreOrders(page, storeId, null, null);
            }

            return Result.OK("获取店铺订单列表成功", orderPage);
        } catch (Exception e) {
            log.error("获取店铺订单列表失败", e);
            return Result.error("获取店铺订单列表失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "获取订单统计信息")
    @ApiOperation(value = "获取订单统计信息", notes = "获取订单统计信息")
    @GetMapping("/stats")
    public Result<Map<String, Object>> getOrderStats(HttpServletRequest request) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            Map<String, Object> stats = orderService.getOrderStats(userId);
            return Result.OK(stats);
        } catch (Exception e) {
            log.error("获取订单统计信息失败", e);
            return Result.error("获取订单统计信息失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "获取物流信息")
    @ApiOperation(value = "获取物流信息", notes = "获取物流信息")
    @GetMapping("/logistics/{orderId}")
    public Result<Map<String, Object>> getLogisticsInfo(@PathVariable String orderId, HttpServletRequest request) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            Map<String, Object> logisticsInfo = orderService.getLogisticsInfo(orderId, userId);

            if (logisticsInfo != null) {
                return Result.OK("获取物流信息成功", logisticsInfo);
            } else {
                return Result.error("物流信息不存在或无权限查看");
            }
        } catch (Exception e) {
            log.error("获取物流信息失败", e);
            return Result.error("获取物流信息失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "创建积分订单")
    @ApiOperation(value = "创建积分订单", notes = "创建积分商城订单")
    @PostMapping("/points/create")
    public Result<Map<String, Object>> createPointsOrder(@RequestBody Order order, HttpServletRequest request) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            String orderId = orderService.createPointsOrder(
                    userId,
                    order.getStoreId(),
                    order.getOrderItems(),
                    order.getReceiverName(),
                    order.getReceiverPhone(),
                    order.getReceiverAddress(),
                    order.getRemark()
            );

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("orderId", orderId);

            return Result.OK("积分订单创建成功", resultMap);
        } catch (Exception e) {
            log.error("创建积分订单失败", e);
            return Result.error("创建积分订单失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "支付积分订单")
    @ApiOperation(value = "支付积分订单", notes = "使用积分支付订单")
    @PostMapping("/points/pay/{orderId}")
    public Result<Map<String, Object>> payPointsOrder(@PathVariable String orderId, HttpServletRequest request) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            boolean success = orderService.payPointsOrder(orderId, userId);

            if (success) {
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("orderId", orderId);
                resultMap.put("status", 1);

                return Result.OK("积分订单支付成功", resultMap);
            } else {
                return Result.error("积分订单支付失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("支付积分订单失败", e);
            return Result.error("支付积分订单失败: " + e.getMessage());
        }
    }

    /**
     * 搜索订单
     */
    @AutoLog(value = "搜索订单")
    @ApiOperation(value = "搜索订单", notes = "根据多种条件搜索订单")
    @GetMapping("/search")
    public Result<IPage<Order>> searchOrders(
            @RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String keyword,           // 关键词搜索(订单号、商品名称、收货人)
            @RequestParam(required = false) String orderNo,           // 订单号精确搜索
            @RequestParam(required = false) String status,            // 订单状态(支持多个,如"1,2,3")
            @RequestParam(required = false) String orderType,         // 订单类型(支持多个,如"1,2")
            @RequestParam(required = false) String payType,           // 支付方式(支持多个,如"1,2,3")
            @RequestParam(required = false) String storeId,           // 店铺ID
            @RequestParam(required = false) String storeName,         // 店铺名称模糊搜索
            @RequestParam(required = false) String receiverName,      // 收货人姓名
            @RequestParam(required = false) String receiverPhone,     // 收货人电话
            @RequestParam(required = false) String startTime,         // 开始时间(yyyy-MM-dd HH:mm:ss)
            @RequestParam(required = false) String endTime,           // 结束时间(yyyy-MM-dd HH:mm:ss)
            @RequestParam(required = false) String payStartTime,      // 支付开始时间
            @RequestParam(required = false) String payEndTime,        // 支付结束时间
            @RequestParam(required = false) BigDecimal minAmount,     // 最小金额
            @RequestParam(required = false) BigDecimal maxAmount,     // 最大金额
            @RequestParam(required = false) String sortField,         // 排序字段(createTime,payTime,totalPrice)
            @RequestParam(required = false, defaultValue = "desc") String sortOrder) { // 排序方式(asc,desc)

        try {
            // 获取当前用户ID
            String userId = CommonUtils.getUserIdByToken();

            // 创建分页对象
            Page<Order> page = new Page<>(pageNo, pageSize);

            // 解析状态参数
            List<Integer> statusList = parseIntegerList(status);

            // 解析订单类型参数
            List<Integer> orderTypeList = parseIntegerList(orderType);

            // 解析支付方式参数
            List<Integer> payTypeList = parseIntegerList(payType);

            // 解析日期参数
            Date startDate = null;
            Date endDate = null;
            Date payStartDate = null;
            Date payEndDate = null;

            try {
                if (StringUtils.isNotBlank(startTime)) {
                    startDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(startTime);
                }
                if (StringUtils.isNotBlank(endTime)) {
                    endDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(endTime);
                }
                if (StringUtils.isNotBlank(payStartTime)) {
                    payStartDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(payStartTime);
                }
                if (StringUtils.isNotBlank(payEndTime)) {
                    payEndDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(payEndTime);
                }
            } catch (Exception e) {
                log.error("日期格式解析错误", e);
                return Result.error("日期格式错误，请使用yyyy-MM-dd HH:mm:ss格式");
            }

            // 调用服务层方法执行搜索
            IPage<Order> orderPage = orderService.searchOrders(
                    page,
                    userId,
                    keyword,
                    orderNo,
                    statusList,
                    orderTypeList,
                    payTypeList,
                    storeId,
                    storeName,
                    receiverName,
                    receiverPhone,
                    startDate,
                    endDate,
                    payStartDate,
                    payEndDate,
                    minAmount,
                    maxAmount,
                    sortField,
                    sortOrder
            );

            return Result.OK(orderPage);

        } catch (Exception e) {
            log.error("搜索订单失败", e);
            return Result.error("搜索订单失败: " + e.getMessage());
        }
    }


    /**
     * 解析整数列表参数
     */
    private List<Integer> parseIntegerList(String param) {
        List<Integer> result = new ArrayList<>();
        if (StringUtils.isNotBlank(param)) {
            String[] array = param.split(",");
            for (String item : array) {
                if (StringUtils.isNotBlank(item)) {
                    try {
                        result.add(Integer.parseInt(item.trim()));
                    } catch (NumberFormatException e) {
                        log.warn("参数格式错误: " + item);
                    }
                }
            }
        }
        return result;
    }
}