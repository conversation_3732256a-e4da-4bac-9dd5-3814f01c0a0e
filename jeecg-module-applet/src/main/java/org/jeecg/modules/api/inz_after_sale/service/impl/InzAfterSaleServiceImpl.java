package org.jeecg.modules.api.inz_after_sale.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.api.inz_after_sale.entity.InzAfterSale;
import org.jeecg.modules.api.inz_after_sale.mapper.InzAfterSaleMapper;
import org.jeecg.modules.api.inz_after_sale.service.IInzAfterSaleService;
// 注意：资金流水服务已迁移到system模块，前端模块不再直接操作资金流水
import org.jeecg.modules.api.user_front.entity.Order;
import org.jeecg.modules.api.user_front.service.IOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Map;

/**
 * @Description: 商品售后表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
@Service
@Slf4j
public class InzAfterSaleServiceImpl extends ServiceImpl<InzAfterSaleMapper, InzAfterSale> implements IInzAfterSaleService {
    
    @Autowired
    private IOrderService orderService;

    // 注意：资金流水服务已迁移到system模块，前端模块不再直接操作资金流水
    // 如需操作资金流水，应通过后台管理系统或API调用

    @Override
    public IPage<InzAfterSale> queryAfterSaleByStoreId(Page<InzAfterSale> page, 
                                                     InzAfterSale afterSale, 
                                                     String storeId,
                                                     Map<String, String[]> parameterMap) {
        QueryWrapper<InzAfterSale> queryWrapper = QueryGenerator.initQueryWrapper(afterSale, parameterMap);
        queryWrapper.eq("store_id", storeId);
        return this.page(page, queryWrapper);
    }

    @Override
    public IPage<InzAfterSale> queryAfterSaleByUserId(Page<InzAfterSale> page, 
                                                    InzAfterSale afterSale, 
                                                    String userId,
                                                    Map<String, String[]> parameterMap) {
        QueryWrapper<InzAfterSale> queryWrapper = QueryGenerator.initQueryWrapper(afterSale, parameterMap);
        queryWrapper.eq("user_id", userId);
        return this.page(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditAfterSale(String id, Integer status, String handleNote) {
        InzAfterSale afterSale = this.getById(id);
        if (afterSale == null) {
            return false;
        }
        
        afterSale.setStatus(status);
        afterSale.setHandleNote(handleNote);
        afterSale.setAuditTime(new Date());
        
        // 如果是拒绝，则直接完成
        if (status == 3) {
            afterSale.setFinishTime(new Date());
        }
        
        // 如果是通过且是仅退款，则更新状态为退款中
        if (status == 2 && afterSale.getType() == 1) {
            afterSale.setStatus(4); // 退款中
        }
        
        return this.updateById(afterSale);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean confirmRefund(String id, String refundNo) {
        InzAfterSale afterSale = this.getById(id);
        if (afterSale == null) {
            return false;
        }
        
        afterSale.setStatus(5); // 已完成
        afterSale.setRefundNo(refundNo);
        afterSale.setRefundTime(new Date());
        afterSale.setFinishTime(new Date());
        
        return this.updateById(afterSale);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processRefundWithTransaction(String id, String refundNo, String operator) {
        InzAfterSale afterSale = this.getById(id);
        if (afterSale == null) {
            log.error("处理退款失败，售后记录不存在，ID：{}", id);
            return false;
        }
        
        if (afterSale.getStatus() != 4) { // 不是退款中状态
            log.error("处理退款失败，售后状态不正确，当前状态：{}", afterSale.getStatus());
            return false;
        }
        
        // 获取关联订单
        Order order = orderService.getById(afterSale.getOrderId());
        if (order == null) {
            log.error("处理退款失败，关联订单不存在，订单ID：{}", afterSale.getOrderId());
            return false;
        }
        
        try {
            // 更新售后状态
            afterSale.setStatus(5); // 已完成
            afterSale.setRefundNo(refundNo);
            afterSale.setRefundTime(new Date());
            afterSale.setFinishTime(new Date());
            this.updateById(afterSale);
            
            // 更新订单状态
            order.setRefundStatus(4); // 退款成功
            order.setRefundTime(new Date());
            orderService.updateById(order);

            // 注意：资金流水记录创建已迁移到后台管理系统
            // 前端模块只负责更新售后和订单状态
            // 资金流水记录应该通过后台管理系统或定时任务创建
            log.info("售后退款处理完成，售后ID：{}，订单ID：{}，退款金额：{}",
                    id, order.getId(), afterSale.getRefundAmount());
            log.info("注意：资金流水记录需要在后台管理系统中手动创建或通过定时任务自动创建");
            
            return true;
        } catch (Exception e) {
            log.error("处理退款异常", e);
            throw e;
        }
    }
}