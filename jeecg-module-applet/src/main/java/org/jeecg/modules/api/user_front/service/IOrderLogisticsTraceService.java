package org.jeecg.modules.api.user_front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.user_front.entity.OrderLogisticsTrace;

import java.util.List;

/**
 * @Description: 订单物流轨迹服务接口
 * @Author: jeecg-boot
 * @Date: 2024-06-01
 * @Version: V1.0
 */
public interface IOrderLogisticsTraceService extends IService<OrderLogisticsTrace> {

    /**
     * 根据物流ID获取物流轨迹列表
     * @param logisticsId 物流ID
     * @return 物流轨迹列表
     */
    List<OrderLogisticsTrace> getTracesByLogisticsId(String logisticsId);

    /**
     * 添加物流轨迹
     * @param logisticsId 物流ID
     * @param status 状态
     * @param info 物流详情描述
     * @return 是否成功
     */
    boolean addTrace(String logisticsId, Integer status, String info);
} 