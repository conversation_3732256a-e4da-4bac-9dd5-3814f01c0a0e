package org.jeecg.modules.api.user_front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.user_front.entity.PointsRecord;

/**
 * 积分记录服务接口
 */
public interface IPointsRecordService extends IService<PointsRecord> {
    
    /**
     * 添加积分记录
     * @param userId 用户ID
     * @param points 积分数量
     * @param type 类型（1-增加，2-减少）
     * @param source 来源（1-签到，2-消费，3-退款，4-活动）
     * @param relatedId 关联ID（订单ID等）
     * @param remark 备注
     * @return 是否成功
     */
    boolean addPointsRecord(String userId, Integer points, Integer type, Integer source, String relatedId, String remark);
} 