package org.jeecg.modules.api.user_front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.user_front.entity.UserFront;

/**
 * 用户积分服务接口
 */
public interface IUserPointsService extends IService<UserFront> {
    
    /**
     * 增加用户积分
     * @param userId 用户ID
     * @param points 积分数量
     * @return 是否成功
     */
    boolean increasePoints(String userId, Integer points);
    
    /**
     * 扣减用户积分
     * @param userId 用户ID
     * @param points 积分数量
     * @return 是否成功
     * @deprecated 使用 {@link #deductPoints(String, Integer, String, String)} 代替
     */
    @Deprecated
    boolean decreasePoints(String userId, Integer points);
    
    /**
     * 扣减用户积分并记录变动原因
     * @param userId 用户ID
     * @param points 积分数量
     * @param reason 变动原因
     * @param orderId 关联订单ID
     * @return 是否成功
     */
    boolean deductPoints(String userId, Integer points, String reason, String orderId);
    
    /**
     * 获取用户积分
     * @param userId 用户ID
     * @return 用户积分
     */
    Integer getUserPoints(String userId);
} 