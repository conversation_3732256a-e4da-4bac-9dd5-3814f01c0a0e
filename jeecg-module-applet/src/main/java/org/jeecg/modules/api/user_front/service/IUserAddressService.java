package org.jeecg.modules.api.user_front.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.user_front.entity.UserAddress;

import java.util.List;

/**
 * @Description: 用户地址服务接口
 * @Author: jeecg-boot
 * @Date: 2024-01-24
 * @Version: V1.0
 */
public interface IUserAddressService extends IService<UserAddress> {
    
    /**
     * 获取用户地址列表
     *
     * @param userId 用户ID
     * @return 地址列表
     */
    List<UserAddress> getUserAddressList(String userId);
    
    /**
     * 设置默认地址
     *
     * @param userId 用户ID
     * @param addressId 地址ID
     * @return 是否成功
     */
    boolean setDefaultAddress(String userId, String addressId);
    
    /**
     * 获取用户默认地址
     *
     * @param userId 用户ID
     * @return 默认地址，如果没有则返回null
     */
    UserAddress getDefaultAddress(String userId);
} 