package org.jeecg.modules.api.user_front.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.api.store.entity.Store;
import org.jeecg.modules.api.store.service.IStoreService;
import org.jeecg.modules.api.user_front.entity.Coupon;
import org.jeecg.modules.api.user_front.entity.UserCoupon;
import org.jeecg.modules.api.user_front.enums.CouponCategoryEnum;
import org.jeecg.modules.api.user_front.enums.CouponSubCategoryEnum;
import org.jeecg.modules.api.user_front.service.ICouponManagerService;
import org.jeecg.modules.api.user_front.service.ICouponService;
import org.jeecg.modules.api.user_front.service.IUserCouponService;
import org.jeecg.modules.api.user_front.entity.UserCoupon;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.api.user_front.vo.CouponCategoryVO;
import org.jeecg.modules.api.user_front.vo.CouponVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 优惠券接口
 * @Author: jeecg-boot
 * @Date: 2024-01-24
 * @Version: V1.0
 */
@Api(tags = "优惠券接口")
@RestController
@RequestMapping("/api/user/coupon")
@Slf4j
public class CouponController {

    @Autowired
    private ICouponService couponService;

    @Autowired
    private ICouponManagerService couponManagerService;

    @Autowired
    private IStoreService storeService;

    @Autowired
    private IUserCouponService userCouponService;

    @AutoLog(value = "优惠券-获取可用优惠券列表")
    @ApiOperation(value = "优惠券-获取可用优惠券列表", notes = "获取可用优惠券列表")
    @GetMapping(value = "/available")
    public Result<IPage<CouponVO>> getAvailableCoupons(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String storeId,
            HttpServletRequest req) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            Page<CouponVO> page = new Page<>(pageNo, pageSize);
            IPage<CouponVO> pageList = couponService.getAvailableCoupons(page, storeId, userId);
            return Result.OK("获取可用优惠券列表成功", pageList);
        } catch (Exception e) {
            log.error("获取可用优惠券列表失败", e);
            return Result.error("获取可用优惠券列表失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "优惠券-获取我的优惠券列表")
    @ApiOperation(value = "优惠券-获取我的优惠券列表", notes = "获取我的优惠券列表")
    @GetMapping(value = "/my")
    public Result<IPage<CouponVO>> getMyCoupons(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Integer subCategory) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            Page<CouponVO> page = new Page<>(pageNo, pageSize);
            IPage<CouponVO> pageList = couponService.getMyCoupons(page, userId, status, subCategory);
            return Result.OK("获取我的优惠券列表成功", pageList);
        } catch (Exception e) {
            log.error("获取我的优惠券列表失败", e);
            return Result.error("获取我的优惠券列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取优惠券主分类", notes = "获取优惠券主分类")
    @GetMapping("/categories")
    public Result<List<CouponCategoryVO>> getCouponCategories() {
        List<CouponCategoryVO> categories = Arrays.stream(CouponCategoryEnum.values())
                .map(e -> new CouponCategoryVO(e.getCode(), e.getName()))
                .collect(Collectors.toList());
        return Result.OK("获取成功", categories);
    }

    @ApiOperation(value = "获取优惠券分类", notes = "获取优惠券分类")
    @GetMapping("/subcategories")
    public Result<List<CouponCategoryVO>> getCouponSubCategories() {
        List<CouponCategoryVO> categories = Arrays.stream(CouponSubCategoryEnum.values())
                .map(e -> new CouponCategoryVO(e.getCode(), e.getName()))
                .collect(Collectors.toList());
        return Result.OK("获取成功", categories);
    }

    @Data
    public static class CouponReceiveRequest {
        private List<String> couponIds;
    }

    @AutoLog(value = "优惠券-领取优惠券")
    @ApiOperation(value = "优惠券-领取优惠券", notes = "领取多个优惠券")
    @PostMapping(value = "/receive")
    public Result<Map<String, Object>> receiveCoupons(@RequestBody CouponReceiveRequest request) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            int successCount = 0;
            List<Map<String, Object>> details = new ArrayList<>();

            for (String couponId : request.getCouponIds()) {
                Map<String, Object> result = new HashMap<>();
                Coupon coupon = couponService.getById(couponId);
                if (coupon == null) {
                    result.put("success", false);
                    result.put("message", "优惠券不存在");
                } else {
                    Map<String, Object> receiveResult = couponService.receiveSingleCoupon(couponId, userId);
                    result.putAll(receiveResult);
                    if (Boolean.TRUE.equals(receiveResult.get("success"))) {
                        successCount++;
                    }
                }
                details.add(result);
            }
            Map<String, Object> response = new HashMap<>();
            response.put("successCount", successCount);
            response.put("details", details);
            return Result.OK("成功领取", response);
        } catch (Exception e) {
            log.error("领取优惠券失败", e);
            return Result.error("领取优惠券失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "优惠券-使用优惠券")
    @ApiOperation(value = "优惠券-使用优惠券", notes = "使用优惠券")
    @PostMapping(value = "/use")
    public Result<Map<String, Object>> useCoupon(
            @RequestParam String couponId,
            @RequestParam String orderId,
            @RequestParam BigDecimal totalPrice,
            HttpServletRequest req) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            BigDecimal finalAmount = couponManagerService.useCoupon(couponId, userId, orderId, totalPrice);

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("couponId", couponId);
            resultMap.put("orderId", orderId);
            resultMap.put("originalAmount", totalPrice);
            resultMap.put("discountAmount", totalPrice.subtract(finalAmount));
            resultMap.put("finalAmount", finalAmount);

            return Result.OK("优惠券使用成功", resultMap);
        } catch (Exception e) {
            log.error("使用优惠券失败", e);
            return Result.error("使用优惠券失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "优惠券-检查优惠券是否可用")
    @ApiOperation(value = "优惠券-检查优惠券是否可用", notes = "检查优惠券是否可用")
    @GetMapping(value = "/check")
    public Result<Map<String, Object>> checkCouponAvailable(
            @RequestParam String couponId,
            @RequestParam BigDecimal totalPrice,
            HttpServletRequest req) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            boolean available = couponManagerService.checkCouponAvailable(couponId, userId, totalPrice);

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("couponId", couponId);
            resultMap.put("totalPrice", totalPrice);
            resultMap.put("available", available);
            if (available) {
                return Result.OK("优惠券可用", resultMap);
            }
            return Result.OK("该优惠券不可用", resultMap);
        } catch (Exception e) {
            log.error("检查优惠券可用性失败", e);
            return Result.error("检查优惠券可用性失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "优惠券-获取不同类型的优惠券")
    @ApiOperation(value = "优惠券-获取不同类型的优惠券", notes = "获取不同类型的优惠券")
    @GetMapping(value = "/all")
    public Result<IPage<CouponVO>> getAllCoupons(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(defaultValue = "1") String mainCategory,
            @RequestParam(defaultValue = "1") String subCategory) {
        try {
            // 获取当前用户ID
            String userId = null;
            try {
                userId = CommonUtils.getUserIdByToken();
            } catch (Exception e) {
                log.debug("用户未登录，优惠券领取状态将显示为false");
            }
            
            // 创建分页对象
            Page<CouponVO> page = new Page<>(pageNo, pageSize);
            
            // 处理"全部"分类的逻辑
            String finalMainCategory = "1".equals(mainCategory) ? null : mainCategory;
            String finalSubCategory = "1".equals(subCategory) ? null : subCategory;
            
            // 使用统一的查询方法
            IPage<CouponVO> pageList = couponService.getCouponsWithClaimStatus(
                page, null, userId, 1, true, finalMainCategory, finalSubCategory, null
            );
            
            return Result.OK("获取优惠券列表成功", pageList);
        } catch (Exception e) {
            log.error("获取优惠券列表失败", e);
            return Result.error("获取优惠券列表失败: " + e.getMessage());
        }
    }

    /**
     * 兑换码请求体
     */
    @Data
    public static class RedeemCouponRequest {
        private List<String> codes;
    }

    /**
     * 使用兑换码兑换优惠券
     *
     * @param request 包含兑换码列表的请求
     * @return 兑换结果
     */
    @AutoLog(value = "优惠券-使用兑换码兑换优惠券")
    @ApiOperation(value = "优惠券-使用兑换码兑换优惠券", notes = "使用兑换码兑换优惠券")
    @PostMapping(value = "/redeem")
    public Result<Map<String, Object>> redeemCouponByCode(@RequestBody RedeemCouponRequest request) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            
            if (request.getCodes() == null || request.getCodes().isEmpty()) {
                return Result.error("兑换码不能为空");
            }
            
            List<CouponVO> successList = new ArrayList<>();
            List<Map<String, Object>> failList = new ArrayList<>();
            
            for (String code : request.getCodes()) {
                try {
            CouponVO couponVO = couponService.redeemCouponByCode(code, userId);
                    if (couponVO != null) {
                        successList.add(couponVO);
                        couponVO.setIsReceivedInt(1); // 兑换成功，设置为已领取
                    }
                } catch (Exception e) {
                    Map<String, Object> failItem = new HashMap<>();
                    failItem.put("code", code);
                    failItem.put("message", e.getMessage());
                    failList.add(failItem);
                    log.error("兑换优惠券失败: " + code, e);
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("successCount", successList.size());
            result.put("failCount", failList.size());
            result.put("successList", successList);
            result.put("failList", failList);
            
            if (successList.isEmpty()) {
                return Result.error("所有兑换码兑换失败", result);
            } else if (failList.isEmpty()) {
                return Result.OK("所有兑换码兑换成功", result);
            } else {
                return Result.OK("部分兑换码兑换成功", result);
            }
        } catch (Exception e) {
            log.error("兑换优惠券失败", e);
            return Result.error("兑换优惠券失败: " + e.getMessage());
        }
    }

    /**
     * 获取优惠券列表（包含当前用户领取状态）
     *
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @param storeId 店铺ID（可选）
     * @param mainCategory 主分类（可选）
     * @param subCategory 子分类（可选）
     * @param onlyAvailable 是否只查询可用的（可选，默认true）
     * @return 优惠券列表，每个优惠券都包含claimed字段表示当前用户是否已领取
     */
    @AutoLog(value = "获取优惠券列表（包含领取状态）")
    @ApiOperation(value = "获取优惠券列表（包含领取状态）", notes = "获取优惠券列表，自动包含当前用户的领取状态")
    @GetMapping(value = "/listWithStatus")
    public Result<IPage<CouponVO>> getCouponsWithStatus(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(name = "storeId", required = false) String storeId,
            @RequestParam(name = "mainCategory", required = false) String mainCategory,
            @RequestParam(name = "subCategory", required = false) String subCategory,
            @RequestParam(name = "onlyAvailable", defaultValue = "true") Boolean onlyAvailable) {

        try {
            // 获取当前用户ID（可能为null，表示未登录）
            String userId = null;
            try {
                userId = CommonUtils.getUserIdByToken();
            } catch (Exception e) {
                log.debug("用户未登录，优惠券领取状态将显示为false");
            }

            // 创建分页对象
            Page<CouponVO> page = new Page<>(pageNo, pageSize);

            // 查询优惠券列表（包含领取状态）
            IPage<CouponVO> pageList = couponService.getCouponsWithClaimStatus(
                page, storeId, userId, 1, onlyAvailable, mainCategory, subCategory, null
            );

            return Result.OK("获取优惠券列表成功", pageList);

        } catch (Exception e) {
            log.error("获取优惠券列表失败", e);
            return Result.error("获取优惠券列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取店铺优惠券（包含当前用户领取状态）
     *
     * @param storeId 店铺ID
     * @param mainCategory 主分类（可选）
     * @param subCategory 子分类（可选）
     * @return 店铺优惠券列表，每个优惠券都包含claimed字段
     */
    @AutoLog(value = "获取店铺优惠券（包含领取状态）")
    @ApiOperation(value = "获取店铺优惠券（包含领取状态）", notes = "获取指定店铺的优惠券，自动包含当前用户的领取状态")
    @GetMapping(value = "/store/{storeId}/withStatus")
    public Result<List<CouponVO>> getStoreCouponsWithStatus(
            @PathVariable("storeId") String storeId,
            @RequestParam(name = "mainCategory", required = false) String mainCategory,
            @RequestParam(name = "subCategory", required = false) String subCategory) {

        try {
            // 获取当前用户ID（可能为null，表示未登录）
            String userId = null;
            try {
                userId = CommonUtils.getUserIdByToken();
            } catch (Exception e) {
                log.debug("用户未登录，优惠券领取状态将显示为false");
            }

            // 查询店铺优惠券列表（包含领取状态）
            List<CouponVO> coupons = couponService.getStoreCouponsWithClaimStatus(
                storeId, userId, mainCategory, subCategory
            );

            return Result.OK("获取店铺优惠券成功", coupons);

        } catch (Exception e) {
            log.error("获取店铺优惠券失败", e);
            return Result.error("获取店铺优惠券失败: " + e.getMessage());
        }
    }
}