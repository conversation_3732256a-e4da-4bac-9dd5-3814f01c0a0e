package org.jeecg.modules.api.inz_after_sale.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.api.inz_after_sale.entity.InzAfterSale;
import org.jeecg.modules.api.inz_after_sale.service.IInzAfterSaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;

/**
 * @Description: 商品售后表
 * @Author: jeecg-boot
 * @Date: 2025-06-18
 * @Version: V1.0
 */
@RestController
@RequestMapping("/inz_after_sale/inzAfterSale")
@Slf4j
public class InzAfterSaleController extends JeecgController<InzAfterSale, IInzAfterSaleService> {
    @Autowired
    private IInzAfterSaleService inzAfterSaleService;

    /**
     * 分页列表查询
     */
    @GetMapping(value = "/list")
    public Result<IPage<InzAfterSale>> queryPageList(InzAfterSale inzAfterSale,
                                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                     @RequestParam(name = "storeId", required = false) String storeId,
                                                     HttpServletRequest req) {
        Page<InzAfterSale> page = new Page<>(pageNo, pageSize);
        IPage<InzAfterSale> pageList;

        if (StringUtils.isNotBlank(storeId)) {
            // 如果提供了storeId，使用自定义方法按storeId过滤
            pageList = inzAfterSaleService.queryAfterSaleByStoreId(page, inzAfterSale, storeId, req.getParameterMap());
        } else {
            // 不需要storeId过滤的原始方法
            QueryWrapper<InzAfterSale> queryWrapper = QueryGenerator.initQueryWrapper(inzAfterSale, req.getParameterMap());
            pageList = inzAfterSaleService.page(page, queryWrapper);
        }

        return Result.OK(pageList);
    }

    /**
     * 用户查询自己的售后申请
     */
    @GetMapping(value = "/user/list")
    public Result<IPage<InzAfterSale>> queryUserAfterSales(InzAfterSale inzAfterSale,
                                                           @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                           @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                           HttpServletRequest req) {
        // 获取当前登录用户ID
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userId = sysUser.getId();

        Page<InzAfterSale> page = new Page<>(pageNo, pageSize);
        IPage<InzAfterSale> pageList = inzAfterSaleService.queryAfterSaleByUserId(page, inzAfterSale, userId, req.getParameterMap());

        return Result.OK(pageList);
    }

    /**
     * 添加售后申请
     */
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InzAfterSale inzAfterSale) {
        // 获取当前登录用户ID
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        inzAfterSale.setUserId(sysUser.getId());
        inzAfterSale.setCreateBy(sysUser.getUsername());
        inzAfterSale.setStatus(1); // 待审核
        inzAfterSale.setCreateTime(new Date());

        boolean success = inzAfterSaleService.save(inzAfterSale);
        if (success) {
            return Result.OK("售后申请提交成功！");
        } else {
            return Result.error("售后申请提交失败！");
        }
    }

    /**
     * 商家审核售后
     */
    @PostMapping(value = "/audit")
    @RequiresPermissions("inz_after_sale:inz_after_sale:audit")
    public Result<String> audit(@RequestParam(name = "id", required = true) String id,
                                @RequestParam(name = "status", required = true) Integer status,
                                @RequestParam(name = "handleNote", required = false) String handleNote) {
        boolean success = inzAfterSaleService.auditAfterSale(id, status, handleNote);
        if (success) {
            return Result.OK("审核成功！");
        } else {
            return Result.error("审核失败，请检查数据！");
        }
    }

    /**
     * 确认退款
     */
    @PostMapping(value = "/refund")
    @RequiresPermissions("inz_after_sale:inz_after_sale:refund")
    public Result<String> confirmRefund(@RequestParam(name = "id", required = true) String id,
                                        @RequestParam(name = "refundNo", required = true) String refundNo) {
        boolean success = inzAfterSaleService.confirmRefund(id, refundNo);
        if (success) {
            return Result.OK("退款操作成功！");
        } else {
            return Result.error("退款操作失败！");
        }
    }

    /**
     * 处理退款并创建资金流水记录
     */
    @PostMapping(value = "/process-refund")
    @RequiresPermissions("inz_after_sale:inz_after_sale:refund")
    public Result<String> processRefund(@RequestParam(name = "id", required = true) String id,
                                       @RequestParam(name = "refundNo", required = true) String refundNo,
                                       @RequestParam(name = "operator", required = false) String operator) {
        try {
            boolean success = inzAfterSaleService.processRefundWithTransaction(id, refundNo, operator);
            if (success) {
                return Result.OK("退款处理成功，已更新店铺资金流水！");
            } else {
                return Result.error("退款处理失败！");
            }
        } catch (Exception e) {
            log.error("退款处理异常", e);
            return Result.error("退款处理异常：" + e.getMessage());
        }
    }

    /**
     * 通过id查询
     */
    @GetMapping(value = "/queryById")
    public Result<InzAfterSale> queryById(@RequestParam(name = "id", required = true) String id) {
        InzAfterSale inzAfterSale = inzAfterSaleService.getById(id);
        if (inzAfterSale == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzAfterSale);
    }

    /**
     * 删除
     */
    @DeleteMapping(value = "/delete")
    @RequiresPermissions("inz_after_sale:inz_after_sale:delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        inzAfterSaleService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @DeleteMapping(value = "/deleteBatch")
    @RequiresPermissions("inz_after_sale:inz_after_sale:deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzAfterSaleService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 导出excel
     */
    @RequiresPermissions("inz_after_sale:inz_after_sale:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzAfterSale inzAfterSale) {
        return super.exportXls(request, inzAfterSale, InzAfterSale.class, "商品售后表");
    }

    /**
     * 通过excel导入数据
     */
    @RequiresPermissions("inz_after_sale:inz_after_sale:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzAfterSale.class);
    }
}