package org.jeecg.modules.api.user_front.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.user_front.entity.Exhibition;

/**
 * @Description: 展馆服务接口
 * @Author: jeecg-boot
 * @Date: 2024-06-12
 * @Version: V1.0
 */
public interface IExhibitionService extends IService<Exhibition> {
    
    /**
     * 创建展馆
     *
     * @param userId 用户ID
     * @param name 展馆名称
     * @param description 展馆描述
     * @param coverImage 封面图
     * @param status 状态
     * @return 展馆ID
     */
    String createExhibition(String userId, String name, String description, String coverImage, Integer status);
    
    /**
     * 更新展馆
     *
     * @param exhibitionId 展馆ID
     * @param name 展馆名称
     * @param description 展馆描述
     * @param coverImage 封面图
     * @param status 状态
     * @return 是否成功
     */
    boolean updateExhibition(String exhibitionId, String name, String description, String coverImage, Integer status);
    
    /**
     * 删除展馆
     *
     * @param exhibitionId 展馆ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deleteExhibition(String exhibitionId, String userId);
    
    /**
     * 获取展馆详情
     *
     * @param exhibitionId 展馆ID
     * @return 展馆详情
     */
    Exhibition getExhibitionDetail(String exhibitionId);
    
    /**
     * 获取用户的展馆列表
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @return 展馆列表
     */
    IPage<Exhibition> getUserExhibitions(Page<Exhibition> page, String userId);
    
    /**
     * 获取公开的展馆列表
     *
     * @param page 分页参数
     * @return 公开展馆列表
     */
    IPage<Exhibition> getPublicExhibitions(Page<Exhibition> page);
    
    /**
     * 向展馆添加卡片
     * 
     * @param exhibitionId 展馆ID
     * @param cardId 卡片ID
     * @param cardName 卡片名称
     * @param cardImage 卡片图片
     * @param cardGrade 卡片分级
     * @param team 所属球队
     * @return 是否成功
     */
    boolean addCard(String exhibitionId, String cardId, String cardName, String cardImage, String cardGrade, String team);
    
    /**
     * 从展馆移除卡片
     * 
     * @param exhibitionId 展馆ID
     * @param cardId 卡片ID
     * @return 是否成功
     */
    boolean removeCard(String exhibitionId, String cardId);
} 