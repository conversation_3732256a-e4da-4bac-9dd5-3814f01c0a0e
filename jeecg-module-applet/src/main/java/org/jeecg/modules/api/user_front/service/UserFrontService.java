package org.jeecg.modules.api.user_front.service;

import org.jeecg.modules.api.user_front.entity.UserFront;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: inz_user_front
 * @Author: jeecg-boot
 * @Date:   2025-01-13
 * @Version: V1.0
 */
public interface UserFrontService extends IService<UserFront> {

    /**
     * 获取用户信息，并计算加入时长
     * @param userId 用户ID
     * @return 用户信息，包含加入时长
     */
    UserFront getUserWithJoinDuration(String userId);
    
    /**
     * 为用户列表计算加入时长
     * @param userList 用户列表
     */
    void calculateJoinDurationForList(java.util.List<UserFront> userList);
}
