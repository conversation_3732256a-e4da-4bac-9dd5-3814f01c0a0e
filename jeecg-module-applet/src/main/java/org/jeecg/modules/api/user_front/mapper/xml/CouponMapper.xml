<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.user_front.mapper.CouponMapper">

    <!-- 获取可用优惠券列表 -->
    <select id="getAvailableCoupons" resultType="org.jeecg.modules.api.user_front.vo.CouponVO">
        SELECT
            c.*,
            s.name as store_name,
            CASE WHEN uc.id IS NOT NULL THEN 1 ELSE 0 END as is_received,
            CASE WHEN uc.id IS NOT NULL THEN true ELSE false END as claimed,
            CASE
                WHEN NOW() > c.end_time THEN 2
                WHEN uc.status IS NOT NULL THEN uc.status
                ELSE 0
            END as status,
            DATEDIFF(c.end_time, NOW()) as days_remaining
        FROM inz_coupon c
        LEFT JOIN inz_store s ON c.store_id = s.id
        LEFT JOIN inz_user_coupon uc ON c.id = uc.coupon_id AND uc.user_id = #{userId}
        WHERE
            c.remaining_quantity > 0
            AND NOW() BETWEEN c.start_time AND c.end_time
            <if test="storeId != null and storeId != ''">
                AND c.store_id = #{storeId}
            </if>
        ORDER BY c.create_time DESC
    </select>

    <!-- 获取我的优惠券列表 -->
    <select id="getMyCoupons" resultType="org.jeecg.modules.api.user_front.vo.CouponVO">
        SELECT
            c.*,
            s.name as store_name,
            1 as is_received,
            true as claimed,
            CASE
                WHEN NOW() > c.end_time THEN 2
                WHEN uc.status IS NOT NULL THEN uc.status
                ELSE 0
            END as status,
            DATEDIFF(c.end_time, NOW()) as days_remaining
        FROM inz_user_coupon uc
        JOIN inz_coupon c ON uc.coupon_id = c.id
        LEFT JOIN inz_store s ON c.store_id = s.id
        WHERE
            uc.user_id = #{userId}
            <if test="status != null">
                AND (
                    CASE
                        WHEN NOW() > c.end_time THEN 2
                        WHEN uc.status IS NOT NULL THEN uc.status
                        ELSE 0
                    END
                ) = #{status}
            </if>
            <if test="subCategory != null">
                AND c.sub_category = #{subCategory}
            </if>
        ORDER BY uc.create_time DESC
    </select>

    <!-- 通用查询优惠券列表（包含用户领取状态） -->
    <select id="getCouponsWithClaimStatus" resultType="org.jeecg.modules.api.user_front.vo.CouponVO">
        SELECT
            c.*,
            s.name as store_name,
            CASE WHEN uc.id IS NOT NULL THEN 1 ELSE 0 END as is_received,
            CASE WHEN uc.id IS NOT NULL THEN true ELSE false END as claimed,
            CASE
                WHEN NOW() > c.end_time THEN 2
                WHEN uc.status IS NOT NULL THEN uc.status
                ELSE 0
            END as status,
            DATEDIFF(c.end_time, NOW()) as days_remaining
        FROM inz_coupon c
        LEFT JOIN inz_store s ON c.store_id = s.id
        LEFT JOIN inz_user_coupon uc ON c.id = uc.coupon_id
            <if test="userId != null and userId != ''">
                AND uc.user_id = #{userId}
            </if>
        WHERE 1=1
            <if test="storeId != null and storeId != ''">
                AND c.store_id = #{storeId}
            </if>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="onlyAvailable != null and onlyAvailable == true">
                AND c.remaining_quantity > 0
                AND NOW() BETWEEN c.start_time AND c.end_time
            </if>
            <if test="mainCategory != null and mainCategory != ''">
                AND c.main_category = #{mainCategory}
            </if>
            <if test="subCategory != null and subCategory != ''">
                AND c.sub_category = #{subCategory}
            </if>
        ORDER BY
            <if test="orderBy != null and orderBy != ''">
                ${orderBy}
            </if>
            <if test="orderBy == null or orderBy == ''">
                c.create_time DESC
            </if>
    </select>

    <!-- 查询店铺优惠券（包含用户领取状态） -->
    <select id="getStoreCouponsWithClaimStatus" resultType="org.jeecg.modules.api.user_front.vo.CouponVO">
        SELECT
            c.*,
            s.name as store_name,
            CASE WHEN uc.id IS NOT NULL THEN 1 ELSE 0 END as is_received,
            CASE WHEN uc.id IS NOT NULL THEN true ELSE false END as claimed,
            CASE
                WHEN NOW() > c.end_time THEN 2
                ELSE COALESCE(uc.status, 0)
            END as status,
            DATEDIFF(c.end_time, NOW()) as days_remaining
        FROM inz_coupon c
        LEFT JOIN inz_store s ON c.store_id = s.id
        LEFT JOIN inz_user_coupon uc ON c.id = uc.coupon_id
            <if test="userId != null and userId != ''">
                AND uc.user_id = #{userId}
            </if>
        WHERE c.store_id = #{storeId}
            AND c.status = 1
            AND c.remaining_quantity > 0
            AND NOW() BETWEEN c.start_time AND c.end_time
            <if test="mainCategory != null and mainCategory != ''">
                AND c.main_category = #{mainCategory}
            </if>
            <if test="subCategory != null and subCategory != ''">
                AND c.sub_category = #{subCategory}
            </if>
        ORDER BY c.sort ASC, c.create_time DESC
    </select>

</mapper>