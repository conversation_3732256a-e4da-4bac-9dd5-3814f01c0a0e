package org.jeecg.modules.api.user_front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.user_front.entity.OrderLogistics;
import org.jeecg.modules.api.user_front.entity.OrderLogisticsTrace;

import java.util.List;

/**
 * @Description: 订单物流服务接口
 * @Author: jeecg-boot
 * @Date: 2024-06-01
 * @Version: V1.0
 */
public interface IOrderLogisticsService extends IService<OrderLogistics> {

    /**
     * 根据订单ID获取物流信息
     * @param orderId 订单ID
     * @param bizType 业务类型(1:普通订单,2:积分订单)
     * @return 物流信息
     */
    OrderLogistics getLogisticsByOrderId(String orderId, Integer bizType);

    /**
     * 根据物流单号获取物流信息
     * @param trackingNo 物流单号
     * @return 物流信息
     */
    OrderLogistics getLogisticsByTrackingNo(String trackingNo);

    /**
     * 创建物流信息
     * @param orderId 订单ID
     * @param orderNo 订单编号
     * @param bizType 业务类型(1:普通订单,2:积分订单)
     * @param trackingNo 物流单号
     * @param expressCompany 物流公司
     * @param expressCode 物流公司编码
     * @return 物流信息ID
     */
    String createLogistics(String orderId, String orderNo, Integer bizType, 
                         String trackingNo, String expressCompany, String expressCode);

    /**
     * 更新物流状态
     * @param id 物流信息ID
     * @param status 物流状态
     * @param latestInfo 最新物流信息
     * @return 是否成功
     */
    boolean updateLogisticsStatus(String id, Integer status, String latestInfo);

    /**
     * 添加物流轨迹
     * @param logisticsId 物流信息ID
     * @param status 状态
     * @param info 物流详情描述
     * @return 是否成功
     */
    boolean addLogisticsTrace(String logisticsId, Integer status, String info);

    /**
     * 获取物流轨迹列表
     * @param logisticsId 物流信息ID
     * @return 物流轨迹列表
     */
    List<OrderLogisticsTrace> getLogisticsTraces(String logisticsId);
} 