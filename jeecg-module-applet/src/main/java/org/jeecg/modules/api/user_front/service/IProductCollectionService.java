package org.jeecg.modules.api.user_front.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.user_front.entity.ProductCollection;

/**
 * @Description: 商品收藏服务接口
 * @Author: jeecg-boot
 * @Date: 2024-06-12
 * @Version: V1.0
 */
public interface IProductCollectionService extends IService<ProductCollection> {
    
    /**
     * 收藏商品
     *
     * @param userId 用户ID
     * @param productId 商品ID
     * @param productType 商品类型
     * @param storeId 店铺ID
     * @return 是否成功
     */
    boolean collectProduct(String userId, String productId, Integer productType, String storeId);
    
    /**
     * 取消收藏
     *
     * @param userId 用户ID
     * @param productId 商品ID
     * @return 是否成功
     */
    boolean cancelCollection(String userId, String productId);
    
    /**
     * 检查商品是否已收藏
     *
     * @param userId 用户ID
     * @param productId 商品ID
     * @return 是否已收藏
     */
    boolean isCollected(String userId, String productId);
    
    /**
     * 获取用户收藏的商品列表
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @param productType 商品类型
     * @return 收藏列表
     */
    IPage<ProductCollection> getUserCollections(Page<ProductCollection> page, String userId, Integer productType);
} 