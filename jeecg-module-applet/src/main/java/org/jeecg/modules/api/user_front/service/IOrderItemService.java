package org.jeecg.modules.api.user_front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.user_front.entity.OrderItem;

import java.util.List;

/**
 * @Description: 订单详情服务接口
 * @Author: jeecg-boot
 * @Date: 2024-01-24
 * @Version: V1.0
 */
public interface IOrderItemService extends IService<OrderItem> {

    /**
     * 批量保存订单详情
     *
     * @param orderId 订单ID
     * @param orderItems 订单详情列表
     * @return 是否保存成功
     */
    boolean saveOrderItems(String orderId, List<OrderItem> orderItems);

    /**
     * 获取订单详情列表
     *
     * @param orderId 订单ID
     * @return 订单详情列表
     */
    List<OrderItem> getOrderItems(String orderId);
} 