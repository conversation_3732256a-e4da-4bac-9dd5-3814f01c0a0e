package org.jeecg.modules.api.inz_after_sale.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.inz_after_sale.entity.InzAfterSale;

import java.util.Map;

/**
 * @Description: 商品售后表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
public interface IInzAfterSaleService extends IService<InzAfterSale> {
    /**
     * 根据店铺ID查询售后信息
     */
    IPage<InzAfterSale> queryAfterSaleByStoreId(Page<InzAfterSale> page, 
                                              InzAfterSale afterSale, 
                                              String storeId,
                                              Map<String, String[]> parameterMap);
    
    /**
     * 根据用户ID查询售后信息
     */
    IPage<InzAfterSale> queryAfterSaleByUserId(Page<InzAfterSale> page, 
                                             InzAfterSale afterSale, 
                                             String userId,
                                             Map<String, String[]> parameterMap);
                                             
    /**
     * 审核售后申请
     */
    boolean auditAfterSale(String id, Integer status, String handleNote);
    
    /**
     * 确认退款
     */
    boolean confirmRefund(String id, String refundNo);
    
    /**
     * 处理退款并创建店铺资金流水记录
     * 
     * @param id 售后ID
     * @param refundNo 退款流水号
     * @param operator 操作人
     * @return 是否处理成功
     */
    boolean processRefundWithTransaction(String id, String refundNo, String operator);
}