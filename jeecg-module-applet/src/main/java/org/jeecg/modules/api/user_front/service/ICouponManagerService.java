package org.jeecg.modules.api.user_front.service;

import java.math.BigDecimal;

/**
 * @Description: 优惠券管理服务接口
 * 用于处理跨越CouponService和UserCouponService的业务逻辑，
 * 解决循环依赖问题
 * @Author: jeecg-boot
 * @Date: 2024-06-12
 * @Version: V1.0
 */
public interface ICouponManagerService {
    
    /**
     * 领取优惠券
     *
     * @param couponId 优惠券ID
     * @param userId 用户ID
     * @return 是否领取成功
     */
    boolean claimCoupon(String couponId, String userId);
    
    /**
     * 使用优惠券
     *
     * @param couponId 优惠券ID
     * @param userId 用户ID
     * @param orderId 订单ID
     * @param totalPrice 订单金额
     * @return 优惠后金额
     */
    BigDecimal useCoupon(String couponId, String userId, String orderId, BigDecimal totalPrice);
    
    /**
     * 检查优惠券是否可用
     *
     * @param couponId 优惠券ID
     * @param userId 用户ID
     * @param totalPrice 订单金额
     * @return 是否可用
     */
    boolean checkCouponAvailable(String couponId, String userId, BigDecimal totalPrice);
} 