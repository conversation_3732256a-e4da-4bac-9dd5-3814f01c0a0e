package org.jeecg.modules.api.user_front.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.api.user_front.entity.UserAddress;
import org.jeecg.modules.api.user_front.service.IUserAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: 用户地址接口
 * @Author: jeecg-boot
 * @Date: 2024-01-24
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "用户地址")
@RestController
@RequestMapping("/api/user/address")
public class UserAddressController {

    @Autowired
    private IUserAddressService userAddressService;

    @AutoLog(value = "用户地址-列表")
    @ApiOperation(value = "用户地址-列表", notes = "获取用户的所有地址")
    @GetMapping("/list")
    public Result<List<UserAddress>> list() {
        String userId = CommonUtils.getUserIdByToken();
        List<UserAddress> list = userAddressService.getUserAddressList(userId);
        return Result.OK(list);
    }

    @AutoLog(value = "用户地址-添加")
    @ApiOperation(value = "用户地址-添加", notes = "添加新地址")
    @PostMapping("/add")
    public Result<UserAddress> add(@RequestBody UserAddress userAddress) {
        String userId = CommonUtils.getUserIdByToken();
        userAddress.setUserId(userId);
        
        // 如果是第一个地址，自动设为默认地址
        if (userAddressService.count() == 0) {
            userAddress.setIsDefault(1);
        } else if (userAddress.getIsDefault() == null) {
            userAddress.setIsDefault(0);
        }
        
        userAddressService.save(userAddress);
        return Result.OK(userAddress);
    }

    @AutoLog(value = "用户地址-编辑")
    @ApiOperation(value = "用户地址-编辑", notes = "编辑地址")
    @PutMapping("/edit")
    public Result<UserAddress> edit(@RequestBody UserAddress userAddress) {
        String userId = CommonUtils.getUserIdByToken();
        
        // 验证地址归属
        UserAddress originalAddress = userAddressService.getById(userAddress.getId());
        if (originalAddress == null || !originalAddress.getUserId().equals(userId)) {
            return Result.error("无权限修改此地址");
        }
        
        userAddress.setUserId(userId);
        userAddressService.updateById(userAddress);
        return Result.OK(userAddress);
    }

    @AutoLog(value = "用户地址-通过id删除")
    @ApiOperation(value = "用户地址-通过id删除", notes = "删除地址")
    @PostMapping("/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        String userId = CommonUtils.getUserIdByToken();
        
        // 验证地址归属
        UserAddress address = userAddressService.getById(id);
        if (address == null || !address.getUserId().equals(userId)) {
            return Result.error("无权限删除此地址");
        }
        
        userAddressService.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog(value = "用户地址-设为默认")
    @ApiOperation(value = "用户地址-设为默认", notes = "设置默认地址")
    @PostMapping("/setDefault")
    public Result<?> setDefault(@RequestParam(name = "id") String id) {
        String userId = CommonUtils.getUserIdByToken();
        
        // 验证地址归属
        UserAddress address = userAddressService.getById(id);
        if (address == null || !address.getUserId().equals(userId)) {
            return Result.error("无权限操作此地址");
        }
        
        boolean success = userAddressService.setDefaultAddress(userId, id);
        return success ? Result.OK("设置成功!") : Result.error("设置失败!");
    }

    @AutoLog(value = "用户地址-获取默认地址")
    @ApiOperation(value = "用户地址-获取默认地址", notes = "获取用户默认地址")
    @GetMapping("/default")
    public Result<UserAddress> getDefaultAddress() {
        String userId = CommonUtils.getUserIdByToken();
        UserAddress defaultAddress = userAddressService.getDefaultAddress(userId);
        return Result.OK(defaultAddress);
    }

    @AutoLog(value = "用户地址-通过id查询")
    @ApiOperation(value = "用户地址-通过id查询", notes = "获取地址详情")
    @GetMapping("/detail")
    public Result<UserAddress> detail(@RequestParam(name = "id") String id) {
        String userId = CommonUtils.getUserIdByToken();
        
        UserAddress address = userAddressService.getById(id);
        if (address == null || !address.getUserId().equals(userId)) {
            return Result.error("无权限查看此地址");
        }
        return Result.OK(address);
    }
} 