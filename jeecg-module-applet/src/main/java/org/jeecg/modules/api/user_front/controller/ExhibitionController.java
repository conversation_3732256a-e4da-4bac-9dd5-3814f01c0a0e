package org.jeecg.modules.api.user_front.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.api.user_front.entity.Exhibition;
import org.jeecg.modules.api.user_front.service.IExhibitionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 展馆接口
 * @Author: jeecg-boot
 * @Date: 2024-06-12
 * @Version: V1.0
 */
@Api(tags="我的展馆")
@RestController
@RequestMapping("/api/user/exhibition")
@Slf4j
public class ExhibitionController {

    @Autowired
    private IExhibitionService exhibitionService;

    @AutoLog(value = "展馆-创建展馆")
    @ApiOperation(value="创建展馆", notes="创建展馆")
    @PostMapping(value = "/create")
    public Result<Map<String, Object>> createExhibition(
            @RequestParam String name,
            @RequestParam(required=false) String description,
            @RequestParam(required=false) String coverImage,
            @RequestParam(required=false, defaultValue="0") Integer status,
            HttpServletRequest req) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            String exhibitionId = exhibitionService.createExhibition(userId, name, description, coverImage, status);
            
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("exhibitionId", exhibitionId);
            
            return Result.OK("创建展馆成功", resultMap);
        } catch (Exception e) {
            log.error("创建展馆失败", e);
            return Result.error("创建展馆失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "展馆-更新展馆")
    @ApiOperation(value="更新展馆", notes="更新展馆")
    @PostMapping(value = "/update")
    public Result<Map<String, Object>> updateExhibition(
            @RequestParam String exhibitionId,
            @RequestParam(required=false) String name,
            @RequestParam(required=false) String description,
            @RequestParam(required=false) String coverImage,
            @RequestParam(required=false) Integer status,
            HttpServletRequest req) {
        try {
            boolean success = exhibitionService.updateExhibition(exhibitionId, name, description, coverImage, status);
            
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("exhibitionId", exhibitionId);
            resultMap.put("updated", success);
            
            return Result.OK("更新展馆成功", resultMap);
        } catch (Exception e) {
            log.error("更新展馆失败", e);
            return Result.error("更新展馆失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "展馆-删除展馆")
    @ApiOperation(value="删除展馆", notes="删除展馆")
    @PostMapping(value = "/delete")
    public Result<Map<String, Object>> deleteExhibition(
            @RequestParam String exhibitionId,
            HttpServletRequest req) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            boolean success = exhibitionService.deleteExhibition(exhibitionId, userId);
            
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("exhibitionId", exhibitionId);
            resultMap.put("deleted", success);
            
            return Result.OK("删除展馆成功", resultMap);
        } catch (Exception e) {
            log.error("删除展馆失败", e);
            return Result.error("删除展馆失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "展馆-获取展馆详情")
    @ApiOperation(value="获取展馆详情", notes="获取展馆详情")
    @GetMapping(value = "/detail")
    public Result<Exhibition> getExhibitionDetail(
            @RequestParam String exhibitionId,
            HttpServletRequest req) {
        try {
            Exhibition exhibition = exhibitionService.getExhibitionDetail(exhibitionId);
            if (exhibition == null) {
                return Result.error("展馆不存在");
            }
            return Result.OK("获取展馆详情成功", exhibition);
        } catch (Exception e) {
            log.error("获取展馆详情失败", e);
            return Result.error("获取展馆详情失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "展馆-获取我的展馆列表")
    @ApiOperation(value="获取我的展馆列表", notes="获取我的展馆列表")
    @GetMapping(value = "/my")
    public Result<IPage<Exhibition>> getMyExhibitions(
            @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
            @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
            HttpServletRequest req) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            Page<Exhibition> page = new Page<>(pageNo, pageSize);
            IPage<Exhibition> pageList = exhibitionService.getUserExhibitions(page, userId);
            return Result.OK("获取我的展馆列表成功", pageList);
        } catch (Exception e) {
            log.error("获取我的展馆列表失败", e);
            return Result.error("获取我的展馆列表失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "展馆-获取公开展馆列表")
    @ApiOperation(value="获取公开展馆列表", notes="获取公开展馆列表")
    @GetMapping(value = "/public")
    public Result<IPage<Exhibition>> getPublicExhibitions(
            @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
            @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
            HttpServletRequest req) {
        try {
            Page<Exhibition> page = new Page<>(pageNo, pageSize);
            IPage<Exhibition> pageList = exhibitionService.getPublicExhibitions(page);
            return Result.OK("获取公开展馆列表成功", pageList);
        } catch (Exception e) {
            log.error("获取公开展馆列表失败", e);
            return Result.error("获取公开展馆列表失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "展馆-添加卡片")
    @ApiOperation(value="向展馆添加卡片", notes="向展馆添加卡片")
    @PostMapping(value = "/addCard")
    public Result<Map<String, Object>> addCard(
            @RequestParam String exhibitionId,
            @RequestParam String cardId,
            @RequestParam String cardName,
            @RequestParam String cardImage,
            @RequestParam String cardGrade,
            @RequestParam(required=false) String team,
            HttpServletRequest req) {
        try {
            boolean success = exhibitionService.addCard(exhibitionId, cardId, cardName, cardImage, cardGrade, team);
            
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("exhibitionId", exhibitionId);
            resultMap.put("cardId", cardId);
            resultMap.put("added", success);
            
            return Result.OK("添加卡片成功", resultMap);
        } catch (Exception e) {
            log.error("添加卡片失败", e);
            return Result.error("添加卡片失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "展馆-移除卡片")
    @ApiOperation(value="从展馆移除卡片", notes="从展馆移除卡片")
    @PostMapping(value = "/removeCard")
    public Result<Map<String, Object>> removeCard(
            @RequestParam String exhibitionId,
            @RequestParam String cardId,
            HttpServletRequest req) {
        try {
            boolean success = exhibitionService.removeCard(exhibitionId, cardId);
            
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("exhibitionId", exhibitionId);
            resultMap.put("cardId", cardId);
            resultMap.put("removed", success);
            
            return Result.OK("移除卡片成功", resultMap);
        } catch (Exception e) {
            log.error("移除卡片失败", e);
            return Result.error("移除卡片失败: " + e.getMessage());
        }
    }
} 