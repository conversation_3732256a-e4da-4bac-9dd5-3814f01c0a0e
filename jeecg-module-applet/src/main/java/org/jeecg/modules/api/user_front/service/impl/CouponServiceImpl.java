package org.jeecg.modules.api.user_front.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.modules.api.user_front.entity.Coupon;
import org.jeecg.modules.api.user_front.entity.CouponCode;
import org.jeecg.modules.api.user_front.entity.UserCoupon;
import org.jeecg.modules.api.user_front.mapper.CouponCodeMapper;
import org.jeecg.modules.api.user_front.mapper.CouponMapper;
import org.jeecg.modules.api.user_front.mapper.UserCouponMapper;
import org.jeecg.modules.api.user_front.service.ICouponService;
import org.jeecg.modules.api.user_front.service.ICouponValidationService;
import org.jeecg.modules.api.user_front.vo.CouponVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.dao.DuplicateKeyException;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Collectors;

/**
 * @Description: 优惠券服务实现类
 * @Author: jeecg-boot
 * @Date: 2024-01-24
 * @Version: V1.0
 */
@Service
@Slf4j
public class CouponServiceImpl extends ServiceImpl<CouponMapper, Coupon> implements ICouponService {

    @Autowired
    private UserCouponMapper userCouponMapper;

    @Autowired
    private CouponCodeMapper couponCodeMapper;

    @Override
    public IPage<CouponVO> getAvailableCoupons(Page<CouponVO> page, String storeId, String userId) {
        return baseMapper.getAvailableCoupons(page, storeId, userId);
    }

    @Override
    public IPage<CouponVO> getMyCoupons(Page<CouponVO> page, String userId, Integer status, Integer subCategory) {
        return baseMapper.getMyCoupons(page, userId, status, subCategory);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public boolean receiveCoupon(String couponId, String userId) {
        // 调用新方法进行处理，保持向后兼容
        Map<String, Object> result = receiveSingleCoupon(couponId, userId);
        return (boolean) result.getOrDefault("success", false);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> receiveSingleCoupon(String couponId, String userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (StringUtils.isAnyBlank(couponId, userId)) {
                result.put("success", false);
                result.put("message", "参数不能为空");
                return result;
            }
    
            // 1. 查询优惠券信息
            Coupon coupon = getById(couponId);
            if (coupon == null) {
                result.put("success", false);
                result.put("message", "优惠券不存在");
                return result;
            }
    
                        // 2. 检查优惠券是否有效
            Date now = new Date();
            if (now.before(coupon.getStartTime()) || now.after(coupon.getEndTime())) {
                result.put("success", false);
                result.put("message", "优惠券不在有效期内");
                return result;
            }

            // 3. 检查优惠券数量和状态
            if (coupon.getRemainingQuantity() <= 0 || coupon.getStatus() == 0) {
                result.put("success", false);
                result.put("message", coupon.getStatus() == 0 ? "优惠券已禁用" : "优惠券已被领完");
                return result;
            }
    
            // 4. 使用数据库唯一约束防止重复领取，先尝试创建记录
            try {
                UserCoupon userCoupon = new UserCoupon();
                userCoupon.setUserId(userId);
                userCoupon.setCouponId(couponId);
                userCoupon.setStatus(0); // 未使用
                userCoupon.setCreateTime(now);
                userCoupon.setClaimTime(now); // 设置领取时间
                userCoupon.setIsReceived(1);

                // 直接插入，如果违反唯一约束会抛出异常
                userCouponMapper.insert(userCoupon);
                log.info("用户优惠券记录创建成功: userId={}, couponId={}", userId, couponId);

            } catch (DuplicateKeyException e) {
                // 捕获重复键异常，说明用户已领取过
                log.warn("用户重复领取优惠券: userId={}, couponId={}", userId, couponId);
                result.put("success", false);
                result.put("message", "您已领取过该优惠券");
                return result;
            } catch (Exception e) {
                log.error("创建用户优惠券记录失败: userId={}, couponId={}", userId, couponId, e);
                result.put("success", false);
                result.put("message", "领取优惠券失败，请重试");
                return result;
            }

            // 5. 更新优惠券剩余数量
            coupon.setRemainingQuantity(coupon.getRemainingQuantity() - 1);
            updateById(coupon);
            log.info("优惠券剩余数量更新成功: couponId={}, remainingQuantity={}", couponId, coupon.getRemainingQuantity());
            
            // 7. 构建返回结果
            List<Coupon> successList = new ArrayList<>();
            successList.add(coupon);
            
            result.put("success", true);
            result.put("message", "领取优惠券成功");
            result.put("successCount", 1);
            result.put("failedCount", 0);
            result.put("successList", successList);
            result.put("coupon", coupon);

            log.info("优惠券领取成功: userId={}, couponId={}", userId, couponId);
            return result;

        } catch (Exception e) {
            log.error("领取优惠券失败: couponId={}, userId={}", couponId, userId, e);
            result.put("success", false);
            result.put("message", "领取优惠券失败: " + e.getMessage());
            return result;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CouponVO redeemCouponByCode(String code, String userId) {
        if (StringUtils.isAnyBlank(code, userId)) {
            throw new JeecgBootException("参数不能为空");
        }
        
        // 1. 查询兑换码
        CouponCode couponCode = couponCodeMapper.selectByCodeUnused(code);
        if (couponCode == null) {
            throw new JeecgBootException("兑换码不存在或已被使用");
        }
        
        // 2. 查询优惠券信息
        Coupon coupon = getById(couponCode.getCouponId());
        if (coupon == null) {
            throw new JeecgBootException("优惠券不存在");
        }
        
        // 3. 检查优惠券是否有效
        Date now = new Date();
        if (now.before(coupon.getStartTime()) || now.after(coupon.getEndTime())) {
            throw new JeecgBootException("优惠券不在有效期内");
        }
        
        // 3.1 检查优惠券是否已禁用
        if (coupon.getStatus() == 0) {
            throw new JeecgBootException("优惠券已禁用");
        }
        
        // 4. 检查用户是否已领取过该优惠券
        UserCoupon existUserCoupon = userCouponMapper.selectOne(
            new QueryWrapper<UserCoupon>()
                .eq("user_id", userId)
                .eq("coupon_id", coupon.getId())
        );
        if (existUserCoupon != null) {
            throw new JeecgBootException("您已领取过该优惠券");
        }
        
        // 5. 创建用户优惠券记录
        UserCoupon userCoupon = new UserCoupon();
        userCoupon.setUserId(userId);
        userCoupon.setCouponId(coupon.getId());
        userCoupon.setStatus(0); // 未使用
        userCoupon.setCreateTime(now);
        userCoupon.setClaimTime(now); // 设置领取时间
        userCoupon.setIsReceived(1); // 设置为已领取
        userCouponMapper.insert(userCoupon);
        
        // 6. 标记兑换码为已使用
        couponCodeMapper.markAsUsed(couponCode.getId(), userId);
        
        // 7. 构造返回结果
        CouponVO couponVO = new CouponVO();
        BeanUtils.copyProperties(coupon, couponVO);
        couponVO.setIsReceived(true);
        couponVO.setCouponStatus(0); // 未使用
        
        return couponVO;
    }
    
    @Override
    public Map<String, Object> getStoreCouponsWithCount(String storeId, String mainCategory, String subCategory) {
        Map<String, Object> result = new HashMap<>();
        
        // 1. 查询满足主分类和子分类条件的优惠券
        QueryWrapper<Coupon> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("store_id", storeId);
        
        if (mainCategory != null && !mainCategory.isEmpty()) {
            queryWrapper.eq("main_category", mainCategory);
        }
        
        if (subCategory != null && !subCategory.isEmpty()) {
            queryWrapper.eq("sub_category", subCategory);
        }
        
        // 确保只返回有效期内且还有剩余数量的优惠券
        Date now = new Date();
        queryWrapper.le("start_time", now)
                   .ge("end_time", now)
                   .gt("remaining_quantity", 0);
        
        List<Coupon> coupons = list(queryWrapper);
        
        // 2. 按子分类对优惠券进行分组
        Map<String, List<Coupon>> categoryGrouped = new HashMap<>();
        
        // 添加全部分类的优惠券
        categoryGrouped.put("all", coupons);
        
        // 如果未指定子分类，按子分类分组
        if (subCategory == null || subCategory.isEmpty()) {
            for (Coupon coupon : coupons) {
                String category = coupon.getSubCategory();
                if (category != null && !category.isEmpty()) {
                    if (!categoryGrouped.containsKey(category)) {
                        categoryGrouped.put(category, new ArrayList<>());
                    }
                    categoryGrouped.get(category).add(coupon);
                }
            }
        }
        
        // 3. 统计每个分类的一键领取数量
        Map<String, Integer> categoryCounts = new HashMap<>();
        for (Map.Entry<String, List<Coupon>> entry : categoryGrouped.entrySet()) {
            categoryCounts.put(entry.getKey(), entry.getValue().size());
        }
        
        // 4. 构建返回结果
        result.put("coupons", coupons);
        result.put("categoryGrouped", categoryGrouped);
        result.put("categoryCounts", categoryCounts);
        result.put("totalCount", coupons.size());
        
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchReceiveCoupons(String storeId, String userId, String mainCategory, String subCategory) {
        Map<String, Object> result = new HashMap<>();
        List<Coupon> successList = new ArrayList<>();
        List<Coupon> failedList = new ArrayList<>();
        
        // 1. 获取符合条件的优惠券
        Map<String, Object> storeCouponsInfo = getStoreCouponsWithCount(storeId, mainCategory, subCategory);
        List<Coupon> coupons = (List<Coupon>) storeCouponsInfo.get("coupons");
        
        if (coupons == null || coupons.isEmpty()) {
            result.put("success", false);
            result.put("message", "没有可领取的优惠券");
            return result;
        }
        
        // 2. 查询用户已领取的优惠券列表
        QueryWrapper<UserCoupon> userCouponQuery = new QueryWrapper<>();
        userCouponQuery.eq("user_id", userId);
        List<UserCoupon> userCoupons = userCouponMapper.selectList(userCouponQuery);
        
        // 用户已领取的优惠券ID集合
        Set<String> userCouponIds = new HashSet<>();
        for (UserCoupon userCoupon : userCoupons) {
            userCouponIds.add(userCoupon.getCouponId());
        }
        
        // 3. 批量领取优惠券
        Date now = new Date();
        int successCount = 0;
        int failedCount = 0;
        
        for (Coupon coupon : coupons) {
            try {
                // 检查用户是否已领取该优惠券
                if (userCouponIds.contains(coupon.getId())) {
                    failedList.add(coupon);
                    failedCount++;
                    continue;
                }
                
                // 检查优惠券是否有效
                if (now.before(coupon.getStartTime()) || now.after(coupon.getEndTime())) {
                    failedList.add(coupon);
                    failedCount++;
                    continue;
                }
                
                // 检查优惠券数量
                if (coupon.getRemainingQuantity() <= 0) {
                    failedList.add(coupon);
                    failedCount++;
                    continue;
                }
                
                // 创建用户优惠券记录
                UserCoupon userCoupon = new UserCoupon();
                userCoupon.setUserId(userId);
                userCoupon.setCouponId(coupon.getId());
                userCoupon.setStatus(0); // 未使用
                userCoupon.setCreateTime(now);
                userCoupon.setClaimTime(now); // 设置领取时间
                userCouponMapper.insert(userCoupon);
                
                // 更新优惠券剩余数量
                coupon.setRemainingQuantity(coupon.getRemainingQuantity() - 1);
                updateById(coupon);
                
                successList.add(coupon);
                successCount++;
                
                // 添加到已领取集合，防止重复领取
                userCouponIds.add(coupon.getId());
                
            } catch (Exception e) {
                log.error("领取优惠券失败: " + coupon.getId(), e);
                failedList.add(coupon);
                failedCount++;
            }
        }
        
        // 4. 构建返回结果
        result.put("success", true);
        result.put("message", "成功领取 " + successCount + " 张优惠券，失败 " + failedCount + " 张");
        result.put("successCount", successCount);
        result.put("failedCount", failedCount);
        result.put("successList", successList);
        result.put("failedList", failedList);
        
        return result;
    }

    /**
     * 判断优惠券是否被当前用户领取过
     *
     * @param couponId 优惠券ID
     * @param userId 用户ID
     * @return true-已领取，false-未领取
     */
    @Override
    public boolean isCouponClaimedByUser(String couponId, String userId) {
        if (StringUtils.isAnyBlank(couponId, userId)) {
            return false;
        }

        UserCoupon userCoupon = userCouponMapper.selectOne(
            new QueryWrapper<UserCoupon>()
                .eq("user_id", userId)
                .eq("coupon_id", couponId)
        );

        return userCoupon != null;
    }

    /**
     * 批量判断优惠券是否被当前用户领取过
     *
     * @param couponIds 优惠券ID列表
     * @param userId 用户ID
     * @return Map<优惠券ID, 是否已领取>
     */
    @Override
    public Map<String, Boolean> batchCheckCouponsClaimed(List<String> couponIds, String userId) {
        Map<String, Boolean> result = new HashMap<>();

        if (couponIds == null || couponIds.isEmpty() || StringUtils.isBlank(userId)) {
            return result;
        }

        // 查询用户已领取的优惠券
        List<UserCoupon> userCoupons = userCouponMapper.selectList(
            new QueryWrapper<UserCoupon>()
                .eq("user_id", userId)
                .in("coupon_id", couponIds)
        );

        // 构建已领取的优惠券ID集合
        Set<String> claimedCouponIds = userCoupons.stream()
            .map(UserCoupon::getCouponId)
            .collect(Collectors.toSet());

        // 为每个优惠券设置领取状态
        for (String couponId : couponIds) {
            result.put(couponId, claimedCouponIds.contains(couponId));
        }

        return result;
    }

    /**
     * 为优惠券列表设置领取状态
     *
     * @param coupons 优惠券列表
     * @param userId 用户ID
     */
    @Override
    public void setCouponClaimStatus(List<Coupon> coupons, String userId) {
        if (coupons == null || coupons.isEmpty()) {
            return;
        }

        if (StringUtils.isBlank(userId)) {
            // 用户未登录，所有优惠券都设为未领取
            coupons.forEach(coupon -> coupon.setIsReceived(false));
            return;
        }

        // 获取优惠券ID列表
        List<String> couponIds = coupons.stream()
            .map(Coupon::getId)
            .collect(Collectors.toList());

        // 批量检查领取状态
        Map<String, Boolean> claimStatusMap = batchCheckCouponsClaimed(couponIds, userId);

        // 为每个优惠券设置领取状态
        coupons.forEach(coupon ->
            coupon.setIsReceived(claimStatusMap.getOrDefault(coupon.getId(), false))
        );
    }

    /**
     * 为CouponVO列表设置领取状态（Integer类型）
     *
     * @param coupons 优惠券VO列表
     * @param userId 用户ID
     */
    public void setCouponVOClaimStatus(List<CouponVO> coupons, String userId) {
        if (coupons == null || coupons.isEmpty()) {
            return;
        }

        if (StringUtils.isBlank(userId)) {
            // 用户未登录，所有优惠券都设为未领取
            coupons.forEach(coupon -> coupon.setIsReceivedInt(0));
            return;
        }

        // 获取优惠券ID列表
        List<String> couponIds = coupons.stream()
            .map(CouponVO::getId)
            .collect(Collectors.toList());

        // 批量检查领取状态
        Map<String, Boolean> claimStatusMap = batchCheckCouponsClaimed(couponIds, userId);

        // 为每个优惠券设置领取状态
        coupons.forEach(coupon -> {
            Boolean claimed = claimStatusMap.getOrDefault(coupon.getId(), false);
            coupon.setIsReceivedInt(claimed ? 1 : 0);
        });
    }

    /**
     * 通用查询优惠券列表（包含用户领取状态）
     */
    @Override
    public IPage<CouponVO> getCouponsWithClaimStatus(Page<CouponVO> page,
                                                    String storeId,
                                                    String userId,
                                                    Integer status,
                                                    Boolean onlyAvailable,
                                                    String mainCategory,
                                                    String subCategory,
                                                    String orderBy) {
        return baseMapper.getCouponsWithClaimStatus(page, storeId, userId, status,
                                                   onlyAvailable, mainCategory, subCategory, orderBy);
    }

    /**
     * 查询店铺优惠券（包含用户领取状态）
     */
    @Override
    public List<CouponVO> getStoreCouponsWithClaimStatus(String storeId,
                                                        String userId,
                                                        String mainCategory,
                                                        String subCategory) {
        return baseMapper.getStoreCouponsWithClaimStatus(storeId, userId, mainCategory, subCategory);
    }
}