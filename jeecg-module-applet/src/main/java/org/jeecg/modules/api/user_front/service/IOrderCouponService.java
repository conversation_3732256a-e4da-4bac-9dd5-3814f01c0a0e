package org.jeecg.modules.api.user_front.service;

import java.math.BigDecimal;

/**
 * @Description: 订单优惠券服务接口
 * 这个服务负责订单和优惠券之间的交互，消除循环依赖
 * @Author: jeecg-boot
 * @Date: 2024-01-24
 * @Version: V1.0
 */
public interface IOrderCouponService {

    /**
     * 验证优惠券是否可用于当前订单
     *
     * @param userId 用户ID
     * @param couponId 优惠券ID
     * @param totalPrice 订单金额
     * @return 是否可用
     */
    boolean validateCoupon(String userId, String couponId, BigDecimal totalPrice);
    
    /**
     * 计算优惠金额
     *
     * @param couponId 优惠券ID
     * @param totalPrice 订单金额
     * @return 优惠金额
     */
    BigDecimal calculateDiscount(String couponId, BigDecimal totalPrice);
    
    /**
     * 使用优惠券
     *
     * @param userId 用户ID
     * @param couponId 优惠券ID
     * @param orderId 订单ID
     * @return 是否成功
     */
    boolean useCoupon(String userId, String couponId, String orderId);
} 