package org.jeecg.modules.api.user_front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.user_front.entity.UserCoupon;

import java.math.BigDecimal;

/**
 * @Description: 用户优惠券服务接口
 * @Author: jeecg-boot
 * @Date: 2024-01-24
 * @Version: V1.0
 */
public interface IUserCouponService extends IService<UserCoupon> {

    /**
     * 创建用户获取优惠券记录
     *
     * @param userId 用户ID
     * @param couponId 优惠券ID
     * @return 是否创建成功
     */
    boolean createUserCoupon(String userId, String couponId);

    /**
     * 更新优惠券使用状态
     *
     * @param userId 用户ID
     * @param couponId 优惠券ID
     * @param orderId 订单ID
     * @return 是否更新成功
     */
    boolean updateCouponStatus(String userId, String couponId, String orderId);

    /**
     * 检查用户是否已领取优惠券
     *
     * @param userId 用户ID
     * @param couponId 优惠券ID
     * @return 是否已领取
     */
    boolean hasClaimed(String userId, String couponId);

    /**
     * 获取用户优惠券
     *
     * @param userId 用户ID
     * @param couponId 优惠券ID
     * @return 用户优惠券
     */
    UserCoupon getUserCoupon(String userId, String couponId);
    
    /**
     * 获取用户有效优惠券数量
     *
     * @param userId 用户ID
     * @return 有效优惠券数量
     */
    int getValidCouponCount(String userId);
    
    /**
     * 检查优惠券是否可用
     *
     * @param userId 用户ID
     * @param couponId 优惠券ID
     * @param totalPrice 订单金额
     * @return 是否可用
     */
} 