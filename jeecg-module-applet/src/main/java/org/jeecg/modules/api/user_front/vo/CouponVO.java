package org.jeecg.modules.api.user_front.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.modules.api.user_front.entity.Coupon;

import java.io.Serializable;

/**
 * @Description: 优惠券视图对象
 * @Author: jeecg-boot
 * @Date: 2024-01-24
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="优惠券视图对象", description="优惠券视图对象")
public class CouponVO extends Coupon implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    // 移除重复的isReceived字段定义，直接使用父类的Boolean类型
    // private Integer isReceived; // 删除这行
    
    // 修改claimed字段的setter，用于SQL结果映射
    public void setClaimed(<PERSON><PERSON><PERSON> claimed) {
        // 直接设置父类的Boolean字段
        this.setIsReceived(claimed);
    }
    
    public Boolean getClaimed() {
        return this.getIsReceived();
    }
    
    // 如果需要Integer类型的便捷方法，可以添加转换方法
    public Integer getIsReceivedAsInteger() {
        Boolean received = this.getIsReceived();
        return received != null && received ? 1 : 0;
    }
    
    public void setIsReceivedFromInteger(Integer isReceived) {
        this.setIsReceived(isReceived != null && isReceived == 1);
    }
    
    @ApiModelProperty(value = "使用状态（0：未使用，1：已使用，2：已过期）")
    private Integer couponStatus;

    @ApiModelProperty(value = "剩余有效期（天）")
    private Integer daysRemaining;

    @ApiModelProperty(value = "优惠券类型（1：固定金额，2：折扣）")
    private String typeName;
    
    /**
     * 获取使用状态，用于保持与前端的兼容性
     * @return 使用状态（0：未使用，1：已使用，2：已过期）
     */
    public Integer getStatus() {
        return this.couponStatus;
    }
}