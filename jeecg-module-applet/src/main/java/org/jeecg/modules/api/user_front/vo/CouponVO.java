package org.jeecg.modules.api.user_front.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.modules.api.user_front.entity.Coupon;

import java.io.Serializable;

/**
 * @Description: 优惠券视图对象
 * @Author: jeecg-boot
 * @Date: 2024-01-24
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="优惠券视图对象", description="优惠券视图对象")
public class CouponVO extends Coupon implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "是否已领取（1：已领取，0：未领取）")
    private Integer isReceived;

    // 兼容性方法：用于SQL结果映射
    public void setClaimed(<PERSON><PERSON><PERSON> claimed) {
        this.isReceived = (claimed != null && claimed) ? 1 : 0;
    }

    public Boolean getClaimed() {
        return this.isReceived != null && this.isReceived == 1;
    }

    // 重写父类的isReceived方法，确保返回Integer类型
    @Override
    public Boolean getIsReceived() {
        return this.isReceived != null && this.isReceived == 1;
    }

    @Override
    public void setIsReceived(Boolean received) {
        this.isReceived = (received != null && received) ? 1 : 0;
    }

    // 新增：直接获取Integer类型的isReceived
    public Integer getIsReceivedInt() {
        return this.isReceived != null ? this.isReceived : 0;
    }

    public void setIsReceivedInt(Integer isReceived) {
        this.isReceived = isReceived != null ? isReceived : 0;
    }
    
    @ApiModelProperty(value = "使用状态（0：未使用，1：已使用，2：已过期）")
    private Integer couponStatus;

    @ApiModelProperty(value = "剩余有效期（天）")
    private Integer daysRemaining;

    @ApiModelProperty(value = "优惠券类型（1：固定金额，2：折扣）")
    private String typeName;
    
    /**
     * 获取使用状态，用于保持与前端的兼容性
     * @return 使用状态（0：未使用，1：已使用，2：已过期）
     */
    public Integer getStatus() {
        return this.couponStatus;
    }
}